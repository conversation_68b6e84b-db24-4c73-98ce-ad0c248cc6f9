﻿//*****************************************************************************
//*  Program System    : LVS-Leitstand
//*  Module Name       : LVSTopologieDLG
//*  Author            : <PERSON> / Common Solutions
//*  Date of creation  : 20.07.2004
//*****************************************************************************
// $Archive: /storelogix/LVS/FrontEnd/XE11/LVSTopologieDLG.pas $
// $Revision: 168 $
// $Modtime: 18.12.23 17:05 $
// $Author: Stefan.graf $
//*****************************************************************************
//*  Description       : Bearbeiten der Lagertopologie,
//*                      <PERSON><PERSON>, Lagerbereiche und Lagerplätze anlegen und verändern.
//*****************************************************************************
unit LVSTopologieDLG;

{$i compilers.inc}

interface

uses
  Windows, Messages, SysUtils, Variants, Classes, Graphics, Controls, Forms,
  Dialogs, Grids, DB, ADODB, ComCtrls, StdCtrls, CompTranslate, DBGrids,
  SMDBGrid, DBGridPro, StringGridPro, ComboBoxPro, FrontendUtils, Menus, BarCodeScanner,
  InfoWinForm, MemDS, DBAccess, Ora, OraSmart, ExtCtrls, EditLagerBereichDLG;

type
  TLagerTopologieForm = class(TForm)
    PageControl1: TPageControl;
    Button1: TButton;
    LagerTabSheet: TTabSheet;
    LBTabSheet: TTabSheet;
    LPTabSheet: TTabSheet;
    ADOQuery1: TADOQuery;
    CompTranslateForm1: TCompTranslateForm;
    LagerNeuButton: TButton;
    LagerDelButton: TButton;
    LBNeuButton: TButton;
    LagerComboBox1: TComboBoxPro;
    LPDataSource: TDataSource;
    LBCopyButton: TButton;
    DelLBButton: TButton;
    LagerStringGrid: TStringGridPro;
    Label3: TLabel;
    Label4: TLabel;
    Label5: TLabel;
    KommTabSheet: TTabSheet;
    Label7: TLabel;
    LagerComboBox3: TComboBoxPro;
    Label8: TLabel;
    LBComboBox2: TComboBoxPro;
    LPListBox: TListBox;
    Label9: TLabel;
    AddKommLPButton: TButton;
    KommDBGrid: TDBGridPro;
    KommADOQuery: TADOQuery;
    KommDataSource: TDataSource;
    RemoveKommLPButton: TButton;
    LagerCfgButton: TButton;
    LagerCommButton: TButton;
    LocComboBox: TComboBoxPro;
    Label10: TLabel;
    TabSheet5: TTabSheet;
    LocStringGrid: TStringGridPro;
    NewLocButton: TButton;
    KommLPPrintButton: TButton;
    LagerMandButton: TButton;
    LBDBGrid: TDBGridPro;
    LBQuery: TADOQuery;
    LBDataSource: TDataSource;
    EditLBButton: TButton;
    LagerEditButton: TButton;
    ChangeLocButton: TButton;
    KommDBGridPopupMenu: TPopupMenu;
    LagerCopyButton: TButton;
    CopyLocButton: TButton;
    LagerPopupMenu: TPopupMenu;
    LagerCheckConfigMenuItem: TMenuItem;
    LBZoneDBGrid: TDBGridPro;
    Label11: TLabel;
    LBZoneNewButton: TButton;
    LBZOneEditButton: TButton;
    LBZoneDelButton: TButton;
    LBZoneDataSource: TDataSource;
    LBZoneQuery: TADOQuery;
    LBZoneDBGridPopupMenu: TPopupMenu;
    PrintLBZonenLabelMenuItem: TMenuItem;
    LPDBGridPopupMenu: TPopupMenu;
    ImportLPDataImportMenuItem: TMenuItem;
    N1: TMenuItem;
    LPCreateSameMenuItem: TMenuItem;
    ImportLPKommFolgeMenuItem: TMenuItem;
    ExportLPDataMenuItem: TMenuItem;
    N2: TMenuItem;
    LBDBGridPopupMenu: TPopupMenu;
    PrintLBLabelMenuItem: TMenuItem;
    Einlagerplatzanlegen1: TMenuItem;
    N3: TMenuItem;
    LPOraQuery: TSmartQuery;
    LagerCopyColMenuItem: TMenuItem;
    N4: TMenuItem;
    LagerColOptimalMenuItem: TMenuItem;
    LPArtikelPanel: TPanel;
    LPArtikelDBGrid: TDBGridPro;
    LPADOQuery: TADOQuery;
    LPArtikelDataSource: TDataSource;
    LPDatenPanel: TPanel;
    Label1: TLabel;
    LagerComboBox2: TComboBoxPro;
    Label2: TLabel;
    LBComboBox1: TComboBoxPro;
    DrawLPButton: TButton;
    NeuLPButton: TButton;
    NeuLPSerieButton: TButton;
    EditLPButton: TButton;
    ChangeLPLBButton: TButton;
    SperrenLPButton: TButton;
    FreigebenLPButton: TButton;
    ChangeTypenButton: TButton;
    PrintLPButton: TButton;
    RegalButton: TButton;
    KommFolgeButton: TButton;
    DelLPButton: TButton;
    LPDBGrid: TDBGridPro;
    Label6: TLabel;
    LPArtikelQuery: TSmartQuery;
    Label12: TLabel;
    LPTabSplitter: TSplitter;
    LPArAddButton: TButton;
    LPARDelButton: TButton;
    LPArtikelDBGridPopupMenu: TPopupMenu;
    LPARNachschubMenuItem: TMenuItem;
    N5: TMenuItem;
    LPNotUniqueMenuItem: TMenuItem;
    procedure LagerStringGridDblClick(Sender: TObject);
    procedure LagerNeuButtonClick(Sender: TObject);
    procedure LBTabSheetShow(Sender: TObject);
    procedure LagerComboBox1Change(Sender: TObject);
    procedure StringGrid2DblClick(Sender: TObject);
    procedure LBNeuButtonClick(Sender: TObject);
    procedure LPTabSheetShow(Sender: TObject);
    procedure LagerComboBox2Change(Sender: TObject);
    procedure LagerComboBox3Change(Sender: TObject);
    procedure LBComboBoxChange(Sender: TObject);
    procedure LBCopyButtonClick(Sender: TObject);
    procedure NeuLPSerieButtonClick(Sender: TObject);
    procedure StringGrid1KeyPress(Sender: TObject; var Key: Char);
    procedure StringGrid2KeyPress(Sender: TObject; var Key: Char);
    procedure FormCreate(Sender: TObject);
    procedure KommTabSheetShow(Sender: TObject);
    procedure AddKommLPButtonClick(Sender: TObject);
    procedure RemoveKommLPButtonClick(Sender: TObject);
    procedure FormShow(Sender: TObject);
    procedure LagerTabSheetResize(Sender: TObject);
    procedure LPDBGridDblClick(Sender: TObject);
    procedure NeuLPButtonClick(Sender: TObject);
    procedure LagerCfgButtonClick(Sender: TObject);
    procedure LagerCommButtonClick(Sender: TObject);
    procedure LocComboBoxChange(Sender: TObject);
    procedure LagerTabSheetShow(Sender: TObject);
    procedure TabSheet5Show(Sender: TObject);
    procedure TabSheet5Resize(Sender: TObject);
    procedure LocStringGridDblClick(Sender: TObject);
    procedure NewLocButtonClick(Sender: TObject);
    procedure LPListBoxDragOver(Sender, Source: TObject; X, Y: Integer;
      State: TDragState; var Accept: Boolean);
    procedure KommDBGridDragOver(Sender, Source: TObject; X, Y: Integer;
      State: TDragState; var Accept: Boolean);
    procedure KommDBGridDragDrop(Sender, Source: TObject; X, Y: Integer);
    procedure PrintLPButtonClick(Sender: TObject);
    procedure KommLPPrintButtonClick(Sender: TObject);
    procedure LPListBoxDrawItem(Control: TWinControl; Index: Integer;
      Rect: TRect; State: TOwnerDrawState);
    procedure ChangeTypenButtonClick(Sender: TObject);
    procedure DelLPButtonClick(Sender: TObject);
    procedure EditLPButtonClick(Sender: TObject);
    procedure FormClose(Sender: TObject; var Action: TCloseAction);
    procedure KommFolgeButtonClick(Sender: TObject);
    function LPDBGridColumnSort(Sender: TCustomDBGridPro;
      const ColumnName: String): String;
    procedure LagerMandButtonClick(Sender: TObject);
    procedure KommDBGridDblClick(Sender: TObject);
    procedure DrawLPButtonClick(Sender: TObject);
    procedure LPDataSourceDataChange(Sender: TObject; Field: TField);
    procedure FormKeyDown(Sender: TObject; var Key: Word;
      Shift: TShiftState);
    procedure PageControl1Changing(Sender: TObject;
      var AllowChange: Boolean);
    procedure DelLBButtonClick(Sender: TObject);
    procedure LBTabSheetResize(Sender: TObject);
    procedure LPTabSheetResize(Sender: TObject);
    procedure KommTabSheetResize(Sender: TObject);
    function KommDBGridColumnSort(Sender: TCustomDBGridPro;
      const ColumnName: string): string;
    procedure ChangeLPLBButtonClick(Sender: TObject);
    procedure SperrenLPButtonClick(Sender: TObject);
    procedure FreigebenLPButtonClick(Sender: TObject);
    procedure DBGridDrawColumnCell(Sender: TObject; const Rect: TRect;
      DataCol: Integer; Column: TColumn; State: TGridDrawState);
    procedure FormDestroy(Sender: TObject);
    procedure LagerCopyButtonClick(Sender: TObject);
    procedure CopyLocButtonClick(Sender: TObject);
    procedure LBDataSourceDataChange(Sender: TObject; Field: TField);
    procedure LagerStringGridClick(Sender: TObject);
    procedure LagerCheckConfigMenuItemClick(Sender: TObject);
    procedure LBZoneNewButtonClick(Sender: TObject);
    procedure LBZOneEditButtonClick(Sender: TObject);
    procedure LBZoneDataSourceDataChange(Sender: TObject; Field: TField);
    procedure PrintLBZonenLabelMenuItemClick(Sender: TObject);
    procedure LBZoneDBGridPopupMenuPopup(Sender: TObject);
    procedure ImportLPDataImportMenuItemClick(Sender: TObject);
    procedure DBGridHotTrackKeyPress(Sender: TObject; var Key: Char);
    procedure LBZoneDelButtonClick(Sender: TObject);
    procedure ImportLPKommFolgeMenuItemClick(Sender: TObject);
    procedure ExportLPDataMenuItemClick(Sender: TObject);
    procedure LPDBGridPopupMenuPopup(Sender: TObject);
    procedure CompTranslateForm1ChangeLanguage(Sender: TObject);
    procedure PrintLBLabelMenuItemClick(Sender: TObject);
    procedure Einlagerplatzanlegen1Click(Sender: TObject);
    procedure RegalButtonClick(Sender: TObject);
    procedure StringGridColOptimalMenuItemClick(Sender: TObject);
    procedure StringGridCopyColMenuItemClick(Sender: TObject);
    procedure LagerPopupMenuPopup(Sender: TObject);
    procedure LPArAddButtonClick(Sender: TObject);
    procedure LPARNachschubMenuItemClick(Sender: TObject);
    procedure LPARDelButtonClick(Sender: TObject);
    procedure LPNotUniqueMenuItemClick(Sender: TObject);
  private
    fAktLocationRef : Integer;
    fAktLagerRef    : Integer;
    fAktBereichRef  : Integer;

    fBereichTabIndex : Integer;

    fProgressForm   : TInfoWin;

    fLastLPNr       : Integer;
    fLastFeldStep   : Integer;
    fLastFeldStart  : Integer;
    fLastFeldEnd    : Integer;
    fLastFachStart  : Integer;
    fLastFachEnd    : Integer;
    fLastEbeneStep  : Integer;
    fLastEbeneStart : Integer;
    fLastEbeneEnd   : Integer;

    procedure ScannerErfassung(var Message: TMessage); message WM_USER + SCANNER_DETECT;

    function  GetLBArt       (const RefLB : Integer) : String;
    function  GetLBArtLPType (const RefLB : Integer) : Integer;

    function  LoadLPArten (const RefLager : Integer; LPComboBox : TComboBoxPro) : Integer;
    procedure UpdateBereicheComboBox (Sender: TObject; const LagerRef : Integer; LBComboBox : TComboBoxPro; const UpdateFlag : Boolean);
    procedure UpdateLPQuery (const RefLager, RefBereich : Integer);
    procedure UpdateKommQuery (const RefLager, RefBereich : Integer);
    procedure UpdateLagerGrid(Sender: TObject);
    function  KommLPAnlegen : Integer;

    function ChangeLagerDaten (const Ref : Integer) : Integer;

    function ChangeLP    (DBGrid : TDBGridPro; Daten : Pointer; InfoForm : TForm; var Option : String; var Done : Boolean; var Abort : Boolean) : Integer;
    function ChangeLPLB  (DBGrid : TDBGridPro; Daten : Pointer; InfoForm : TForm; var Option : String; var Done : Boolean; var Abort : Boolean) : Integer;
    function DeleteLP    (DBGrid : TDBGridPro; Daten : Pointer; InfoForm : TForm; var Option : String; var Done : Boolean; var Abort : Boolean) : Integer;
    function SperrenLP   (DBGrid : TDBGridPro; Daten : Pointer; InfoForm : TForm; var Option : String; var Done : Boolean; var Abort : Boolean) : Integer;
    function FreigebenLP (DBGrid : TDBGridPro; Daten : Pointer; InfoForm : TForm; var Option : String; var Done : Boolean; var Abort : Boolean) : Integer;
    function AssignARLP  (DBGrid : TDBGridPro; Daten : Pointer; InfoForm : TForm; var Option : String; var Done : Boolean; var Abort : Boolean) : Integer;

    procedure prepareHouseKeeping(aquery: TADOQuery; var editform: TEditLagerbereichForm; const RefLB: Integer);
  public
  end;

implementation

uses
  VCLUtilitys, StrUtils, StringUtils, DBMyParameter,
  LVSSecurity, LVSGlobalDaten, DatenModul, SprachModul, EditLocationDLG, EditLagerDLG,
  LVSLagertopologie, LVSDatenInterface,
  CreateLPSerie, ConfigModul, EditLPDLG,

  {$ifdef ConfigSetupNeu}
    ConfigSetupNeuDLG,
  {$else}
    ConfigSetupDLG,
  {$endif}

  KommunikationsPartnerDLG, PrintLPLableDLG,
  PrintModul, ChangeLPZuordnungDLG, DBGridUtilModule, DefKommFolgeDLG, MandantRelLagerDLG,
  IntegerUpDown, EditKommLPDLG, DrawLPDLG, ChangeLPLBDLG, SperrGrundDLG, CopyLagerDLG, CheckConfigDLG,
  EditLBZoneDLG, ImportLPDefinitionDLG, ImportKommFolgeDLG, LVSExport, ResourceText, CreateNachschubPlatzDLG,
  LagerRegalDLG, ErrorTracking, Clipbrd, FrontendImageModule, ArtikelListeDLG,
  FrontendMessages, EditNachschubParamDLG;

{$R *.dfm}

type
  TKommLPDaten = record
    MaxAnzahl : Integer;
    KommFolge : Integer;
  end;

  TArtikeDaten = record
    RefArtikel : Integer;
    RefEinheit : Integer;
  end;

  TLagerGridEntry = class (TGridRef)
    ACORef : Integer;
    RefLoc : Integer;

    constructor Create (const RecRef, RecRefLoc : Integer; const RecACORef : Integer);
  end;

  TChangeLPData = record
    EditForm    : TForm;
    ChangeQuery : TADOQuery;
  end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
constructor TLagerGridEntry.Create (const RecRef, RecRefLoc : Integer; const RecACORef : Integer);
begin
  inherited Create (RecRef);

  RefLoc := RecRefLoc;
  ACORef := RecACORef;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function TLagerTopologieForm.LoadLPArten (const RefLager : Integer; LPComboBox : TComboBoxPro) : Integer;
var
  query : TADOQuery;
begin
  query := TADOQuery.Create (Self);

  try
    query.LockType := ltReadOnly;
    query.Connection := LVSDatenModul.MainADOConnection;

    query.SQL.Add ('select * from V_LAGER_LP_TYPEN where REF_LAGER=:ref');
    query.Parameters [0].Value := RefLager;

    ClearComboBoxObjects (LPComboBox);

    query.Open;
    while not (query.Eof) do begin
      LPComboBox.Items.AddObject (query.FieldByName ('TYP').AsString + '|' + query.FieldByName ('BEZEICHNUNG').AsString, TComboBoxRef.Create (query.FieldByName ('REF').AsInteger));

      query.Next;
    end;
    query.Close;
  finally
    query.Free;
  end;

  Result := 0;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function TLagerTopologieForm.GetLBArt (const RefLB : Integer) : String;
var
  query : TADOQuery;
begin
  query := TADOQuery.Create (Self);

  try
    query.LockType := ltReadOnly;
    query.Connection := LVSDatenModul.MainADOConnection;

    query.SQL.Add ('select LB_ART from V_LB where REF=:ref');
    query.Parameters [0].Value := RefLB;

    try
      query.Open;

      if (query.RecNo > 0) then
        Result := query.Fields [0].AsString
      else Result := '';

      query.Close;
    except
      Result := '';
    end;
  finally
    query.Free;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function TLagerTopologieForm.GetLBArtLPType (const RefLB : Integer) : Integer;
var
  query : TADOQuery;
begin
  query := TADOQuery.Create (Self);

  try
    query.LockType := ltReadOnly;
    query.Connection := LVSDatenModul.MainADOConnection;

    query.SQL.Add ('select t.REF from V_LAGER_LP_TYPEN t, V_LB lb where t.REF_LAGER=lb.REF_LAGER and t.TYP=lb.LB_ART and lb.REF=:ref');
    query.Parameters [0].Value := RefLB;

    try
      query.Open;

      if (query.RecNo > 0) then
        Result := query.Fields [0].AsInteger
      else Result := -1;

      query.Close;
    except
      Result := -1;
    end;
  finally
    query.Free;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TLagerTopologieForm.ImportLPDataImportMenuItemClick(Sender: TObject);
var
  impform : TImportLPDefinitionForm;
begin
  impform := TImportLPDefinitionForm.Create (Self);

  try
    impform.LagerRef := GetComboBoxRef (LagerComboBox2);

    LoadLBComboboxNullEntry (impform.LBComboBox, '', '', GetComboBoxRef (LagerComboBox2));

    if (GetComboBoxRef (LBComboBox1) = -1) then
      impform.LBComboBox.ItemIndex := -1
    else begin
      impform.LBComboBox.ItemIndex := FindComboboxRef (impform.LBComboBox, GetComboBoxRef (LBComboBox1));
      impform.LBComboBoxChange (Sender);
    end;

    if (impform.ShowModal = mrOk) then
      LPDBGrid.Reload;
  finally
    impform.Release;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TLagerTopologieForm.ImportLPKommFolgeMenuItemClick(Sender: TObject);
var
  impform : TImportKommFolgeForm;
begin
  if (GetComboBoxRef (LagerComboBox2) > 0) then begin
    impform := TImportKommFolgeForm.Create (Self);

    try
      impform.LagerRef := GetComboBoxRef (LagerComboBox2);

      impform.LagerComboBox.Enabled := False;

      LoadLagerCombobox (impform.LagerComboBox, LVSDatenModul.AktLocationRef);
      impform.LagerComboBox.ItemIndex := FindComboboxRef (impform.LagerComboBox, GetComboBoxRef (LagerComboBox2));

      //Das wird erst mal noch nicht benutzt
      LoadLBComboboxNullEntry (impform.LBComboBox, '', '', GetComboBoxRef (LagerComboBox2));
      impform.LBComboBox.ItemIndex := -1;

      (*
      if (GetComboBoxRef (LBComboBox1) = -1) then
        impform.LBComboBox.ItemIndex := -1
      else begin
        impform.LBComboBox.ItemIndex := FindComboboxRef (impform.LBComboBox, GetComboBoxRef (LBComboBox1));
        impform.LBComboBoxChange (Sender);
      end;
      *)

      if (impform.ShowModal = mrOk) then
        LPDBGrid.Reload;
    finally
      impform.Release;
    end;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TLagerTopologieForm.LagerTabSheetShow(Sender: TObject);
begin
  if (Length (LVSDatenModul.AktLocation) = 0) then begin
    LoadLocationCombobox (LocComboBox);
  end else begin
    ClearComboBoxObjects (LocComboBox);

    LocComboBox.Items.AddObject (LVSDatenModul.AktLocation, TComboBoxRef.Create (LVSDatenModul.AktLocationRef));
  end;
  LocComboBox.Items.Insert (0, GetResourceText (1020));
  LocComboBox.Items.Insert (1, GetResourceText (1319));

  if (fAktLocationRef = -1) Then
    LocComboBox.ItemIndex := 0
  else begin
    LocComboBox.ItemIndex := FindComboboxRef(LocComboBox, fAktLocationRef);
    if (LocComboBox.ItemIndex = -1) then LocComboBox.ItemIndex := 0;
  end;

  UpdateLagerGrid (Sender);
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TLagerTopologieForm.UpdateLagerGrid(Sender: TObject);
var
  idx   : Integer;
begin
  ClearGridObjects (LagerStringGrid);

  ADOQuery1.SQL.Clear;
  ADOQuery1.SQL.Add ('select REF, LOCATION, NAME, LAGER_ART_STR, BESCHREIBUNG, BETRIEBS_NR, SPERR_LAGER, REF_ACO, REF_LOCATION from V_TOP_LAGER');

  if (GetComboBoxRef (LocComboBox) > 0) then
    ADOQuery1.SQL.Add ('where REF_LOCATION='+IntToStr (GetComboBoxRef(LocComboBox)))
  else if (LocComboBox.ItemIndex = 1) then
    ADOQuery1.SQL.Add ('where REF_LOCATION is null');

  idx := 0;

  ADOQuery1.Open;
  while not (ADOQuery1.Eof) do begin
    LagerStringGrid.Cells [1, LagerStringGrid.FixedRows + idx] := ADOQuery1.Fields [1].AsString;
    LagerStringGrid.Cells [2, LagerStringGrid.FixedRows + idx] := ADOQuery1.Fields [2].AsString;
    LagerStringGrid.Cells [3, LagerStringGrid.FixedRows + idx] := ADOQuery1.Fields [3].AsString;
    LagerStringGrid.Cells [4, LagerStringGrid.FixedRows + idx] := ADOQuery1.Fields [4].AsString;
    LagerStringGrid.Cells [5, LagerStringGrid.FixedRows + idx] := ADOQuery1.Fields [5].AsString;
    LagerStringGrid.Cells [6, LagerStringGrid.FixedRows + idx] := ADOQuery1.Fields [6].AsString;

    LagerStringGrid.Rows[LagerStringGrid.FixedRows + idx].Objects [0] := TLagerGridEntry.Create (ADOQuery1.FieldByName ('REF').AsInteger, ADOQuery1.FieldByName ('REF_LOCATION').AsInteger, ADOQuery1.FieldByName ('REF_ACO').AsInteger);

    Inc (idx);

    ADOQuery1.Next;
  end;
  ADOQuery1.Close;

  if (idx > 0) then begin
    LagerStringGrid.RowCount := LagerStringGrid.FixedRows + idx;
    LagerStringGridClick (Sender);
  end else begin
    LagerStringGrid.RowCount := LagerStringGrid.FixedRows + 1;
    LagerStringGrid.Rows [LagerStringGrid.FixedRows].Clear;
  end;

  LagerCopyButton.Enabled := (idx > 0);
  LagerCfgButton.Enabled  := (idx > 0);
  LagerCommButton.Enabled := (idx > 0);
  LagerMandButton.Enabled := (idx > 0);
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TLagerTopologieForm.LagerStringGridClick(Sender: TObject);
begin
  LagerEditButton.Enabled := False;
  LagerDelButton.Enabled  := False;

  if Assigned (LagerStringGrid.Rows [LagerStringGrid.Row].Objects [0]) then begin
    LagerEditButton.Enabled := LVSSecurityModule.CheckAdminRecht (TLagerGridEntry (LagerStringGrid.Rows [LagerStringGrid.Row].Objects [0]).ACORef);
    LagerDelButton.Enabled := LagerEditButton.Enabled;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TLagerTopologieForm.LagerStringGridDblClick(Sender: TObject);
var
  ref : Integer;
begin
  if Assigned (LagerStringGrid.Rows [LagerStringGrid.Row].Objects [0]) then begin
    ref := TLagerGridEntry (LagerStringGrid.Rows [LagerStringGrid.Row].Objects [0]).Ref;

    ChangeLagerDaten (ref);
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 24.06.2017
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TLagerTopologieForm.PrintLBLabelMenuItemClick(Sender: TObject);
var
  prtform : TPrintLPLableForm;
begin
  if (LBQuery.Active) and (LBQuery.RecNo > 0) then begin
    prtform := TPrintLPLableForm.Create (self);

    prtform.Prepare(UserReg.ReadRegValue ('LP-PRINTER'), 'E9', -1, LVSDatenModul.AktLocationRef, LBQuery.FieldByName ('REF_LAGER').AsInteger, -1);

    if (prtform.PrinterComboBox.Items.Count = 0) Then
      FrontendMessages.MessageDlg (FormatMessageText (1058, ['E9']), mtError, [mbOK], 0)
    else begin
      prtform.LBZonenTabSheet.TabVisible := True;
      prtform.PageControl1.ActivePage := prtform.LBZonenTabSheet;

      prtform.RefLB := LBQuery.FieldByName ('REF').AsInteger;

      prtform.ShowModal;
    end;

    UserReg.WriteRegValue ('LP-PRINTER', prtform.GetSelectedPrinter);

    prtform.Free;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TLagerTopologieForm.PrintLBZonenLabelMenuItemClick(Sender: TObject);
var
  prtform : TPrintLPLableForm;
begin
  if (LBZoneQuery.Active) and (LBZoneQuery.RecNo > 0) then begin
    prtform := TPrintLPLableForm.Create (self);

    prtform.Prepare(UserReg.ReadRegValue ('LP-PRINTER'), 'E9', -1, LVSDatenModul.AktLocationRef, LBQuery.FieldByName ('REF_LAGER').AsInteger, -1);

    if (prtform.PrinterComboBox.Items.Count = 0) Then
      FrontendMessages.MessageDlg('Für dieses Lager ist kein passender Etikettendrucker (Format E9) definiert', mtError, [mbOK], 0)
    else begin
      prtform.LBZonenTabSheet.TabVisible := True;
      prtform.PageControl1.ActivePage := prtform.LBZonenTabSheet;

      prtform.RefLBZone := LBZoneQuery.FieldByName ('REF').AsInteger;

      prtform.ShowModal;
    end;

    UserReg.WriteRegValue ('LP-PRINTER', prtform.GetSelectedPrinter);

    prtform.Free;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function TLagerTopologieForm.ChangeLagerDaten (const Ref : Integer) : Integer;
var
  res,
  idx,
  hkl,
  diff      : Integer;
  optch     : char;
  optstr,
  koptstr   : String;
  altcursor : TCursor;
  editform  : TEditLagerForm;
begin
  res := 0;

  editform := TEditLagerForm.Create (Self);
  editform.MandantPanel.Visible := False;

  try
    editform.ArtComboBox.Enabled := False;

    editform.LocComboBox.Clear;
    editform.LocComboBox.Items.Add (GetResourceText (1319));

    ADOQuery1.SQL.Clear;
    ADOQuery1.SQL.Add ('select REF, NAME, BESCHREIBUNG from V_PCD_LOCATION order by NAME');

    ADOQuery1.Open;
    while not (ADOQuery1.Eof) do begin
      idx := editform.LocComboBox.Items.AddObject (ADOQuery1.Fields [1].AsString+'|'+ADOQuery1.Fields [2].AsString, TComboBoxRef.Create (ADOQuery1.Fields [0].AsInteger));

      ADOQuery1.Next;
    end;
    ADOQuery1.Close;

    LoadComboxDBItems (editform.ArtComboBox, 'LAGER', 'LAGER_ART', True);

    LoadComboxDBItems (editform.HKLComboBox, 'LAGER', 'HOEHEN_KLASSE');
    editform.HKLComboBox.Items.Insert (0, '');

    LoadAlleLagerCombobox (editform.SperrLagerComboBox, '''SPERR''', GetComboBoxRef (editform.LocComboBox));

    editform.SperrLagerComboBox.Items.Insert (0, '');

    ADOQuery1.SQL.Clear;
    ADOQuery1.SQL.Add ('select * from V_LAGER where REF=:ref');
    ADOQuery1.Parameters.ParamByName('ref').Value := ref;

    try
      ADOQuery1.Open;

      with editform do begin
        Caption := GetResourceText (1324);

        NameEdit.Text         := ADOQuery1.FieldByName ('NAME').AsString;
        BeschreibungEdit.Text := ADOQuery1.FieldByName ('BESCHREIBUNG').AsString;
        BetriebEdit.Text      := ADOQuery1.FieldByName ('BETRIEBS_NR').AsString;
        AdrEdit.Text          := ADOQuery1.FieldByName ('ADR_ZUSATZ').AsString;
        RoadEdit.Text         := ADOQuery1.FieldByName ('STRASSE').AsString;
        PLZEdit.Text          := ADOQuery1.FieldByName ('PLZ').AsString;
        OrtEdit.Text          := ADOQuery1.FieldByName ('ORT').AsString;
        LandEdit.Text         := ADOQuery1.FieldByName ('LAND').AsString;
        ILNEdit.Text          := ADOQuery1.FieldByName ('ILN').AsString;
        EUNrEdit.Text         := ADOQuery1.FieldByName ('EU_ZULASSUNG').AsString;
        FonEdit.Text          := ADOQuery1.FieldByName ('TELEFON').AsString;
        FaxEdit.Text          := ADOQuery1.FieldByName ('FAX').AsString;
        ContactEdit.Text      := ADOQuery1.FieldByName ('ANSPRECHPARTNER').AsString;
        MailEdit.Text         := ADOQuery1.FieldByName ('MAIL').AsString;

        if not Assigned (ADOQuery1.FindField('ID_IFC')) then
          IFCIDEdit.Visible := false
        else
          IFCIDEdit.Text := ADOQuery1.FieldByName ('ID_IFC').AsString;

        LocComboBox.ItemIndex := FindComboboxRef (LocComboBox, ADOQuery1.FieldByName ('REF_LOCATION').AsInteger);
        if (LocComboBox.ItemIndex = -1) then LocComboBox.ItemIndex := 0;

        ArtComboBox.ItemIndex := ArtComboBox.IndexOf(ADOQuery1.FieldByName ('LAGER_ART').AsString, 0);
        if (ArtComboBox.ItemIndex = -1) then ArtComboBox.ItemIndex := 0;

        HKLComboBox.ItemIndex := HKLComboBox.IndexOf (ADOQuery1.FieldByName ('DEFAULT_HKL').AsString);
        if (HKLComboBox.ItemIndex = -1) then HKLComboBox.ItemIndex := 0;

        //Ein Sperrlager kann selber kein Sperrlager mehr haben
        if (ADOQuery1.FieldByName ('LAGER_ART').AsString = 'SPERR') then begin
          SperrLagerComboBox.Enabled := False;
          SperrLagerComboBox.Text := '';
          SperrLagerComboBox.ItemIndex := -1
        end else if (ADOQuery1.FieldByName ('REF_SPERR_LAGER').IsNull) then
          SperrLagerComboBox.ItemIndex := 0
        else begin
          SperrLagerComboBox.ItemIndex := GetComboBoxRef (SperrLagerComboBox, ADOQuery1.FieldByName ('REF_SPERR_LAGER').AsInteger);
          if (SperrLagerComboBox.ItemIndex = -1) then SperrLagerComboBox.ItemIndex := 0;
        end;

        if Assigned (ADOQuery1.FindField ('OPT_BESTAND')) then
          BestandCheckBox.Checked := (ADOQuery1.FieldByName ('OPT_BESTAND').AsString = '1')
        else begin
          BestandCheckBox.Enabled := False;
          BestandCheckBox.Checked := True;
        end;

        PlanVollPALCheckBox.Checked       := CheckOpt (ADOQuery1.FieldByName ('KOMM_OPT').AsString, 1);
        PlanNVEPalFaktorCheckBox.Checked  := CheckOpt (ADOQuery1.FieldByName ('KOMM_OPT').AsString, 2);
        KommOptCheckBox.Checked           := CheckOpt (ADOQuery1.FieldByName ('KOMM_OPT').AsString, 5);
        SumResCheckBox.Checked            := CheckOpt (ADOQuery1.FieldByName ('KOMM_OPT').AsString, 6);
        ErsatzpickCheckBox.Checked        := CheckOpt (ADOQuery1.FieldByName ('KOMM_OPT').AsString, 7);
        FehlwareInvCheckBox.Checked       := CheckOpt (ADOQuery1.FieldByName ('KOMM_OPT').AsString, 9);
        NullInvKommCheckBox.Checked           := CheckOpt (ADOQuery1.FieldByName ('KOMM_OPT').AsString, 8);
        FehlwareCheckInvCheckBox.Checked  := CheckOpt (ADOQuery1.FieldByName ('KOMM_OPT').AsString, 10);
        BesCheckKommBeginCheckBox.Checked := CheckOpt (ADOQuery1.FieldByName ('KOMM_OPT').AsString, 12);
        PlanTourPalCheckBox.Checked       := CheckOpt (ADOQuery1.FieldByName ('KOMM_OPT').AsString, 14);

        OptVerkaufCheckBox.Checked       := CheckOpt (ADOQuery1.FieldByName ('OPTIONS').AsString, 1);       //PA_LAGER.LAGER_OPT_VERKAUF
        OptWEImCheckBox.Checked          := CheckOpt (ADOQuery1.FieldByName ('OPTIONS').AsString, 2);       //PA_LAGER.LAGER_OPT_IM_WE_RESPONSE
        OptRETImCheckBox.Checked         := CheckOpt (ADOQuery1.FieldByName ('OPTIONS').AsString, 3);       //PA_LAGER.LAGER_OPT_IM_RET_RESPONSE
        OptCloseLFCheckBox.Checked       := CheckOpt (ADOQuery1.FieldByName ('OPTIONS').AsString, 10);      //PA_LAGER.LAGER_OPT_AUTO_CLOSE_LF
        LiefRetCheckBox.Checked          := CheckOpt (ADOQuery1.FieldByName ('OPTIONS').AsString, 5);       //PA_LAGER.LAGER_OPT_LIEFERANT_RET
        OPTWEPosAKTCheckBox.Checked      := CheckOpt (ADOQuery1.FieldByName ('OPTIONS').AsString, 6);       //PA_LAGER.LAGER_OPT_IM_WE_POS_RESPONSE
        OptWEBesAKTBestCloseCheckBox.Checked := CheckOpt (ADOQuery1.FieldByName ('OPTIONS').AsString, 8);   //PA_LAGER.LAGER_OPT_BEST_BES_AKT_CLOSE
        OPTRETPosAKTCheckBox.Checked     := CheckOpt (ADOQuery1.FieldByName ('OPTIONS').AsString, 7);       //PA_LAGER.LAGER_OPT_IM_RET_POS_RESPONSE

        AutoCloseRetourenAvisCheckBox.Checked := CheckOpt (ADOQuery1.FieldByName ('OPTIONS').AsString, 12); //PA_LAGER.LAGER_OPT_AUTO_CLOSE_RETOURE
        AutoCloseBestellungCheckBox.Checked   := CheckOpt (ADOQuery1.FieldByName ('OPTIONS').AsString, 11); //PA_LAGER.LAGER_OPT_AUTO_CLOSE_BEST

        BesMergeCheckBox.Checked := CheckOpt (ADOQuery1.FieldByName ('OPTIONS').AsString, 13);              //PA_LAGER.LAGER_OPT_MARGE_BESTAND

        optch := GetOpt (ADOQuery1.FieldByName ('OPTIONS').AsString, 14);

        if (optch = #0) or (optch = ' ') then begin
          NoteDateReqCheckBox.Checked     := false;
          NoteDateDefaultCheckBox.Checked := true;
        end else begin
          NoteDateReqCheckBox.Checked     := (optch = '2') or (optch = '3');
          NoteDateDefaultCheckBox.Checked := (optch = '0') or (optch = '3');
        end;

        optch := GetOpt (ADOQuery1.FieldByName ('OPTIONS').AsString, 18);

        if (optch = #0) or (optch = ' ') then
          NoteNoDutyCheckBox.Checked := true
        else
          NoteNoDutyCheckBox.Checked := (optch = '0');  //Historisch bedingt ist Pflicht='0' und keine Pflicht > '0'

        NullInvEinCheckBox.Checked  := CheckOpt (ADOQuery1.FieldByName ('OPTIONS').AsString, 19);
        NullInvNachCheckBox.Checked := CheckOpt (ADOQuery1.FieldByName ('OPTIONS').AsString, 20);

        if not Assigned (ADOQuery1.FindField('MAX_KOMM_INV_DIFF')) then
          MaxInvMengeEdit.Visible := false
        else if (ADOQuery1.FieldByName ('MAX_KOMM_INV_DIFF').IsNull) then
          MaxInvMengeEdit.Text := ''
        else
          MaxInvMengeEdit.Text := ADOQuery1.FieldByName ('MAX_KOMM_INV_DIFF').AsString;

        if not Assigned (ADOQuery1.FindField('AUTO_KOMM_INV_DIFF')) then begin
          AutoInvArCheckBox.Visible := false;
          AutoCheckInvCheckBox.Visible := false;
        end else begin
          AutoCheckInvCheckBox.Checked := (ADOQuery1.FieldByName ('AUTO_KOMM_INV_DIFF').AsString <> '0');

          if not Assigned (ADOQuery1.FindField('AUTO_KOMM_INV_AR')) then
            AutoInvArCheckBox.Visible := false
          else
            AutoInvArCheckBox.Checked := (ADOQuery1.FieldByName ('AUTO_KOMM_INV_AR').AsString <> '0');
        end;

        if not Assigned (ADOQuery1.FindField('NEAR_ZERO_INV_QTY')) then
          NaheNullEdit.Visible := false
        else if (ADOQuery1.FieldByName ('NEAR_ZERO_INV_QTY').IsNull) then
          NaheNullEdit.Text := ''
        else
          NaheNullEdit.Text := ADOQuery1.FieldByName ('NEAR_ZERO_INV_QTY').AsString;

        if not Assigned(ADOQuery1.FindField('NEXT_INV_MIN_DAYS')) then begin
          NextInvMinDaysEdit.Visible := false;
        end else if ADOQuery1.FieldByName('NEXT_INV_MIN_DAYS').IsNull then begin
          NextInvMinDaysEdit.Text := '';
        end else begin
          NextInvMinDaysEdit.Text := ADOQuery1.FieldByName('NEXT_INV_MIN_DAYS').AsString;
        end;


        optch := GetOpt (ADOQuery1.FieldByName ('OPTIONS').AsString, 15);

        if (optch = #0) or (optch = ' ') then begin
          DeliveryDateReqCheckBox.Checked     := false;
          DeliveryDateDefaultCheckBox.Checked := true;
        end else begin
          DeliveryDateReqCheckBox.Checked     := (optch = '2') or (optch = '3');
          DeliveryDateDefaultCheckBox.Checked := (optch = '0') or (optch = '3');
        end;

        RetUniqueIDCheckBox.Checked := CheckOpt (ADOQuery1.FieldByName ('OPTIONS').AsString, 16);         //PA_LAGER.LAGER_OPT_RET_UNIQUE_ID
        RetNotAKTCheckBox.Checked := CheckOpt (ADOQuery1.FieldByName ('OPTIONS').AsString, 17);           //PA_LAGER.LAGER_OPT_RET_POS_NOT_AKT_ON_CLOSE
      end;

      ADOQuery1.Close;
    except
      on e: Exception do begin
        ErrorTrackingModule.WriteErrorLog ('TLagerTopologieForm.ChangeLagerDaten', e.ClassName + ' : ' + e.Message + #13+#10+'Query: '+ADOQuery1.SQL.Text);
      end;
    end;

    if (editform.ShowModal = mrOK) then begin
      altcursor := Screen.Cursor;
      Screen.Cursor :=crSQLWait;
      try
        koptstr := '00000000000000000000';

        if editform.PlanVollPALCheckBox.Checked then
          koptstr [1] := '1';

        if editform.PlanNVEPalFaktorCheckBox.Checked then
          koptstr [2] := '1';

        if editform.KommOptCheckBox.Checked then
          koptstr [5] := '1';

        if editform.SumResCheckBox.Checked then
          koptstr [6] := '1';

        if editform.ErsatzpickCheckBox.Checked then
          koptstr [7] := '1';

        if editform.FehlwareInvCheckBox.Checked then
          koptstr [9] := '1';

        if editform.NullInvKommCheckBox.Checked then begin
          koptstr [8] := '1';
        end;

        if editform.FehlwareCheckInvCheckBox.Enabled and editform.FehlwareCheckInvCheckBox.Checked then
          koptstr [10] := '1';

        if editform.BesCheckKommBeginCheckBox.Enabled and  editform.BesCheckKommBeginCheckBox.Checked then
          koptstr [12] := '1';

        if editform.PlanTourPalCheckBox.Enabled and  editform.PlanTourPalCheckBox.Checked then
          koptstr [14] := '1';

        optstr := '0000000000000000000000000000000';
        if editform.OptVerkaufCheckBox.Checked then
          optstr [1] := '1';
        if editform.OptWEImCheckBox.Checked then
          optstr [2] := '1';
        if editform.OptRETImCheckBox.Checked then
          optstr [3] := '1';
        if editform.LiefRetCheckBox.Checked then
          optstr [5] := '1';
        if editform.OPTWEPosAKTCheckBox.Checked then
          optstr [6] := '1';
        if editform.OPTRETPosAKTCheckBox.Checked then
          optstr [7] := '1';
        if editform.OptWEBesAKTBestCloseCheckBox.Checked then
          optstr [8] := '1';
        if editform.OptCloseLFCheckBox.Checked then
          optstr [10] := '1';
        if editform.AutoCloseBestellungCheckBox.Checked then
          optstr [11] := '1';
        if editform.AutoCloseRetourenAvisCheckBox.Checked then
          optstr [12] := '1';
        if editform.BesMergeCheckBox.Checked then
          optstr [13] := '1';

        if editform.NoteDateReqCheckBox.Checked and editform.NoteDateDefaultCheckBox.Checked then
          optstr [14] := '3'
        else if editform.NoteDateReqCheckBox.Checked then
          optstr [14] := '2'
        else if editform.NoteDateDefaultCheckBox.Checked then
          optstr [14] := '0'
        else
          optstr [14] := '1';

        if editform.DeliveryDateReqCheckBox.Checked and editform.DeliveryDateDefaultCheckBox.Checked then
          optstr [15] := '3'
        else if editform.DeliveryDateReqCheckBox.Checked then
          optstr [15] := '2'
        else if editform.DeliveryDateDefaultCheckBox.Checked then
          optstr [15] := '0'
        else
          optstr [15] := '1';

        if editform.RetUniqueIDCheckBox.Checked then
          optstr [16] := '1';

        if editform.RetNotAKTCheckBox.Checked then
          optstr [17] := '1';

        if not editform.NoteNoDutyCheckBox.Checked then   //Historisch bedingt ist Pflicht='0' und keine Pflicht > '0'
          optstr [18] := '1';

        if editform.NullInvEinCheckBox.Checked then begin
          optstr [19] := '1';
        end;

        if editform.NullInvNachCheckBox.Checked then begin
          optstr [20] := '1';
        end;

        if (editform.HKLComboBox.ItemIndex = 0) then
          hkl := -1
        else if not (TryStrToInt (editform.HKLComboBox.GetItemText, hkl)) then
          hkl := -1;


        res := ChangeLager (ref,
                            GetComboBoxRef (editform.LocComboBox),
                            GetComboBoxRef (editform.SperrLagerCombobox),
                            hkl,
                            editform.NameEdit.Text,
                            editform.BeschreibungEdit.Text,
                            editform.BetriebEdit.Text,
                            editform.ILNEdit.Text,
                            editform.EUNrEdit.Text,
                            koptstr,
                            optstr);

        if (res = 0) Then
          res := SetLagerAdresse (ref, editform.AdrEdit.Text, editform.RoadEdit.Text, editform.PLZEdit.Text, editform.OrtEdit.Text, editform.LandEdit.Text);

        if (res = 0) Then
          res := SetLagerInfos (ref, editform.FonEdit.Text, editform.FaxEdit.Text, editform.ContactEdit.Text, editform.MailEdit.Text);

        if (res = 0) and editform.IFCIDEdit.Visible Then
          res := SetLagerIFCID (ref, editform.IFCIDEdit.Text);

        if (res = 0) and editform.MaxInvMengeEdit.Visible and editform.MaxInvMengeEdit.Enabled Then begin
          if (Length (editform.MaxInvMengeEdit.Text) = 0) then
            diff := -1
          else if not (TryStrToInt (editform.MaxInvMengeEdit.Text, diff)) then
            diff := -1;

          if not (editform.AutoCheckInvCheckBox.Visible) then
            res := SetLagerMaxInvDiff (ref, diff)
          else if not (editform.AutoInvArCheckBox.Visible) then
            res := SetLagerMaxInvDiff (ref, diff, editform.AutoCheckInvCheckBox.Checked)
          else
            res := SetLagerMaxInvDiff (ref, diff, editform.AutoCheckInvCheckBox.Checked, editform.AutoInvArCheckBox.Checked);
        end;

        if (res = 0) and editform.NaheNullEdit.Visible and editform.NaheNullEdit.Enabled Then begin
          if (Length (editform.NaheNullEdit.Text) = 0) then
            diff := -1
          else if not (TryStrToInt (editform.NaheNullEdit.Text, diff)) then
            diff := -1;

          res := SetLagerNaheNull (ref, diff);
        end;

        if (res = 0) and editform.NextInvMinDaysEdit.Visible and editform.NextInvMinDaysEdit.Enabled Then begin
          if (Length (editform.NextInvMinDaysEdit.Text) = 0) then begin
            diff := -1
          end else if not (TryStrToInt (editform.NextInvMinDaysEdit.Text, diff)) then begin
            diff := -1;
          end;

          res := SetLagerNextInvMinDays(ref, diff);
        end;

        if (res = 0) and editform.BestandCheckBox.Enabled Then begin
          res := SetLagerOpeBestand (ref, editform.BestandCheckBox.Checked);
        end;
      except
        on e: Exception do begin
          res := -9;

          ErrorTrackingModule.WriteErrorLog ('TLagerTopologieForm.ChangeLagerDaten', e.ClassName + ' : ' + e.Message);
        end;
      end;
      Screen.Cursor := altcursor;

      if (res = 0) Then
        LagerTabSheetShow (Self)
      else FrontendMessages.MessageDlg (FormatMessageText (1248, [LVSDatenModul.LastLVSErrorText]), mtError, [mbOK], 0);
    end;
  finally
    editform.Free;
  end;

  Result := res;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TLagerTopologieForm.LagerNeuButtonClick(Sender: TObject);
var
  res,
  ref,
  hkl,
  diff      : Integer;
  optstr,
  locstr,
  koptstr   : String;
  altcursor : TCursor;
  editform  : TEditLagerForm;
begin
  editform := TEditLagerForm.Create (Self);
  editform.MandantPanel.Visible := False;

  with editform do begin
    Caption := GetResourceText (1323);

    NameEdit.Text         := '';
    BeschreibungEdit.Text := '';
    BetriebEdit.Text      := '';

    ADOQuery1.SQL.Clear;
    ADOQuery1.SQL.Add ('select * from V_LAGER where ROWNUM=0');

    ADOQuery1.Open;

    if not Assigned (ADOQuery1.FindField('ID_IFC')) then
      IFCIDEdit.Visible := false;

    if not Assigned (ADOQuery1.FindField('MAX_KOMM_INV_DIFF')) then
      MaxInvMengeEdit.Visible := false;

    if not Assigned (ADOQuery1.FindField('AUTO_KOMM_INV_DIFF')) then
      AutoCheckInvCheckBox.Visible := false;

    if not Assigned (ADOQuery1.FindField('NEAR_ZERO_INV_QTY')) then
      NaheNullEdit.Visible := false;

    if not Assigned (ADOQuery1.FindField('NEXT_INV_MIN_DAYS')) then begin
      NextInvMinDaysEdit.Visible := false;
    end;

    if not Assigned (ADOQuery1.FindField ('OPT_BESTAND')) then
      BestandCheckBox.Enabled := False;

    ADOQuery1.Close;
  end;

  editform.LocComboBox.Clear;
  editform.LocComboBox.Items.Add (GetResourceText (1319));

  ADOQuery1.SQL.Clear;
  ADOQuery1.SQL.Add ('select REF, NAME, BESCHREIBUNG from V_PCD_LOCATION order by NAME');

  ADOQuery1.Open;
  while not (ADOQuery1.Eof) do begin
    editform.LocComboBox.Items.AddObject (ADOQuery1.Fields [1].AsString+'|'+ADOQuery1.Fields [2].AsString, TComboBoxRef.Create (ADOQuery1.Fields [0].AsInteger));

    ADOQuery1.Next;
  end;
  ADOQuery1.Close;

  if (LocComboBox.ItemIndex = 0) Then begin
    editform.LocComboBox.Enabled := True;

    editform.LocComboBox.ItemIndex := 0
  end else begin
    editform.LocComboBox.Enabled := False;

    editform.LocComboBox.ItemIndex := FindComboboxRef (editform.LocComboBox, GetComboBoxRef(LocComboBox));
  end;

  LoadComboxDBItems (editform.ArtComboBox, 'LAGER', 'LAGER_ART', True);
  if (editform.ArtComboBox.Items.Count = 0) then
    FrontendMessages.MessageDlg ('Es sind noch keine Lagerarten angelegt', mtError, [mbOK], 0)
  else begin
    editform.ArtComboBox.ItemIndex := 0;

    LoadAlleLagerCombobox (editform.SperrLagerComboBox, '''SPERR''', GetComboBoxRef (editform.LocComboBox));

    editform.SperrLagerComboBox.Items.Insert (0, '');
    editform.SperrLagerComboBox.ItemIndex := 0;

    if (editform.ShowModal = mrOK) then begin
      altcursor := Screen.Cursor;
      Screen.Cursor :=crSQLWait;
      try
        koptstr := '00000000000000000000';

        if editform.PlanVollPALCheckBox.Checked then
          koptstr [1] := '1';

        if editform.PlanNVEPalFaktorCheckBox.Checked then
          koptstr [2] := '1';

        if editform.KommOptCheckBox.Checked then
          koptstr [5] := '1';

        if editform.SumResCheckBox.Checked then
          koptstr [6] := '1';

        if editform.ErsatzpickCheckBox.Checked then
          koptstr [7] := '1';

        if editform.FehlwareInvCheckBox.Checked then
          koptstr [9] := '1';

        if editform.NullInvKommCheckBox.Checked then
          koptstr [8] := '1';

        if editform.FehlwareInvCheckBox.Enabled and editform.FehlwareInvCheckBox.Checked then
          koptstr [10] := '1';

        if editform.BesCheckKommBeginCheckBox.Enabled and  editform.BesCheckKommBeginCheckBox.Checked then
          koptstr [12] := '1';

        if editform.PlanTourPalCheckBox.Enabled and  editform.PlanTourPalCheckBox.Checked then
          koptstr [14] := '1';

        optstr := '00000000000000000000000';
        if editform.OptVerkaufCheckBox.Checked then
          optstr [1] := '1';
        if editform.OptWEImCheckBox.Checked then
          optstr [2] := '1';
        if editform.OptRETImCheckBox.Checked then
          optstr [3] := '1';
        if editform.LiefRetCheckBox.Checked then
          optstr [5] := '1';
        if editform.OPTWEPosAKTCheckBox.Checked then
          optstr [6] := '1';
        if editform.OPTRETPosAKTCheckBox.Checked then
          optstr [7] := '1';
        if editform.OptCloseLFCheckBox.Checked then
          optstr [10] := '1';
        if editform.AutoCloseBestellungCheckBox.Checked then
          optstr [11] := '1';
        if editform.AutoCloseRetourenAvisCheckBox.Checked then
          optstr [12] := '1';
        if editform.BesMergeCheckBox.Checked then
          optstr [13] := '1';

        if editform.NoteDateReqCheckBox.Checked and editform.NoteDateDefaultCheckBox.Checked then
          optstr [14] := '3'
        else if editform.NoteDateReqCheckBox.Checked then
          optstr [14] := '2'
        else if editform.NoteDateDefaultCheckBox.Checked then
          optstr [14] := '0'
        else
          optstr [14] := '1';

        if editform.DeliveryDateReqCheckBox.Checked and editform.DeliveryDateDefaultCheckBox.Checked then
          optstr [15] := '3'
        else if editform.DeliveryDateReqCheckBox.Checked then
          optstr [15] := '2'
        else if editform.DeliveryDateDefaultCheckBox.Checked then
          optstr [15] := '0'
        else
          optstr [15] := '1';

        if editform.RetUniqueIDCheckBox.Checked then
          optstr [16] := '1';

        if editform.RetNotAKTCheckBox.Checked then
          optstr [17] := '1';

        if not editform.NoteNoDutyCheckBox.Checked then   //Historisch bedingt ist Pflicht='0' und keine Pflicht > '0'
          optstr [18] := '1';

        if editform.NullInvEinCheckBox.Checked then
          optstr [19] := '1';

        if editform.NullInvNachCheckBox.Checked then
          optstr [20] := '1';

        if (editform.LocComboBox.ItemIndex = 0) then
          locstr := ''
        else
          locstr := editform.LocComboBox.GetItemText;

        if (editform.HKLComboBox.ItemIndex = 0) then
          hkl := -1
        else if not (TryStrToInt (editform.HKLComboBox.GetItemText, hkl)) then
          hkl := -1;

        res := CreateLager (LVSConfigModul.RefProject,
                            locstr,
                            editform.NameEdit.Text,
                            editform.BeschreibungEdit.Text,
                            editform.ArtComboBox.GetItemText,
                            editform.BetriebEdit.Text,
                            editform.ILNEdit.Text,
                            editform.EUNrEdit.Text,
                            koptstr,
                            optstr,
                            GetComboboxRef (editform.SperrLagerComboBox),
                            hkl,
                            ref);

        if (res = 0) Then
          res := SetLagerAdresse (ref, editform.AdrEdit.Text, editform.RoadEdit.Text, editform.PLZEdit.Text, editform.OrtEdit.Text, editform.LandEdit.Text);

        if (res = 0) Then
          res := SetLagerInfos (ref, editform.FonEdit.Text, editform.FaxEdit.Text, editform.ContactEdit.Text, editform.MailEdit.Text);

        if (res = 0) and editform.IFCIDEdit.Visible Then
          res := SetLagerIFCID (ref, editform.IFCIDEdit.Text);

        if (res = 0) and editform.MaxInvMengeEdit.Visible and editform.MaxInvMengeEdit.Enabled Then begin
          if (Length (editform.MaxInvMengeEdit.Text) = 0) then
            diff := -1
          else if not (TryStrToInt (editform.MaxInvMengeEdit.Text, diff)) then
            diff := -1;

          if not (editform.AutoCheckInvCheckBox.Visible) then
            res := SetLagerMaxInvDiff (ref, diff)
          else if not (editform.AutoInvArCheckBox.Visible) then
            res := SetLagerMaxInvDiff (ref, diff, editform.AutoCheckInvCheckBox.Checked)
          else
            res := SetLagerMaxInvDiff (ref, diff, editform.AutoCheckInvCheckBox.Checked, editform.AutoInvArCheckBox.Checked);
        end;

        if (res = 0) and editform.NaheNullEdit.Visible and editform.NaheNullEdit.Enabled Then begin
          if (Length (editform.NaheNullEdit.Text) = 0) then
            diff := -1
          else if not (TryStrToInt (editform.NaheNullEdit.Text, diff)) then
            diff := -1;

          res := SetLagerNaheNull (ref, diff);
        end;

        if (res = 0) and editform.NextInvMinDaysEdit.Visible and editform.NextInvMinDaysEdit.Enabled Then begin
          if (Length (editform.NextInvMinDaysEdit.Text) = 0) then begin
            diff := -1
          end else if not (TryStrToInt (editform.NextInvMinDaysEdit.Text, diff)) then begin
            diff := -1;
          end;

          res := SetLagerNextInvMinDays(ref, diff);
        end;

        if (res = 0) and (LVSDatenModul.AktMandantRef > 0) Then
          res := DefMandantLager (LVSDatenModul.AktMandantRef, ref, '1')
      except
        on e: Exception do begin
          res := -9;

          ErrorTrackingModule.WriteErrorLog ('TLagerTopologieForm.LagerNeuButtonClick', e.ClassName + ' : ' + e.Message);
        end;
      end;
      Screen.Cursor := altcursor;

      if (res = 0) Then
        LagerTabSheetShow (Self)
      else FrontendMessages.MessageDlg (FormatMessageText (1247, [LVSDatenModul.LastLVSErrorText]), mtError, [mbOK], 0);
    end;
  end;

  editform.Free;
end;

procedure TLagerTopologieForm.LagerPopupMenuPopup(Sender: TObject);
var
  col,
  row : Integer;
  clp : TPoint;
begin
  GetCursorPos (clp);
  clp := LagerStringGrid.ScreenToClient (clp);

  LagerStringGrid.MouseToCell (clp.X, clp.Y, col, row);

  LagerCheckConfigMenuItem.Enabled := False;

  if (col = 0) and (row = 0) then begin
    LagerCopyColMenuItem.Visible := False;
    LagerColOptimalMenuItem.Visible := True;
  end else begin
    LagerCopyColMenuItem.Visible := True;
    LagerColOptimalMenuItem.Visible := False;

    LagerCheckConfigMenuItem.Enabled := True;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TLagerTopologieForm.LBTabSheetShow(Sender: TObject);
begin
  LoadLagerCombobox (LagerComboBox1, LVSDatenModul.AktLocation);

  LagerComboBox1.ItemIndex := FindComboboxRef(LagerComboBox1, fAktLagerRef );

  if (LagerComboBox1.ItemIndex = -1) then begin
    if (Length (LVSDatenModul.AktLager) > 0) Then
      LagerComboBox1.ItemIndex := LagerComboBox1.IndexOf(LVSDatenModul.AktLager)
    else if (LagerComboBox1.Items.Count > 0) then
      LagerComboBox1.ItemIndex := 0
    else LagerComboBox1.ItemIndex := -1;
  end;

  LBCopyButton.Enabled := False;
  EditLBButton.Enabled := False;
  DelLPButton.Enabled  := False;

  if (LagerComboBox1.ItemIndex <> -1) then begin
    LagerComboBox1Change (LagerComboBox1);

    if (LBQuery.Active) and (fAktBereichRef > 0) then begin
      try
        LBQuery.Locate ('REF', fAktBereichRef, []);
      except
      end;
    end;
  end;

  LBNeuButton.Enabled := (GetComboBoxRef (LagerComboBox1) <> -1);
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TLagerTopologieForm.LagerComboBox1Change(Sender: TObject);
begin
  if (LagerComboBox1.ItemIndex >= 0) Then begin
    if Assigned (LagerComboBox1.Items.Objects[LagerComboBox1.ItemIndex]) then begin
      LBQuery.SQL.Clear;
      LBQuery.SQL.Add ('select * from V_PCD_LB where REF_LAGER=:ref');
      LBQuery.Parameters.ParamByName('ref').Value := TComboBoxRef (LagerComboBox1.Items.Objects[LagerComboBox1.ItemIndex]).Ref;

      LBQuery.Open;

      LBDBGrid.SetColumnVisible ('KOMM_ART', False);
      LBDBGrid.SetColumnVisible ('USE_FOR_KOMM', False);
      LBDBGrid.SetColumnVisible ('USE_FOR_VORRAT', False);
      LBDBGrid.SetColumnVisible ('AUTO_EMPTY_LE_REMOVE', False);
    end;
  end;

  LBDBGrid.SetFocus;
end;

//******************************************************************************
//* Function Name: PrepareHouseKeeping
//* Author       : Sebastian Schütte
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TLagerTopologieForm.prepareHouseKeeping(aquery: TADOQuery; var editform: TEditLagerbereichForm; const RefLB: Integer);
  procedure prepareAutoInv;
    function DbCharToBool(const AString: string): Boolean;
    begin
      if AString = 'Y' then begin
        Result := True;
      end else begin
        Result := False;
      end;
    end;
  begin
    with editform do
    begin
      AInvIntervalEdit.Text := aquery.FieldByName('INTERVAL_DAYS').AsString;
      AInvCountEdit.Text := aquery.FieldByName('MAX_LP_COUNT').AsString;
      AInvAlterEdit.Text := aquery.FieldByName('MIN_LP_AGE').AsString;
      AInvFreigebenCheckbox.Checked := DbCharToBool(aquery.FieldByName('INV_RELEASE').AsString);
      if aquery.FieldByName('INV_STATUS').AsString = 'AKT' then
      begin
        AInvCheckBox.Checked := True;
        AInvGroupBox.Enabled := True;
        AInvIntervalEdit.Enabled := True;
        AInvCountEdit.Enabled := True;
        AInvAlterEdit.Enabled := True;
        AInvFreigebenCheckbox.Enabled := True;
      end
    end;
  end;
  procedure prepareRuecklagerung;
  begin
    with editform do
    begin
      RueckLBComboBox.ItemIndex := FindComboboxRef(RueckLBComboBox, aquery.FieldByName('REF_MHD_LB').AsInteger);
      RueckFuellgradEdit.Text := aquery.FieldByName('FUELLGRAD').AsString;
      if RueckFuellgradEdit.Text = '-1' then
      begin
        RueckFuellgradEdit.Text := '';
      end;
      RueckFreeLPEdit.Text := aquery.FieldByName('FREE_LP_COUNT').AsString;
      if RueckFreeLPEdit.Text = '-1' then
      begin
        RueckFreeLPEdit.Text := '';
      end;
      if aquery.FieldByName('RUECK_STATUS').AsString = 'AKT' then
      begin
        RueckCheckBox.Checked := True;
        RueckLBComboBox.Enabled := True;
        RueckTriggerBedingungRadioGroup.Enabled	:= True;
        if RueckFuellgradEdit.Text <> '' then
        begin
          RueckTriggerBedingungRadioGroup.ItemIndex	:= 0;
          RueckFuellgradEdit.Enabled := True;
        end
        else
        begin
          RueckTriggerBedingungRadioGroup.ItemIndex	:= 1;
          RueckFreeLPEdit.Enabled := True;
        end;

      end;
    end;
  end;
var
qText : string;
begin
  qText := aquery.SQL.Text;
  aquery.SQL.Text := 'SELECT * FROM V_LB WHERE REF=:ref';
  aquery.Parameters.ParamByName('ref').Value := RefLB;

  UpdateBereicheComboBox(self, self.fAktLagerRef, editform.RueckLBComboBox, False);

  try
    aquery.Open;
    if aquery.RecordCount > 0 then
    begin
      if editform.AutoInventurTabSheet.TabVisible then
      begin
        prepareAutoInv;
      end;
      if editform.RuecklagerungTabSheet.TabVisible then
      begin
        prepareRuecklagerung;
      end;
      //TODO --- ABC-Klassifizierung ---
    end;
  finally
    aquery.Close;
    aquery.SQL.Text := qText;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TLagerTopologieForm.StringGrid2DblClick(Sender: TObject);
var
  res,
  ref,
  idx,
  fmin,
  fmax,
  prio,
  ltref,
  folge,
  maxar,
  lagerref    : Integer;
  lpch,
  selstr,
  autoin      : Char;
  weoptstr,
  kommoptstr,
  storeoptstr : String;
  editlbform  : TEditLagerbereichForm;
  query       : TADOQuery;
begin
  if (LBQuery.Active) and (LBQuery.RecNo > 0) then begin
    query := TADOQuery.Create (Self);

    try
      query.LockType := ltReadOnly;
      query.Connection := LVSDatenModul.MainADOConnection;

      query.SQL.Add ('select * from V_LB where REF=:ref');
      query.Parameters.ParamByName('ref').Value := LBQuery.FieldByName ('REF').AsInteger;

      query.Open;

      if query.FieldByName ('REF').IsNull then
        query.Close
      else begin
        ref      := query.FieldByName ('REF').AsInteger;
        lagerref := query.FieldByName ('REF_LAGER').AsInteger;

        editlbform := TEditLagerbereichForm.Create (Self);

        with editlbform do begin
          Prepare(lagerref, ref);

          if (fBereichTabIndex <> -1) Then
            PageControl1.ActivePageIndex := fBereichTabIndex;

          Caption := GetResourceText (1271);

          if (query.FieldByName ('WE_OPT').IsNull) Then
            LTAnnahmeRadioGroup.ItemIndex := 2
          else if (GetOpt (query.FieldByName ('WE_OPT').AsString, 1) = '0') then
            LTAnnahmeRadioGroup.ItemIndex := 2
          else if (GetOpt (query.FieldByName ('WE_OPT').AsString, 1) = '1') then
            LTAnnahmeRadioGroup.ItemIndex := 3
          else if (GetOpt (query.FieldByName ('WE_OPT').AsString, 1) = '2') then
            LTAnnahmeRadioGroup.ItemIndex := 1
          else if (GetOpt (query.FieldByName ('WE_OPT').AsString, 1) = '3') then
            LTAnnahmeRadioGroup.ItemIndex := 0
          else if (GetOpt (query.FieldByName ('WE_OPT').AsString, 1) = '4') then
            LTAnnahmeRadioGroup.ItemIndex := 4
          else
            LTAnnahmeRadioGroup.ItemIndex := 2;

          MDEDirectStoreCheckBox.Checked := (GetOpt (query.FieldByName ('WE_OPT').AsString, 2) = '1');
          SelWELPCheckBox.Checked        := (GetOpt (query.FieldByName ('WE_OPT').AsString, 3) = '1');
          RetLEOptCheckBox.Checked       := (GetOpt (query.FieldByName ('WE_OPT').AsString, 6) = '1');

          if PrjComboBox.Visible then begin
            if not PrjComboBox.Enabled then
              PrjComboBox.ItemIndex := -1
            else if (query.FieldByName ('REF_ACO').IsNull) Then
              PrjComboBox.ItemIndex := 0
            else PrjComboBox.ItemIndex := FindComboboxRef(PrjComboBox, query.FieldByName ('REF_ACO').AsInteger);
          end;

          if ACOBestandComboBox.Visible then begin
            if not ACOBestandComboBox.Enabled then
              ACOBestandComboBox.ItemIndex := -1
            else if (query.FieldByName ('REF_ACO_BESTAND').IsNull) Then
              ACOBestandComboBox.ItemIndex := 0
            else ACOBestandComboBox.ItemIndex := FindComboboxRef(ACOBestandComboBox, query.FieldByName ('REF_ACO_BESTAND').AsInteger);
          end;

          if (query.FieldByName ('REF_AUTO_LT_TYPE').IsNull) Then
            LTComboBox.ItemIndex := 0
          else LTComboBox.ItemIndex := FindComboboxRef(LTComboBox, query.FieldByName ('REF_AUTO_LT_TYPE').AsInteger);

          if (query.FieldByName ('AUTO_EINLAGERUNG').AsString = '1') then
            AutoStoreComboBox.ItemIndex := 1
          else if (query.FieldByName ('AUTO_EINLAGERUNG').AsString = '2') then
            AutoStoreComboBox.ItemIndex := 3
          else if (query.FieldByName ('AUTO_EINLAGERUNG').AsString = '3') then
            AutoStoreComboBox.ItemIndex := 2
          else
            AutoStoreComboBox.ItemIndex := 0;

          if (query.FieldByName ('NACHSCHUB_ART').IsNull) Then
            NachArtComboBox.ItemIndex := 0
          else NachArtComboBox.ItemIndex := FindComboboxDBItem (NachArtComboBox, query.FieldByName ('NACHSCHUB_ART').AsString);
            NachArtComboBox.Items.Insert (0, '');

          AutoNachCheckBox.Checked := query.FieldByName ('AUTO_NACHSCHUB').AsString = '1';

          if (query.FieldByName ('MIN_NACH_FAKTOR').IsNull) then
            MinFaktorEdit.Text := ''
          else
            MinFaktorEdit.Text := IntToStr (query.FieldByName ('MIN_NACH_FAKTOR').AsInteger);

          if (query.FieldByName ('MIN_NACH_EINHEIT').IsNull) then
            MinNachComboBox.ItemIndex := -1
          else
            MinNachComboBox.ItemIndex := MinNachComboBox.Items.IndexOf (query.FieldByName ('MIN_NACH_EINHEIT').AsString);

          if (query.FieldByName ('MAX_NACH_FAKTOR').IsNull) then
            MaxFaktorEdit.Text := ''
          else
            MaxFaktorEdit.Text := IntToStr (query.FieldByName ('MAX_NACH_FAKTOR').AsInteger);

          if (query.FieldByName ('MAX_NACH_EINHEIT').IsNull) then
            MaxNachComboBox.ItemIndex := -1
          else
            MaxNachComboBox.ItemIndex := MaxNachComboBox.Items.IndexOf (query.FieldByName ('MAX_NACH_EINHEIT').AsString);

          NameEdit.Text         := query.FieldByName ('NAME').AsString;
          KommNameEdit.Text     := query.FieldByName ('KOMM_NAME').AsString;
          ShortNameEdit.Text    := query.FieldByName ('SHORT_NAME').AsString;
          BeschreibungEdit.Text := query.FieldByName ('BESCHREIBUNG').AsString;
          ArtComboBox.ItemIndex := FindComboboxDBItemWert (ArtComboBox, query.FieldByName ('LB_ART').AsString);

          if PrjComboBox.Visible then begin
            if not PrjComboBox.Enabled then
              PrjComboBox.ItemIndex := -1
            else if (query.FieldByName ('REF_ACO').IsNull) Then
              PrjComboBox.ItemIndex := 0
            else PrjComboBox.ItemIndex := FindComboboxRef(PrjComboBox, query.FieldByName ('REF_ACO').AsInteger);
          end;

          if ACOBestandComboBox.Visible then begin
            if not ACOBestandComboBox.Enabled then
              ACOBestandComboBox.ItemIndex := -1
            else if (query.FieldByName ('REF_ACO_BESTAND').IsNull) Then
              ACOBestandComboBox.ItemIndex := 0
            else ACOBestandComboBox.ItemIndex := FindComboboxRef(ACOBestandComboBox, query.FieldByName ('REF_ACO_BESTAND').AsInteger);
          end;

          UseForVorratCheckBox.Checked := CheckOpt (query.FieldByName ('USE_FOR_VORRAT').AsString, 1);
          UseForKommCheckBox.Checked   := CheckOpt (query.FieldByName ('USE_FOR_KOMM').AsString, 1);

          KommArtComboBox.ItemIndex    := FindComboboxDBItemWert (KommArtComboBox, query.FieldByName ('KOMM_ART').AsString);
          KommArtComboBox.OnChange (Self);

          KommAblaufComboBox.ItemIndex := FindComboboxDBItemWert (KommAblaufComboBox, query.FieldByName ('KOMM_ABLAUF').AsString);

          if (query.FieldByName ('KOMM_FREIGABE').IsNull) then
            KommFreigabeComboBox.ItemIndex := 0
          else if (query.FieldByName ('KOMM_FREIGABE').AsInteger < KommFreigabeComboBox.Items.Count) then
            KommFreigabeComboBox.ItemIndex := query.FieldByName ('KOMM_FREIGABE').AsInteger
          else
            KommFreigabeComboBox.ItemIndex := 0;

          ABCComboBox.ItemIndex := ABCComboBox.Items.IndexOf (query.FieldByName ('ABC_KLASSE').AsString);
          if (ABCComboBox.ItemIndex = -1) then ABCComboBox.ItemIndex := 0;

          if (query.FieldByName ('KOMM_ABLAUF').AsString = 'AR') then begin
            if (query.FieldByName ('REF_AR_KOMM_GRUPPE').IsNull) then
              KommGrpComboBox.ItemIndex := 0
            else begin
              KommGrpComboBox.ItemIndex := FindComboboxRef(KommGrpComboBox, query.FieldByName ('REF_AR_KOMM_GRUPPE').AsInteger);

              if (KommGrpComboBox.ItemIndex = -1) then KommGrpComboBox.ItemIndex := 0;
            end;
          end else begin
            if (query.FieldByName ('REF_KOMM_PLAN_GRP').IsNull) then
              KommGrpComboBox.ItemIndex := 0
            else begin
              KommGrpComboBox.ItemIndex := FindComboboxRef(KommGrpComboBox, query.FieldByName ('REF_KOMM_PLAN_GRP').AsInteger);

              if (KommGrpComboBox.ItemIndex = -1) then KommGrpComboBox.ItemIndex := 0;
            end;
          end;


          if (query.FieldByName ('KOMM_FOLGE').IsNull) Then
            KommFolgeUpDown.Position := -1
          else
            KommFolgeUpDown.Position := query.FieldByName ('KOMM_FOLGE').AsInteger;

          RBGEdit.Text := query.FieldByName ('RBG_SYSTEM').AsString;

          if (query.FieldByName ('EINLAGERUNG_PRIO').IsNull) then
            StorePrioUpDown.Position := -1
          else
            StorePrioUpDown.Position := query.FieldByName ('EINLAGERUNG_PRIO').AsInteger;

          StoreResCheckBox.Checked         := CheckOpt (query.FieldByName ('EINLAGERUNG_OPT').AsString, 1);
          StoreDistributeCheckBox.Checked  := CheckOpt (query.FieldByName ('EINLAGERUNG_OPT').AsString, 2);
          StoreSingleMatCheckBox.Checked   := CheckOpt (query.FieldByName ('EINLAGERUNG_OPT').AsString, 3);
          StoreAddMatCheckBox.Checked      := CheckOpt (query.FieldByName ('EINLAGERUNG_OPT').AsString, 4);
          StoreDistrRegalCheckBox.Checked  := CheckOpt (query.FieldByName ('EINLAGERUNG_OPT').AsString, 5);
          StoreChargenCheckBox.Checked     := CheckOpt (query.FieldByName ('EINLAGERUNG_OPT').AsString, 6);
          StoreMHDReinCheckBox.Checked     := CheckOpt (query.FieldByName ('EINLAGERUNG_OPT').AsString, 7);
          AutoWECheckBox.Checked           := CheckOpt (query.FieldByName ('EINLAGERUNG_OPT').AsString, 8);
          OptWEAKTCheckBox.Checked         := CheckOpt (query.FieldByName ('EINLAGERUNG_OPT').AsString, 9);
          MergeBesCheckBox.Checked         := CheckOpt (query.FieldByName ('EINLAGERUNG_OPT').AsString, 10);
          TeilNachschubCheckBox.Checked    := CheckOpt (query.FieldByName ('EINLAGERUNG_OPT').AsString, 11);
          RetourenSortCheckBox.Checked     := CheckOpt (query.FieldByName ('EINLAGERUNG_OPT').AsString, 16);
          NotifyCheckBox.Checked           := CheckOpt (query.FieldByName ('EINLAGERUNG_OPT').AsString, 17);

          VollPALCheckBox.Checked         := CheckOpt (query.FieldByName ('KOMM_OPT').AsString, 1);
          NVEChangeVolCheckBox.Checked    := CheckOpt (query.FieldByName ('KOMM_OPT').AsString, 2);
          NVEChangeGwCheckBox.Checked     := CheckOpt (query.FieldByName ('KOMM_OPT').AsString, 3);
          NVEChangePALCheckBox.Checked    := CheckOpt (query.FieldByName ('KOMM_OPT').AsString, 4);
          KommOptCheckBox.Checked         := CheckOpt (query.FieldByName ('KOMM_OPT').AsString, 5);
          DistributePicksCheckBox.Checked := CheckOpt (query.FieldByName ('KOMM_OPT').AsString, 13);
          UseNullInvCheckBox.State        := CheckOptState (query.FieldByName ('KOMM_OPT').AsString, 14);

          AutoLEDeleteCheckBox.Checked := query.FieldByName ('AUTO_EMPTY_LE_REMOVE').AsString = '1';

          if query.FieldByName ('OPT_SHOW_MDE_LP_NR').IsNull then
            LPShowRadioGroup.ItemIndex := -1
          else if (query.FieldByName ('OPT_SHOW_MDE_LP_NR').AsString = '1') then
            LPShowRadioGroup.ItemIndex := 1
          else if (query.FieldByName ('OPT_SHOW_MDE_LP_NR').AsString = '2') then
            LPShowRadioGroup.ItemIndex := 2
          else if (query.FieldByName ('OPT_SHOW_MDE_LP_NR').AsString = '3') then
            LPShowRadioGroup.ItemIndex := 3
          else
            LPShowRadioGroup.ItemIndex := 0;

          if query.FieldByName ('MAX_DIFF_AR').IsNull then
            MaxDiffArEdit.Text := ''
          else
            MaxDiffArEdit.Text := IntToStr (query.FieldByName ('MAX_DIFF_AR').AsInteger);

          if not Assigned (query.FindField ('OPT_MIX_CATEGORY')) then
            StoreMixCategoryCheckBox.Visible := False
          else
            StoreMixCategoryCheckBox.Checked := (query.FieldByName ('OPT_MIX_CATEGORY').AsString = '1');

          if not Assigned (query.FindField ('OPT_LE_EINLAGERN')) then
            StoreSelectionRadioGroup.Visible := False
          else begin
            if (copy (query.FieldByName ('OPT_LE_EINLAGERN').AsString, 1, 1) = '0') then
              StoreSelectionRadioGroup.ItemIndex := 0
            else if (copy (query.FieldByName ('OPT_LE_EINLAGERN').AsString, 1, 1) = '1') then
              StoreSelectionRadioGroup.ItemIndex := 1
            else if (copy (query.FieldByName ('OPT_LE_EINLAGERN').AsString, 1, 1) = '2') then
              StoreSelectionRadioGroup.ItemIndex := 2;
          end;

          if (ZollTabSheet.TabVisible) then begin
            if Assigned (query.FindField ('OPT_UNVERZOLLT')) then
              ZollBereichCheckBox.Checked := (query.FieldByName ('OPT_UNVERZOLLT').AsString = '1')
            else
              ZollBereichCheckBox.Enabled := False;
          end else begin
            ZollBereichCheckBox.Enabled := False;
          end;

          prepareHousekeeping(query, editlbform, ref);

          query.Close;

          if (TransportTypeCheckListBox.Visible) then begin
            query.SQL.Clear;
            query.SQL.Add ('select REF_TRANSPORT_TYPE from V_TRANSPORT_TYPE_REL_LB where REF_LB=:ref');
            query.Parameters.ParamByName('ref').Value := LBQuery.FieldByName ('REF').AsInteger;

            query.Open;

            while not (query.Eof) do begin
              idx := FindListBoxRef (TransportTypeCheckListBox, query.Fields [0].AsInteger);

              if (idx <> -1) then
                TransportTypeCheckListBox.Checked [idx] := True;

              query.Next;
            end;

            query.Close;
          end;

          if (NachsTabSheet.TabVisible) then begin
            if (NachschubBereichListBox.Visible) then begin
              query.SQL.Clear;

              if LVSDatenModul.ViewExits ('V_ARTIKEL_REL_NACHSCHUB') then begin
                query.SQL.Add ('select lb.REF, lb.NAME, rel.STATUS from V_LB lb left outer join V_LAGER_LB_REL_NACHSCHUB rel on (rel.REF_LB=:ref_lb and rel.REF_NACHSCHUB_LB=lb.REF) where lb.REF<>:ref and lb.REF_LAGER=:ref_lager order by rel.PRIO nulls last');
                query.Parameters.ParamByName ('ref').Value := ref;
              end else begin
                query.SQL.Add ('select lb.REF, lb.NAME, rel.STATUS from V_LB lb left outer join V_LAGER_LB_REL_NACHSCHUB rel on (rel.REF_LB=:ref_lb and rel.REF_NACHSCHUB_LB=lb.REF) where lb.USE_FOR_VORRAT=''1'' and lb.REF_LAGER=:ref_lager order by rel.PRIO nulls last');
              end;

              query.Parameters.ParamByName ('ref_lager').Value := lagerref;
              query.Parameters.ParamByName ('ref_lb').Value := ref;

              try
                query.Open;

                while not (query.EOF) do begin
                  idx := NachschubBereichListBox.Items.AddObject (query.Fields [1].AsString, TComboBoxRef.Create (query.Fields [0].AsInteger));

                  if (query.Fields [2].AsString = 'AKT') then
                    NachschubBereichListBox.Checked [idx] := True;

                  query.Next;
                end;

                query.Close;
              except
                NachschubBereichListBox.Visible := False;
              end;
            end;
          end;
        end;

        if (editlbform.ShowModal = mrOk) Then begin
          fBereichTabIndex := editlbform.PageControl1.ActivePageIndex;

          LBDBGrid.Reload
        end;

        editlbform.Free;
      end;
    finally
      query.Free;
    end;
  end;


  LBDBGrid.SetFocus;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TLagerTopologieForm.LBNeuButtonClick(Sender: TObject);
var
  res,
  ref,
  idx,
  fmin,
  fmax,
  prio,
  ltref,
  folge,
  maxar       : Integer;
  lpch,
  selstr,
  autoin      : Char;
  weoptstr,
  kommoptstr,
  storeoptstr : String;
  query       : TADOQuery;
  editlbform  : TEditLagerbereichForm;
begin
  if (GetComboBoxRef (LagerComboBox1) <> -1) then begin
    editlbform := TEditLagerbereichForm.Create (Self);

    with editlbform do begin
      query := TADOQuery.Create (Self);

      try
        query.LockType := ltReadOnly;
        query.Connection := LVSDatenModul.MainADOConnection;

        if (fBereichTabIndex <> -1) Then
          PageControl1.ActivePageIndex := fBereichTabIndex;

        Caption := GetResourceText (1272);

        Prepare (LBQuery.FieldByName ('REF_LAGER').AsInteger, -1);

        //Standardwerte aus dem Lager übernehmen
        query.SQL.Clear;
        query.SQL.Add ('select * from V_LAGER where REF=:ref');
        query.Parameters.ParamByName('ref').Value := GetComboBoxRef (LagerComboBox1);

        query.Open;

        VollPALCheckBox.Checked         := CheckOpt (query.FieldByName('KOMM_OPT').AsString, 1);
        NVEChangePALCheckBox.Checked    := CheckOpt (query.FieldByName('KOMM_OPT').AsString, 2);
        KommOptCheckBox.Checked         := CheckOpt (query.FieldByName('KOMM_OPT').AsString, 5);
        DistributePicksCheckBox.Checked := CheckOpt (query.FieldByName('KOMM_OPT').AsString, 13);

        query.Close;

        prepareHousekeeping(query, editlbform, -1);

        query.Close;
      finally

      end;
    end;

    if (editlbform.ShowModal = mrOk) Then begin
      fBereichTabIndex := editlbform.PageControl1.ActivePageIndex;

      if (res <> 0) Then
        FrontendMessages.MessageDlg (FormatMessageText (1244, [LVSDatenModul.LastLVSErrorText]), mtError, [mbOK], 0)
      else
        LBDBGrid.Reload (editlbform.RefLB);
    end;

    editlbform.Free;
  end;

  LBDBGrid.SetFocus;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TLagerTopologieForm.LBZoneNewButtonClick(Sender: TObject);
var
  res,
  ref      : Integer;
  editform : TEditLBZoneForm;
begin
  if (LBQuery.Active) and (LBQuery.RecNo > 0) then begin
    editform := TEditLBZoneForm.Create (Self);

    editform.LBLabel.Caption := LBQuery.FieldByName ('NAME').AsString;
    editform.Caption := GetResourceText (1322);

    if (editform.ShowModal = mrOk) then begin
      res := CreateLBZone (LBQuery.FieldByName ('REF').AsInteger, editform.KommFolgeUpDown.Position, editform.NameEdit.Text, editform.NummerEdit.Text, editform.DescEdit.Text, editform.ABCCombobox.Text, ref);

      if (res <> 0) Then
        FrontendMessages.MessageDlg ('Fehler beim Anlegen einer neuen Lagerbereicheszone'+#13+#13+LVSDatenModul.LastLVSErrorText, mtError, [mbOK], 0)
      else
        LBZoneDBGrid.Reload (ref);
    end;

    editform.Release;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TLagerTopologieForm.LBZoneDataSourceDataChange(Sender: TObject; Field: TField);
begin
  LBZOneEditButton.Enabled  := False;
  LBZoneDelButton.Enabled   := False;

  if (LBZoneQuery.Active) and (LBZoneQuery.RecNo > 0) then begin
    LBZOneEditButton.Enabled  := True;
    LBZoneDelButton.Enabled   := True;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TLagerTopologieForm.LBZoneDBGridPopupMenuPopup(Sender: TObject);
begin
  PrintLBZonenLabelMenuItem.Enabled := False;

  if (LBZoneQuery.Active) and (LBZoneQuery.RecNo > 0) then begin
    PrintLBZonenLabelMenuItem.Enabled := not (LBZoneQuery.FieldByName ('ZONEN_NR').IsNull);
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TLagerTopologieForm.LBZoneDelButtonClick(Sender: TObject);
var
  res : Integer;
begin
  if (LBZoneQuery.Active) and (LBZoneQuery.RecNo > 0) then begin
    if (FrontendMessages.MessageDlg(FormatMessageText (1123, []), mtConfirmation, [mbYes,mbNo,mbCancel], 0) = mrYes) then begin
      res := DeleteLBZone (LBZoneQuery.FieldByName ('REF').AsInteger);

      if (res <> 0) Then
        FrontendMessages.MessageDlg ('Fehler beim Löschen der Lagerbereicheszone'+#13+#13+LVSDatenModul.LastLVSErrorText, mtError, [mbOK], 0)
      else LBZoneDBGrid.Reload;
    end;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TLagerTopologieForm.LBZOneEditButtonClick(Sender: TObject);
var
  res      : Integer;
  editform : TEditLBZoneForm;
begin
  if (LBZoneQuery.Active) and (LBZoneQuery.RecNo > 0) then begin
    editform := TEditLBZoneForm.Create (Self);

    editform.LBLabel.Caption := LBQuery.FieldByName ('NAME').AsString;

    editform.Caption := GetResourceText (1321);

    editform.NameEdit.Text   := LBZoneQuery.FieldByName ('NAME').AsString;
    editform.NummerEdit.Text := LBZoneQuery.FieldByName ('ZONEN_NR').AsString;
    editform.DescEdit.Text   := LBZoneQuery.FieldByName ('BESCHREIBUNG').AsString;

    if (LBZoneQuery.FieldByName ('KOMM_FOLGE').IsNull) Then
      editform.KommFolgeUpDown.Position := -1
    else
      editform.KommFolgeUpDown.Position := LBZoneQuery.FieldByName ('KOMM_FOLGE').AsInteger;

    editform.ABCComboBox.ItemIndex := editform.ABCComboBox.Items.IndexOf (LBZoneQuery.FieldByName ('ABC_KLASSE').AsString);
    if (editform.ABCComboBox.ItemIndex = -1) then editform.ABCComboBox.ItemIndex := 0;

    if (editform.ShowModal = mrOk) then begin
      res := ChangeLBZone (LBZoneQuery.FieldByName ('REF').AsInteger, editform.KommFolgeUpDown.Position, editform.NameEdit.Text, editform.NummerEdit.Text, editform.DescEdit.Text, editform.ABCCombobox.Text);

      if (res <> 0) Then
        FrontendMessages.MessageDlg ('Fehler beim Ändern der Lagerbereicheszone'+#13+#13+LVSDatenModul.LastLVSErrorText, mtError, [mbOK], 0)
      else
        LBZoneDBGrid.Reload;
    end;

    editform.Release;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TLagerTopologieForm.LPTabSheetShow(Sender: TObject);
begin
  LoadLagerCombobox (LagerComboBox2, LVSDatenModul.AktLocation);

  LagerComboBox2.ItemIndex := FindComboboxRef(LagerComboBox2, fAktLagerRef );

  if (LagerComboBox2.ItemIndex = -1) then begin
    if (Length (LVSDatenModul.AktLager) > 0) then
      LagerComboBox2.ItemIndex := LagerComboBox2.IndexOf(LVSDatenModul.AktLager)
    else if (LagerComboBox2.Items.Count > 0) Then
      LagerComboBox2.ItemIndex := 0
    else LagerComboBox2.ItemIndex := -1
  end;

  if (LagerComboBox2.ItemIndex <> -1) then begin
    UpdateBereicheComboBox (Sender, GetComboBoxRef (LagerComboBox2), LBComboBox1, False);

    if (fAktBereichRef <> -1) then
      LBComboBox1.ItemIndex := FindComboboxRef(LBComboBox1, fAktBereichRef );

    LBComboBoxChange (LBComboBox1);
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TLagerTopologieForm.UpdateBereicheComboBox (Sender: TObject; const LagerRef : Integer; LBComboBox : TComboBoxPro; const UpdateFlag : Boolean);
var
  reflb   : Integer;
  bereich : String;
begin
  reflb   := GetComboBoxRef (LBComboBox);

  bereich := LBComboBox.Text;

  ADOQuery1.SQL.Clear;
  ADOQuery1.SQL.Add ('select REF, NAME, BESCHREIBUNG from V_LB where REF_LAGER=:ref');;
  ADOQuery1.Parameters [0].Value := LagerRef;

  if (Sender = LagerCombobox3) then
    ADOQuery1.SQL.Add ('and USE_FOR_KOMM=''1'' and LB_ART not in (''WE'',''WA'',''SPERR'')');

  ADOQuery1.SQL.Add ('order by NAME');

  LBComboBox.Clear;
  LBComboBox.Items.Add ('');

  ADOQuery1.Open;
  while not (ADOQuery1.Eof) do begin
    LBComboBox.Items.AddObject (ADOQuery1.Fields [1].AsString+'|'+ADOQuery1.Fields [2].AsString, TComboBoxRef.Create (ADOQuery1.Fields [0].AsInteger));

    ADOQuery1.Next;
  end;
  ADOQuery1.Close;

  if (reflb <= 0) then
    LBComboBox.ItemIndex := 0
  else begin
    LBComboBox.ItemIndex := FindComboboxRef (LBComboBox, reflb);
    if (LBComboBox.ItemIndex = -1) then LBComboBox.ItemIndex := 0;
  end;

  (*
  LBComboBox.ItemIndex := LBComboBox.Items.IndexOf (bereich);
  if (LBComboBox.ItemIndex = -1) then
    LBComboBox.ItemIndex := 0;
  *)

  if (UpdateFlag) Then
    LBComboBoxChange (LBComboBox);
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TLagerTopologieForm.LagerComboBox2Change(Sender: TObject);
begin
  if (LagerComboBox2.ItemIndex >= 0) then
    UpdateBereicheComboBox (Sender, GetComboBoxRef (LagerComboBox2), LBComboBox1, True)
  else UpdateLPQuery (GetComboBoxRef (LagerComboBox2), -1);
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TLagerTopologieForm.LagerComboBox3Change(Sender: TObject);
begin
  if (LagerComboBox3.ItemIndex >= 0) then
    UpdateBereicheComboBox (Sender, GetComboBoxRef (LagerComboBox3), LBComboBox2, True)
  else UpdateKommQuery (GetComboBoxRef (LagerComboBox3), -1);
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TLagerTopologieForm.LagerCopyButtonClick(Sender: TObject);
var
  ref,
  res,
  orgref   : Integer;
  locstr,
  optstr   : String;
  copyform : TCopyLagerForm;
begin
  if Assigned (LagerStringGrid.Rows [LagerStringGrid.Row].Objects [0]) then begin
    orgref := TGridRef (LagerStringGrid.Rows [LagerStringGrid.Row].Objects [0]).Ref;

    copyform := TCopyLagerForm.Create (Self);
    copyform.Caption := FormatResourceText (1320, [LagerStringGrid.Cells [2, LagerStringGrid.Row]]);

    copyform.SourceNameLabel.Caption := LagerStringGrid.Cells [2, LagerStringGrid.Row];

    copyform.LocComboBox.Clear;
    copyform.LocComboBox.Items.Add (GetResourceText (1319));

    ADOQuery1.SQL.Clear;
    ADOQuery1.SQL.Add ('select REF, NAME, BESCHREIBUNG from V_PCD_LOCATION order by NAME');

    ADOQuery1.Open;
    while not (ADOQuery1.Eof) do begin
      copyform.LocComboBox.Items.AddObject (ADOQuery1.Fields [1].AsString+'|'+ADOQuery1.Fields [2].AsString, TComboBoxRef.Create (ADOQuery1.Fields [0].AsInteger));

      ADOQuery1.Next;
    end;
    ADOQuery1.Close;

    if (fAktLocationRef <> -1) then
      copyform.LocComboBox.ItemIndex := FindComboboxRef(copyform.LocComboBox, fAktLocationRef)
    else if (LVSDatenModul.AktLocationRef <> -1) then
      copyform.LocComboBox.ItemIndex := FindComboboxRef(copyform.LocComboBox, LVSDatenModul.AktLocationRef);

    if (copyform.LocComboBox.ItemIndex = -1) then copyform.LocComboBox.ItemIndex := 0;


    if (copyform.ShowModal = mrOk) then begin
      if (copyform.LocComboBox.ItemIndex = 0) then
        locstr := ''
      else
        locstr := copyform.LocComboBox.GetItemText;

      if (not copyform.CopySpedCheckBox.Checked and (Length (copyform.BetriebEdit.Text) = 0) and (Length (copyform.ILNEdit.Text) = 0)) then
        res := CopyLager (orgref,
                          locstr,
                          copyform.NameEdit.Text,
                          copyform.BeschreibungEdit.Text,
                          copyform.CopyACOCheckBox.Checked,
                          copyform.CopyStructCheckBox.Checked,
                          copyform.CopyZuordnungCheckBox.Checked,
                          ref)
      else begin
        optstr := '00000000';

        if copyform.CopyACOCheckBox.Checked then
          optstr [1] := '1';

        if copyform.CopyStructCheckBox.Checked then
          optstr [2] := '1';

        if copyform.CopyZuordnungCheckBox.Checked then
          optstr [3] := '1';

        if copyform.CopySpedCheckBox.Checked then
          optstr [4] := '1';

        res := CopyLager (orgref,
                          locstr,
                          copyform.NameEdit.Text,
                          copyform.BeschreibungEdit.Text,
                          copyform.BetriebEdit.Text,
                          copyform.ILNEdit.Text,
                          optstr,
                          ref)
      end;

      if (res = 0) and (LVSDatenModul.AktMandantRef > 0) Then
        res := DefMandantLager (LVSDatenModul.AktMandantRef, ref, '1');

      if (res = 0) then
        res := ChangeLagerDaten (ref)
      else
        FrontendMessages.MessageDlg (FormatMessageText (1245, [LVSDatenModul.LastLVSErrorText]), mtError, [mbOK], 0);
    end;

    copyform.Release;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TLagerTopologieForm.UpdateKommQuery (const RefLager, RefBereich : Integer);
begin
  ClearListBoxObjects (LPListBox);

  ADOQuery1.SQL.Clear;
  ADOQuery1.SQL.Add ('select REF, LP_NR, NAME from V_LP where REF_LB not in (select REF from V_LB where LB_ART in (''WE'',''WA'',''SPERR'')) and REF_LAGER=:ref_lager');
  ADOQuery1.Parameters.ParamByName('ref_lager').Value := RefLager;

  if (RefBereich <> -1) then begin
    ADOQuery1.SQL.Add (' and REF_LB=:ref_lb');
    ADOQuery1.Parameters.ParamByName('ref_lb').Value := RefBereich;
  end;

  ADOQuery1.SQL.Add ('and REF_KOMM_LP is null order by LP_NR');

  Screen.Cursor := crSQLWait;

  try
    LPListBox.Items.BeginUpdate;

    try
      try
        ADOQuery1.Open;

        while not (ADOQuery1.Eof) do begin
          LPListBox.Items.AddObject (ADOQuery1.Fields [1].AsString + '|' + ADOQuery1.Fields [2].AsString, TListBoxRef.Create(ADOQuery1.Fields [0].AsInteger));

          ADOQuery1.Next;
        end;
        ADOQuery1.Close;
      except
        on e: Exception do begin
          ErrorTrackingModule.WriteErrorLog ('TLagerTopologieForm.UpdateKommQuery', e.ClassName + ' : ' + e.Message);
        end;
      end;
    finally
      LPListBox.Items.EndUpdate;
    end;

    KommADOQuery.Close;
    KommADOQuery.SQL.Clear;
    KommADOQuery.SQL.Add ('select * from V_PCD_KOMM_LP_ZUORDNUNG where REF_LAGER=:ref_lager');
    KommADOQuery.Parameters.ParamByName('ref_lager').Value := RefLager;

    if (RefBereich <> -1) then begin
      KommADOQuery.SQL.Add ('and REF_LB=:ref_lb');
      KommADOQuery.Parameters.ParamByName('ref_lb').Value := RefBereich;
    end;

    try
      KommADOQuery.Open;

      KommDBGrid.SetColumnVisible ('LAGER', (RefLager = -1));
      KommDBGrid.SetColumnVisible ('LT_COUNT', False);
    except
      on e: Exception do begin
        ErrorTrackingModule.WriteErrorLog ('TLagerTopologieForm.UpdateKommQuery', e.ClassName + ' : ' + e.Message);
      end;
    end;
  finally
    Screen.Cursor := crDefault;
  end;

  KommDBGrid.SetFocus;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TLagerTopologieForm.UpdateLPQuery (const RefLager, RefBereich : Integer);
begin
  LPOraQuery.Close;

  LPOraQuery.SQL.Clear;
  LPOraQuery.SQL.Add ('select lp.* from V_LP lp where lp.REF_LAGER=:ref_lager and (:ref_lb is null or (lp.REF_LB=:ref_lb))');

  if LPNotUniqueMenuItem.Checked then
    LPOraQuery.SQL.Add ('and (select count (q.REF) from V_LP q where q.REF_LAGER=lp.REF_LAGER and q.REIHE=lp.REIHE and q.FELD=lp.FELD and q.EBENE=lp.EBENE and q.PLATZ=lp.PLATZ) > 1');

  LPOraQuery.ParamByName ('ref_lager').Value   := RefLager;
  LPOraQuery.ParamByName ('ref_lb').Value      := GetPLSQLParameter (RefBereich);

  LPOraQuery.SQLRefresh.Clear;
  LPOraQuery.SQLRefresh.Add ('select * from V_LP where REF=:ref');

  with LPDBGrid do begin
    if (SortColumns [0].ColumnIndex = -1) then
      SetSortColumn (0, 'FOLGE_NR');
  end;

  Screen.Cursor := crSQLWait;

  try
    LPOraQuery.Open;
  except
  end;

  Screen.Cursor := crDefault;

  if not (LVSConfigModul.UseLPTiefe) then
    LPDBGrid.SetColumnVisible('TIEFE', False);

  LPDBGrid.SetColumnVisible('LAGER', False);
  LPDBGrid.SetColumnVisible('OPT_BESTAND', False);
  LPDBGrid.SetColumnVisible('HOEHEN_KLASSE', False);
  LPDBGrid.SetColumnVisible('OPT_SCANABLE', False);

  LPDBGrid.SetFocus;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TLagerTopologieForm.LBComboBoxChange(Sender: TObject);
begin
  if (Sender = LBComboBox2) then begin
    if (LBComboBox2.ItemIndex > 0) Then
      UpdateKommQuery (GetComboBoxRef (LagerComboBox3), GetComboBoxRef (LBComboBox2))
    else UpdateKommQuery (GetComboBoxRef (LagerComboBox3), -1);

    KommLPPrintButton.Enabled := (KommADOQuery.Active) and (KommADOQuery.RecNo > 0);
  end else begin
    if (Length (LBComboBox1.Text) = 0) Then begin
      NeuLPButton.Enabled := False;
      NeuLPSerieButton.Enabled := False;
    end else begin
      NeuLPButton.Enabled := True;
      NeuLPSerieButton.Enabled := True;
    end;

    if (LBComboBox1.ItemIndex > 0) Then
      UpdateLPQuery (GetComboBoxRef (LagerComboBox2), GetComboBoxRef (LBComboBox1))
    else UpdateLPQuery (GetComboBoxRef (LagerComboBox2), -1);
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TLagerTopologieForm.LBCopyButtonClick(Sender: TObject);
var
  res,
  ref,
  idx,
  fmin,
  fmax,
  prio,
  ltref,
  folge,
  maxar,
  lagerref    : Integer;
  lpch,
  selstr,
  autoin      : Char;
  abcstr,
  weoptstr,
  kommoptstr,
  storeoptstr : String;
  editlbform  : TEditLagerbereichForm;
  query       : TADOQuery;
begin
  if (LBQuery.Active) and (LBQuery.RecNo > 0) then begin
    query := TADOQuery.Create (Self);

    try
      query.LockType := ltReadOnly;
      query.Connection := LVSDatenModul.MainADOConnection;

      query.SQL.Add ('select * from V_LB where REF=:ref');
      query.Parameters.ParamByName('ref').Value := LBQuery.FieldByName ('REF').AsInteger;

      query.Open;

      if query.FieldByName ('REF').IsNull then
        query.Close
      else begin
        lagerref := query.FieldByName ('REF_LAGER').AsInteger;
        ref := query.FieldByName('REF').AsInteger;

        editlbform := TEditLagerbereichForm.Create (Self);

        with editlbform do begin
          if (fBereichTabIndex <> -1) Then
            PageControl1.ActivePageIndex := fBereichTabIndex;

          Caption := GetResourceText (1272);

          Prepare(lagerref, ref);

          NameEdit.Text             := query.FieldByName ('NAME').AsString;
          BeschreibungEdit.Text     := query.FieldByName ('BESCHREIBUNG').AsString;
          ArtComboBox.ItemIndex     := FindComboboxDBItemWert (ArtComboBox, query.FieldByName ('LB_ART').AsString);

          UseForVorratCheckBox.Checked := CheckOpt (query.FieldByName ('USE_FOR_VORRAT').AsString, 1);
          UseForKommCheckBox.Checked   := CheckOpt (query.FieldByName ('USE_FOR_KOMM').AsString, 1);

          KommArtComboBox.ItemIndex := FindComboboxDBItemWert (KommArtComboBox, query.FieldByName ('KOMM_ART').AsString);
          if (KommArtComboBox.ItemIndex = -1) then KommArtComboBox.ItemIndex := 0;

          if PrjComboBox.Visible then begin
            if not PrjComboBox.Enabled then
              PrjComboBox.ItemIndex := -1
            else if (query.FieldByName ('REF_ACO').IsNull) Then
              PrjComboBox.ItemIndex := 0
            else PrjComboBox.ItemIndex := FindComboboxRef(PrjComboBox, query.FieldByName ('REF_ACO').AsInteger);
          end;

          if ACOBestandComboBox.Visible then begin
            if not ACOBestandComboBox.Enabled then
              ACOBestandComboBox.ItemIndex := -1
            else if (query.FieldByName ('REF_ACO_BESTAND').IsNull) Then
              ACOBestandComboBox.ItemIndex := 0
            else ACOBestandComboBox.ItemIndex := FindComboboxRef(ACOBestandComboBox, query.FieldByName ('REF_ACO_BESTAND').AsInteger);
          end;

          KommAblaufComboBox.ItemIndex := FindComboboxDBItemWert (KommAblaufComboBox, query.FieldByName ('KOMM_ABLAUF').AsString);
          if (KommAblaufComboBox.ItemIndex = -1) then KommAblaufComboBox.ItemIndex := 0;

          if (query.FieldByName ('KOMM_FREIGABE').IsNull) then
            KommFreigabeComboBox.ItemIndex := 0
          else if (query.FieldByName ('KOMM_FREIGABE').AsInteger < KommFreigabeComboBox.Items.Count) then
            KommFreigabeComboBox.ItemIndex := query.FieldByName ('KOMM_FREIGABE').AsInteger
          else
            KommFreigabeComboBox.ItemIndex := 0;

          ABCComboBox.ItemIndex := FindComboboxDBItemWert (ABCComboBox, query.FieldByName ('ABC_KLASSE').AsString);
          if (ABCComboBox.ItemIndex = -1) then ABCComboBox.ItemIndex := 0;

          if (query.FieldByName ('WE_OPT').IsNull) Then
            LTAnnahmeRadioGroup.ItemIndex := 2
          else if (GetOpt (query.FieldByName ('WE_OPT').AsString, 1) = '0') then
            LTAnnahmeRadioGroup.ItemIndex := 2
          else if (GetOpt (query.FieldByName ('WE_OPT').AsString, 1) = '1') then
            LTAnnahmeRadioGroup.ItemIndex := 3
          else if (GetOpt (query.FieldByName ('WE_OPT').AsString, 1) = '2') then
            LTAnnahmeRadioGroup.ItemIndex := 1
          else if (GetOpt (query.FieldByName ('WE_OPT').AsString, 1) = '3') then
            LTAnnahmeRadioGroup.ItemIndex := 0
          else if (GetOpt (query.FieldByName ('WE_OPT').AsString, 1) = '4') then
            LTAnnahmeRadioGroup.ItemIndex := 4
          else
            LTAnnahmeRadioGroup.ItemIndex := 2;

          MDEDirectStoreCheckBox.Checked := (GetOpt (query.FieldByName ('WE_OPT').AsString, 2) = '1');
          SelWELPCheckBox.Checked        := (GetOpt (query.FieldByName ('WE_OPT').AsString, 3) = '1');
          RetLEOptCheckBox.Checked        := (GetOpt (query.FieldByName ('WE_OPT').AsString, 6) = '1');

          if (query.FieldByName ('AUTO_EINLAGERUNG').AsString = '1') then
            AutoStoreComboBox.ItemIndex := 1
          else if (query.FieldByName ('AUTO_EINLAGERUNG').AsString = '2') then
            AutoStoreComboBox.ItemIndex := 2
          else
            AutoStoreComboBox.ItemIndex := 0;

          if not (query.FieldByName ('KOMM_FOLGE').IsNull) Then
            KommFolgeUpDown.Position := query.FieldByName ('KOMM_FOLGE').AsInteger
          else begin
            KommFolgeEdit.Text := '';
            KommFolgeUpDown.Position := 0;
          end;

          RBGEdit.Text := query.FieldByName ('RBG_SYSTEM').AsString;

          StoreResCheckBox.Checked         := CheckOpt (query.FieldByName ('EINLAGERUNG_OPT').AsString, 1);
          StoreDistributeCheckBox.Checked  := CheckOpt (query.FieldByName ('EINLAGERUNG_OPT').AsString, 2);
          StoreSingleMatCheckBox.Checked   := CheckOpt (query.FieldByName ('EINLAGERUNG_OPT').AsString, 3);
          StoreAddMatCheckBox.Checked      := CheckOpt (query.FieldByName ('EINLAGERUNG_OPT').AsString, 4);
          StoreDistrRegalCheckBox.Checked  := CheckOpt (query.FieldByName ('EINLAGERUNG_OPT').AsString, 5);
          StoreChargenCheckBox.Checked     := CheckOpt (query.FieldByName ('EINLAGERUNG_OPT').AsString, 6);
          StoreMHDReinCheckBox.Checked     := CheckOpt (query.FieldByName ('EINLAGERUNG_OPT').AsString, 7);
          AutoWECheckBox.Checked           := CheckOpt (query.FieldByName ('EINLAGERUNG_OPT').AsString, 8);
          OptWEAKTCheckBox.Checked         := CheckOpt (query.FieldByName ('EINLAGERUNG_OPT').AsString, 9);
          MergeBesCheckBox.Checked         := CheckOpt (query.FieldByName ('EINLAGERUNG_OPT').AsString, 10);
          TeilNachschubCheckBox.Checked    := CheckOpt (query.FieldByName ('EINLAGERUNG_OPT').AsString, 11);
          RetourenSortCheckBox.Checked     := CheckOpt (query.FieldByName ('EINLAGERUNG_OPT').AsString, 16);
          NotifyCheckBox.Checked           := CheckOpt (query.FieldByName ('EINLAGERUNG_OPT').AsString, 17);

          VollPALCheckBox.Checked         := CheckOpt (query.FieldByName ('KOMM_OPT').AsString, 1);
          NVEChangeVolCheckBox.Checked    := CheckOpt (query.FieldByName ('KOMM_OPT').AsString, 2);
          NVEChangeGwCheckBox.Checked     := CheckOpt (query.FieldByName ('KOMM_OPT').AsString, 3);
          NVEChangePALCheckBox.Checked    := CheckOpt (query.FieldByName ('KOMM_OPT').AsString, 4);
          KommOptCheckBox.Checked         := CheckOpt (query.FieldByName ('KOMM_OPT').AsString, 5);
          DistributePicksCheckBox.Checked := CheckOpt (query.FieldByName ('KOMM_OPT').AsString, 13);
          UseNullInvCheckBox.State        := CheckOptState (query.FieldByName ('KOMM_OPT').AsString, 14);

          AutoLEDeleteCheckBox.Checked := query.FieldByName ('AUTO_EMPTY_LE_REMOVE').AsString = '1';

          if query.FieldByName ('OPT_SHOW_MDE_LP_NR').IsNull then
            LPShowRadioGroup.ItemIndex := -1
          else if (query.FieldByName ('OPT_SHOW_MDE_LP_NR').AsString = '1') then
            LPShowRadioGroup.ItemIndex := 1
          else if (query.FieldByName ('OPT_SHOW_MDE_LP_NR').AsString = '2') then
            LPShowRadioGroup.ItemIndex := 2
          else if (query.FieldByName ('OPT_SHOW_MDE_LP_NR').AsString = '3') then
            LPShowRadioGroup.ItemIndex := 3
          else
            LPShowRadioGroup.ItemIndex := 0;

          if query.FieldByName ('MAX_DIFF_AR').IsNull then
            MaxDiffArEdit.Text := ''
          else
            MaxDiffArEdit.Text := IntToStr (query.FieldByName ('MAX_DIFF_AR').AsInteger);

          if not Assigned (query.FindField ('OPT_MIX_CATEGORY')) then
            StoreMixCategoryCheckBox.Visible := False
          else
            StoreMixCategoryCheckBox.Checked := (query.FieldByName ('OPT_MIX_CATEGORY').AsString = '1');

          if not Assigned (query.FindField ('OPT_LE_EINLAGERN')) then
            StoreSelectionRadioGroup.Visible := False
          else begin
            if (copy (query.FieldByName ('OPT_LE_EINLAGERN').AsString, 1, 1) = '0') then
              StoreSelectionRadioGroup.ItemIndex := 0
            else if (copy (query.FieldByName ('OPT_LE_EINLAGERN').AsString, 1, 1) = '1') then
              StoreSelectionRadioGroup.ItemIndex := 1
            else if (copy (query.FieldByName ('OPT_LE_EINLAGERN').AsString, 1, 1) = '2') then
              StoreSelectionRadioGroup.ItemIndex := 2;
          end;

          if (ZollTabSheet.TabVisible) then begin
            if Assigned (query.FindField ('OPT_UNVERZOLLT')) then
              ZollBereichCheckBox.Checked := (query.FieldByName ('OPT_UNVERZOLLT').AsString = '1')
            else
              ZollBereichCheckBox.Enabled := False;
          end else begin
            ZollBereichCheckBox.Enabled := False;
          end;

          prepareHouseKeeping(query, editlbform, ref);

          query.Close;

          if (TransportTypeCheckListBox.Visible) then begin
            query.SQL.Clear;
            query.SQL.Add ('select REF_TRANSPORT_TYPE from V_TRANSPORT_TYPE_REL_LB where REF_LB=:ref');
            query.Parameters.ParamByName('ref').Value := LBQuery.FieldByName ('REF').AsInteger;

            query.Open;

            while not (query.Eof) do begin
              idx := FindListBoxRef (TransportTypeCheckListBox, query.Fields [0].AsInteger);

              if (idx <> -1) then
                TransportTypeCheckListBox.Checked [idx] := True;

              query.Next;
            end;

            query.Close;
          end;

          if (NachschubBereichListBox.Visible) then begin
            query.SQL.Clear;

            if LVSDatenModul.ViewExits ('V_ARTIKEL_REL_NACHSCHUB') then begin
              query.SQL.Add ('select lb.REF, lb.NAME, rel.STATUS from V_LB lb left outer join V_LAGER_LB_REL_NACHSCHUB rel on (rel.REF_LB=:ref_lb and rel.REF_NACHSCHUB_LB=lb.REF) where lb.REF<>:ref and lb.REF_LAGER=:ref_lager order by rel.PRIO nulls last');
              query.Parameters.ParamByName ('ref').Value := ref;
            end else begin
              query.SQL.Add ('select lb.REF, lb.NAME, rel.STATUS from V_LB lb left outer join V_LAGER_LB_REL_NACHSCHUB rel on (rel.REF_LB=:ref_lb and rel.REF_NACHSCHUB_LB=lb.REF) where lb.USE_FOR_VORRAT=''1'' and lb.REF_LAGER=:ref_lager order by rel.PRIO nulls last');
            end;

            query.Parameters.ParamByName ('ref_lager').Value := lagerref;
            query.Parameters.ParamByName ('ref_lb').Value := ref;

            try
              query.Open;

              while not (query.EOF) do begin
                idx := NachschubBereichListBox.Items.AddObject (query.Fields [1].AsString, TComboBoxRef.Create (query.Fields [0].AsInteger));

                if (query.Fields [2].AsString = 'AKT') then
                  NachschubBereichListBox.Checked [idx] := True;

                query.Next;
              end;

              query.Close;
            except
              NachschubBereichListBox.Visible := False;
            end;
          end;
        end;

        editlbform.RefLB := -1;

        if (editlbform.ShowModal = mrOk) Then begin
          fBereichTabIndex := editlbform.PageControl1.ActivePageIndex;

          LBDBGrid.Reload (editlbform.RefLB);
        end;

        editlbform.Free;
      end;
    finally
      query.Free;
    end;
  end;

  LBDBGrid.SetFocus;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TLagerTopologieForm.LBDataSourceDataChange(Sender: TObject;
  Field: TField);
begin
  LBZoneQuery.Close;

  LBCopyButton.Enabled := False;
  EditLBButton.Enabled := False;
  DelLBButton.Enabled  := False;

  LBZoneNewButton.Enabled   := False;
  LBZOneEditButton.Enabled  := False;
  LBZoneDelButton.Enabled   := False;

  if (LBQuery.Active) and (LBQuery.RecNo > 0) then begin
    LBCopyButton.Enabled := True;
    EditLBButton.Enabled := True;
    DelLBButton.Enabled  := True;

    LBZoneNewButton.Enabled  := True;

    LBZoneQuery.SQL.Clear;
    LBZoneQuery.SQL.Add ('select * from V_LB_ZONE where REF_LB='+LBQuery.FieldByName ('REF').AsString);

    LBZoneQuery.Open;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TLagerTopologieForm.NeuLPSerieButtonClick(Sender: TObject);
var
  res,
  feldstep,
  ebenestep,
  hkl,
  gw,
  tend,
  tstart,
  platznr,
  fachnr,
  stellnr,
  l, b, h,
  ltanz, lpanz      : Integer;
  optstr            : String;
  altcursor         : TCursor;
  CreateLPSerieForm : TCreateLPSerieForm;
begin
  CreateLPSerieForm := TCreateLPSerieForm.Create(Self);

  try
    with CreateLPSerieForm do begin
      if not (LVSConfigModul.UseLPTiefe) then begin
        Label3.Visible := false;
        Label4.Visible := false;
        Edit1.Visible := false;
        Edit2.Visible := false;
        TiefeStartUpDown.Visible := false;
        TiefeEndUpDown.Visible := false;
      end;

      LoadLBZoneCombobox (LBZoneComboBox, GetComboBoxRef (LBComboBox1));
      if (LBZoneComboBox.Items.Count > 0) then
        LBZoneComboBox.ItemIndex := 0;
      LBZoneComboBox.Enabled := (LBZoneComboBox.Items.Count > 1);

      LoadLPArten (GetComboBoxRef (LagerComboBox2), LPArtComboBox);
      LPArtComboBox.ItemIndex := FindComboboxRef (LPArtComboBox, GetLBArtLPType (GetComboBoxRef (LBComboBox1)));
      if (LPArtComboBox.ItemIndex = -1) then LPArtComboBox.ItemIndex := 0;

      LoadLPArten (GetComboBoxRef (LagerComboBox2), FBArtComboBox);
      FBArtComboBox.ItemIndex := FindComboboxRef (FBArtComboBox, GetLBArtLPType (GetComboBoxRef (LBComboBox1)));
      if (FBArtComboBox.ItemIndex = -1) then FBArtComboBox.ItemIndex := 0;

      LoadComboxDBItems (LPHKLComboBox, 'LAGER', 'HOEHEN_KLASSE');
      LPHKLComboBox.Items.Insert (0, '');
      if (LPHKLComboBox.ItemIndex = -1) then LPHKLComboBox.ItemIndex := 0;

      RegalEdit.Text := '';
      NameEdit.Text := '';

      FeldStartUpDown.Position  := fLastFeldStart;
      FeldEndUpDown.Position    := fLastFeldEnd;
      FachStartUpDown.Position  := fLastFachStart;
      FachEndUpDown.Position    := fLastFachEnd;
      EbeneStartUpDown.Position := fLastEbeneStart;
      EbeneEndUpDown.Position   := fLastEbeneEnd;
      FeldStepUpDown.Position   := fLastFeldStep;
      EbeneStepUpDown.Position  := fLastEbeneStep;

      LTAnzUpDown.Position       := 1;
      FBFeldStepUpDown.Position  := fLastFeldStep;
      FBEbeneStepUpDown.Position := fLastEbeneStep;

      if (GetLBArt (GetComboBoxRef (LBComboBox1)) = 'FB') then begin
        if (fLastLPNr > 0) then
          FBPlatzNrEdit.Text := IntToStr (fLastLPNr);

        PageControl1.ActivePage := FBTabSheet;
      end else begin
        if (fLastLPNr > 0) then
          PlatzNrEdit.Text := IntToStr (fLastLPNr);

        PageControl1.ActivePage := LTTabSheet;
      end;
    end;

    if (CreateLPSerieForm.ShowModal = mrOk) then begin
      with CreateLPSerieForm do begin
        fLastFeldStart  := FeldStartUpDown.Position;
        fLastFeldEnd    := FeldEndUpDown.Position;
        fLastFachStart  := FachStartUpDown.Position;
        fLastFachEnd    := FachEndUpDown.Position;
        fLastEbeneStart := EbeneStartUpDown.Position;
        fLastEbeneEnd   := EbeneEndUpDown.Position;

        if (PageControl1.ActivePage = LTTabSheet) then begin
          fLastFeldStep   := FeldStepUpDown.Position;
          fLastEbeneStep  := EbeneStepUpDown.Position;

          if (Length (PlatzNrEdit.Text) = 0) then
            fLastLPNr := -1
          else if not TryStrToInt(PlatzNrEdit.Text, fLastLPNr) then
            fLastLPNr := -1
        end else begin
          fLastFeldStep   := FBFeldStepUpDown.Position;
          fLastEbeneStep  := FBEbeneStepUpDown.Position;

          if (Length (FBPlatzNrEdit.Text) = 0) then
            fLastLPNr := -1
          else if not TryStrToInt(FBPlatzNrEdit.Text, fLastLPNr) then
            fLastLPNr := -1
        end;
      end;

      with CreateLPSerieForm do begin
        altcursor := Screen.Cursor;
        Screen.Cursor :=crSQLWait;
        try
          if (PageControl1.ActivePage = LTTabSheet) then begin
            gw     := -1;
            tend   := 0;
            tstart := 0;

            if (LPHKLComboBox.ItemIndex = 0) then
              hkl := -1
            else if not (TryStrToInt (LPHKLComboBox.GetItemText, hkl)) then
              hkl := -1;

            if (LTAnzUpDown.Position = 0) then
              ltanz := -1
            else
              ltanz := LTAnzUpDown.Position;

            feldstep  := FeldStepUpDown.Position;
            ebenestep := EbeneStepUpDown.Position;

            if (Length (MaxGewichtEdit.Text) = 0) then
              gw := -1
            else if not (TryStrToInt (MaxGewichtEdit.Text, gw)) then
              gw := -1
            else
              gw := gw * 1000;

            if (Length (PlatzNrEdit.Text) = 0) then
              platznr := -1
            else if not (TryStrToInt (PlatzNrEdit.Text, platznr)) then
              platznr := -1;

            if (TiefeStartUpDown.Visible) and (TiefeEndUpDown.Visible) then begin
              tend   := TiefeEndUpDown.Position;
              tstart := TiefeStartUpDown.Position;
            end;

            if (Length (FachNrEdit.Text) = 0) then
              fachnr := -1
            else if not TryStrToInt (FachNrEdit.Text, fachnr) then
              fachnr := -1;

            if (Length (StellplatzNrEdit.Text) = 0) then
              stellnr := -1
            else if not TryStrToInt (StellplatzNrEdit.Text, stellnr) then
              stellnr := -1;

            if (fachnr > 0) then begin
              optstr := '00000000';
              if (LPNrFolgeCheckBox.Checked) then optstr [1] := '1';

              res := CreateLagerplatzSerie (GetComboBoxRef (LBComboBox1),
                                            GetComboBoxRef (LBZoneComboBox),
                                            -1,
                                            NameEdit.Text,
                                            RegalEdit.Text,
                                            FeldStartUpDown.Position,
                                            FeldEndUpDown.Position,
                                            feldstep,
                                            FachStartUpDown.Position,
                                            FachEndUpDown.Position,
                                            fachnr,
                                            FachNrStepUpDown.Position,
                                            tstart,
                                            tend,
                                            EbeneStartUpDown.Position,
                                            EbeneEndUpDown.Position,
                                            ebenestep,
                                            GetComboBoxRef (LPArtComboBox),
                                            ltanz,
                                            gw,
                                            LPPrioUpDown.Position,
                                            hkl,
                                            platznr,
                                            stellnr,
                                            optstr);
            end else if ((stellnr > 0) or (ebenestep > 0)) then begin
              res := CreateLagerplatzSerie (GetComboBoxRef (LBComboBox1),
                                            GetComboBoxRef (LBZoneComboBox),
                                            NameEdit.Text,
                                            RegalEdit.Text,
                                            FeldStartUpDown.Position,
                                            FeldEndUpDown.Position,
                                            feldstep,
                                            FachStartUpDown.Position,
                                            FachEndUpDown.Position,
                                            tstart,
                                            tend,
                                            ebenestep,
                                            EbeneStartUpDown.Position,
                                            EbeneEndUpDown.Position,
                                            GetComboBoxRef (LPArtComboBox),
                                            ltanz,
                                            gw,
                                            LPPrioUpDown.Position,
                                            hkl,
                                            platznr,
                                            LPNrFolgeCheckBox.Checked,
                                            stellnr)
            end else begin
              res := CreateLagerplatzSerie (GetComboBoxRef (LBComboBox1),
                                            GetComboBoxRef (LBZoneComboBox),
                                            NameEdit.Text,
                                            RegalEdit.Text,
                                            FeldStartUpDown.Position,
                                            FeldEndUpDown.Position,
                                            feldstep,
                                            FachStartUpDown.Position,
                                            FachEndUpDown.Position,
                                            tstart,
                                            tend,
                                            EbeneStartUpDown.Position,
                                            EbeneEndUpDown.Position,
                                            GetComboBoxRef (LPArtComboBox),
                                            ltanz,
                                            gw,
                                            LPPrioUpDown.Position,
                                            hkl,
                                            platznr,
                                            LPNrFolgeCheckBox.Checked);
            end;
          end else begin
            if (Length (FBLEdit.Text) = 0) then
              l := -1
            else
              l := StrToInt (FBLEdit.Text);

            if (Length (FBBEdit.Text) = 0) then
              b := -1
            else
              b := StrToInt (FBBEdit.Text);

            if (Length (FBHEdit.Text) = 0) then
              h := -1
            else
              h := StrToInt (FBHEdit.Text);

            if (FBLTAnzUpDown.Position = 0) then
              ltanz := -1
            else
              ltanz := FBLTAnzUpDown.Position;

            feldstep  := FBFeldStepUpDown.Position;
            ebenestep := FBEbeneStepUpDown.Position;

            if (Length (FBMaxGewichtEdit.Text) = 0) then
              gw := -1
            else if not (TryStrToInt (FBMaxGewichtEdit.Text, gw)) then
              gw := -1
            else
              gw := gw * 1000;

            if (Length (FBPlatzNrEdit.Text) = 0) then
              platznr := -1
            else if not (TryStrToInt (FBPlatzNrEdit.Text, platznr)) then
              platznr := -1;

            if (ebenestep > 0) then
              res := CreateFachplatzSerie (GetComboBoxRef (LBComboBox1),
                                           GetComboBoxRef (LBZoneComboBox),
                                           NameEdit.Text,
                                           RegalEdit.Text,
                                           FBFeldStartUpDown.Position,
                                           FBFeldEndUpDown.Position,
                                           feldstep,
                                           FBFachStartUpDown.Position,
                                           FBFachEndUpDown.Position,
                                           ebenestep,
                                           FBEbeneStartUpDown.Position,
                                           FBEbeneEndUpDown.Position,
                                           l, b, h,
                                           GetComboBoxRef (FBArtComboBox),
                                           ltanz,
                                           gw,
                                           LPPrioUpDown.Position,
                                           platznr,
                                           FBLPNrFolgeCheckBox.Checked)
            else
              res := CreateFachplatzSerie (GetComboBoxRef (LBComboBox1),
                                           GetComboBoxRef (LBZoneComboBox),
                                           NameEdit.Text,
                                           RegalEdit.Text,
                                           FBFeldStartUpDown.Position,
                                           FBFeldEndUpDown.Position,
                                           feldstep,
                                           FBFachStartUpDown.Position,
                                           FBFachEndUpDown.Position,
                                           FBEbeneStartUpDown.Position,
                                           FBEbeneEndUpDown.Position,
                                           l, b, h,
                                           GetComboBoxRef (FBArtComboBox),
                                           ltanz,
                                           gw,
                                           LPPrioUpDown.Position,
                                           platznr,
                                           FBLPNrFolgeCheckBox.Checked);
          end;
        except
          res := -9;
        end;

        Screen.Cursor := altcursor;
      end;

      if (res <> 0) Then
        FrontendMessages.MessageDlg (FormatMessageText (1243, [LVSDatenModul.LastLVSErrorText]), mtError, [mbOK], 0)
      else begin
        if (fLastLPNr > 0) then begin
          if (fLastFeldEnd = fLastFeldStart) then
            lpanz := 1
          else begin
            lpanz := abs (fLastFeldEnd - fLastFeldStart);
            if (lpanz = 0) then lpanz := 1;
          end;

          if (fLastFachEnd <> fLastFachStart) then
            lpanz := lpanz * abs (fLastFachEnd - fLastFachStart);

          if (fLastEbeneEnd <> fLastEbeneStart) then
            lpanz := lpanz * abs (fLastEbeneEnd - fLastEbeneStart);

          if (lpanz > 1000) then
            fLastLPNr := (round ((fLastLPNr + lpanz) / 1000.0) + 1) * 1000
          else if (lpanz > 100) then
            fLastLPNr := (round ((fLastLPNr + lpanz) / 100.0) + 1) * 100
          else if (lpanz > 10) then
            fLastLPNr := (round ((fLastLPNr + lpanz) / 10.0) + 1) * 10
        end;

        UpdateLPQuery (GetComboBoxRef (LagerComboBox2), GetComboBoxRef (LBComboBox1));
      end;
   end;
 finally
   CreateLPSerieForm.Free;
 end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TLagerTopologieForm.StringGrid1KeyPress(Sender: TObject;
  var Key: Char);
begin
  if (Key = Chr (VK_RETURN)) Then
    LagerStringGridDblClick (Self);
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TLagerTopologieForm.StringGrid2KeyPress(Sender: TObject; var Key: Char);
begin
  if (Key = Chr (VK_RETURN)) Then
    StringGrid2DblClick (Self);
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TLagerTopologieForm.FormCreate(Sender: TObject);
begin
  fProgressForm := nil;

  fBereichTabIndex := -1;

  fLastFeldStep   := 1;
  fLastFeldStart  := 1;
  fLastFeldEnd    := 1;
  fLastFachStart  := 1;
  fLastFachEnd    := 1;
  fLastEbeneStep  := 1;
  fLastEbeneStart := 0;
  fLastEbeneEnd   := 0;
  fLastLPNr       := -1;

  PageControl1.ActivePageIndex := 0;

  LPOraQuery.Session := LVSDatenModul.OraMainSession;
  LPArtikelQuery.Session := LVSDatenModul.OraMainSession;

  if Assigned (DBGridUtils) then begin
    DBGridUtils.SetupDBGrids (Self);
    DBGridUtils.ConfigDBGrids (Self);
  end;

  LVSSprachModul.InitForm (Self);

  {$ifdef TranslateHelper}
    LVSSprachModul.SetNoTranslate (Self, LocComboBox);
    LVSSprachModul.SetNoTranslate (Self, LagerComboBox1);
    LVSSprachModul.SetNoTranslate (Self, LagerComboBox2);
    LVSSprachModul.SetNoTranslate (Self, LagerComboBox3);
    LVSSprachModul.SetNoTranslate (Self, LBComboBox1);
    LVSSprachModul.SetNoTranslate (Self, LBComboBox2);
  {$endif}

  CompTranslateForm1ChangeLanguage (Sender);

  if not (LVSDatenModul.ViewExits ('V_ARTIKEL_REL_LP')) then
    LPArtikelPanel.Visible := false
  else begin
    LPArtikelPanel.Visible := true;
    LPArtikelQuery.SQL.Add('select * from V_ARTIKEL_REL_LP where REF_LP=:REF_LP');
    Constraints.MinHeight := Constraints.MinHeight + LPArtikelPanel.Height;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TLagerTopologieForm.FormDestroy(Sender: TObject);
begin
  ClearGridObjects (LocStringGrid);
  ClearGridObjects (LagerStringGrid);

  ClearComboBoxObjects (LocComboBox);
  ClearComboBoxObjects (LagerComboBox1);
  ClearComboBoxObjects (LagerComboBox2);
  ClearComboBoxObjects (LagerComboBox3);
  ClearComboBoxObjects (LBComboBox1);
  ClearComboBoxObjects (LBComboBox2);

  if Assigned (fProgressForm) then
    fProgressForm.Free;

  fProgressForm := Nil;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TLagerTopologieForm.KommTabSheetShow(Sender: TObject);
begin
  LoadLagerCombobox (LagerComboBox3, LVSDatenModul.AktLocation);

  LagerComboBox3.ItemIndex := FindComboboxRef(LagerComboBox3, fAktLagerRef );

  if (LagerComboBox3.ItemIndex = -1) then begin
    if (Length (LVSDatenModul.AktLager) > 0) then
      LagerComboBox3.ItemIndex := LagerComboBox3.IndexOf(LVSDatenModul.AktLager)
    else if (LagerComboBox3.Items.Count > 0) Then
      LagerComboBox3.ItemIndex := 0
    else LagerComboBox3.ItemIndex := -1
  end;

  if (LagerComboBox3.ItemIndex <> -1) then begin
    UpdateBereicheComboBox (LagerComboBox3, GetComboBoxRef (LagerComboBox3), LBComboBox2, False);

    if (fAktBereichRef <> -1) Then
      LBComboBox2.ItemIndex := FindComboboxRef(LBComboBox2, fAktBereichRef );

    LBComboBoxChange (LBComboBox2);
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TLagerTopologieForm.AddKommLPButtonClick(Sender: TObject);
begin
  KommLPAnlegen;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function DeleteKommPlatzEvent (DBGrid : TDBGridPro; Daten : Pointer; InfoForm : TForm; var Option : String; var Done : Boolean; var Abort : Boolean) : Integer;
var
  res  : Integer;
begin
  res := DeleteKommPlatz (DBGrid.DataSource.DataSet.FieldByName ('REF').AsInteger);

  Result := res;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 19.01.2020
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TLagerTopologieForm.RegalButtonClick(Sender: TObject);
var
  form : TLagerRegalForm;
begin
  form := TLagerRegalForm.Create (Self);

  try
    if (LPDBGrid.DataSource.DataSet.Active and (LPDBGrid.DataSource.DataSet.RecNo > 0)) then
      form.Prepare (GetComboBoxRef(LagerComboBox2), GetComboBoxRef(LBComboBox1), LPDBGrid.DataSource.DataSet.FieldByName ('REF_REGAL').AsInteger)
    else
      form.Prepare (GetComboBoxRef(LagerComboBox2), GetComboBoxRef(LBComboBox1), -1);

    form.ShowModal;
  finally
    form.Release;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TLagerTopologieForm.RemoveKommLPButtonClick(Sender: TObject);
var
  res   : Integer;
begin
  res := 0;

  if (KommADOQuery.Active) and (KommADOQuery.RecNo > 0) then begin
    if KommDBGrid.SelectedRows.Count > 0 then begin
      res := DBGridUtils.DoSelectedRows (KommDBGrid, DeleteKommPlatzEvent);
    end else if (KommADOQuery.RecNo > 0) then begin
      res := DeleteKommPlatz (KommDBGrid.DataSource.DataSet.FieldByName ('REF').AsInteger);
    end;

    if (res <> 0) then
      FrontendMessages.MessageDlg ('Fehler beim Löschen des Kommissionierplatzes'+#13+#13+LVSDatenModul.LastLVSErrorText, mtError, [mbOK], 0)
    else begin
      if (LBComboBox2.ItemIndex > 0) Then
        UpdateKommQuery (GetComboBoxRef (LagerComboBox3), GetComboBoxRef (LBComboBox2))
      else UpdateKommQuery (GetComboBoxRef (LagerComboBox3), -1)
    end;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TLagerTopologieForm.FormShow(Sender: TObject);
var
  intwert : Integer;
begin
  LPTabSplitter.Visible := LPArtikelPanel.Visible;

  if (Constraints.MinHeight > Height) then
    Height := Constraints.MinHeight;

  LagerTabSheetResize (LagerTabSheet);
  TabSheet5Resize (TabSheet5);

  if Assigned (LVSConfigModul) then begin
    LVSConfigModul.RestoreFormInfo (Self);
    LVSConfigModul.ConfigForm (Self);

    LVSConfigModul.RestoreGridInfo (Self, LocStringGrid);
    LVSConfigModul.RestoreGridInfo (Self, LagerStringGrid);

    if LPTabSplitter.Visible then begin
      if (LVSConfigModul.ReadFormParameter (Self, 'LPTabSplitter', intwert, LPDatenPanel.Height) = 0) then begin
        if (intwert < LPDatenPanel.Constraints.MinHeight) then
          LPDatenPanel.Height := LPDatenPanel.Constraints.MinHeight
        else
          LPDatenPanel.Height := intwert;
      end;
    end;
  end;

  if (Length (LVSDatenModul.AktLocation) = 0) then begin
    Caption := GetResourceText (1515)
  end else begin
    Caption := FormatResourceText (1516, [LVSDatenModul.AktLocation]);
  end;

  fAktLocationRef := LVSDatenModul.AktLocationRef;
  fAktLagerRef    := LVSDatenModul.AktLagerRef;
  fAktBereichRef  := -1;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TLagerTopologieForm.LagerTabSheetResize(Sender: TObject);
begin
  with LagerStringGrid do begin
    ColWidths [1] := ((ClientWidth - ColCount * 1 - ColWidths [0]) * 10) div 100;
    ColWidths [2] := ((ClientWidth - ColCount * 1 - ColWidths [0]) * 10) div 100;
    ColWidths [3] := ((ClientWidth - ColCount * 1 - ColWidths [0]) * 10) div 100;
    ColWidths [4] := ((ClientWidth - ColCount * 1 - ColWidths [0]) * 50) div 100;
    ColWidths [5] := ((ClientWidth - ColCount * 1 - ColWidths [0]) * 10) div 100;
    ColWidths [6] := ((ClientWidth - ColCount * 1 - ColWidths [0]) * 10) div 100;
  end;
end;


//******************************************************************************
//* Function Name: ChangeLP
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function TLagerTopologieForm.ChangeLP (DBGrid : TDBGridPro; Daten : Pointer; InfoForm : TForm; var Option : String; var Done : Boolean; var Abort : Boolean) : Integer;
var
  res,
  gw,
  l,
  b,
  h,
  hkl,
  lpart,
  ltanz,
  stabelfaktor : Integer;
  scan         : TCheckBoxState;
  lpname,
  abc          : String;
  query        : TADOQuery;
begin
  res := 0;
  
  if Assigned (fProgressForm) and (fProgressForm.AbortFlag) then begin
    Done := False;
    Abort := True
  end else begin
    Done := True;

    query := TChangeLPData (Daten^).ChangeQuery;

    try
      if (Length (query.SQL.Text) = 0) then
        query.SQL.Add ('select * from V_LP where REF=:ref');
      query.Parameters.Items [0].Value := DBGrid.DataSource.DataSet.FieldByName ('REF').AsInteger;

      query.Open;

      with TEditLPForm (TChangeLPData (Daten^).EditForm) do begin
        if not (NameEdit.Enabled) then
          lpname := query.FieldByName ('NAME').AsString
        else if (Length (NameEdit.Text) = 0) then
          lpname := query.FieldByName ('NAME').AsString
        else
          lpname := NameEdit.Text;

        if (LPArtComboBox.ItemIndex = 0) then
          lpart := DBGetIntegerNull (query.FieldByName ('REF_LP_TYPE'))
        else
          lpart := GetComboBoxRef (LPArtComboBox);

        if (LPHKLComboBox.ItemIndex = 0) then
          hkl := DBGetIntegerNull (query.FieldByName ('HOEHEN_KLASSE'))
        else if not (TryStrToInt (LPHKLComboBox.GetItemText, hkl)) then
          hkl := -1;

        if (ABCComboBox.Visible and (ABCComboBox.ItemIndex >= 0)) then
          abc := ABCComboBox.Text
        else if Assigned (query.FindField('ABC_KLASSE')) then
          abc := query.FieldByName ('ABC_KLASSE').AsString
        else
          abc := '';

        if not (ScanableCheckBox.Visible) then
          scan := cbGrayed
        else
          scan := ScanableCheckBox.State;

        if (LTAnzUpDown.Position = 0) then
          ltanz := -1
        else
          ltanz := LTAnzUpDown.Position;

        if not (StapelFaktorEdit.Visible and StapelFaktorEdit.Enabled) then
          stabelfaktor := -1
        else if (StapelFaktorUpDown.Position = 0) then
          stabelfaktor := DBGetIntegerNull (query.FieldByName ('STAPEL_FAKTOR'))
        else
          stabelfaktor := StapelFaktorUpDown.Position;

        if (Length (MaxGewichtEdit.Text) = 0) then
          gw := DBGetIntegerNull (query.FieldByName ('MAX_GEWICHT'))
        else if not (TryStrToInt (MaxGewichtEdit.Text, gw)) then
          gw := -1
        else
          gw := gw * 1000;

        if (Length (FBLEdit.Text) = 0) then
          l := DBGetIntegerNull (query.FieldByName ('L'))
        else
          l := StrToInt (FBLEdit.Text);

        if (Length (FBBEdit.Text) = 0) then
          b := DBGetIntegerNull (query.FieldByName ('B'))
        else
          b := StrToInt (FBBEdit.Text);

        if (Length (FBHEdit.Text) = 0) then
          h := DBGetIntegerNull (query.FieldByName ('H'))
        else
          h := StrToInt (FBHEdit.Text);

        if ABCComboBox.Visible then
          res := ChangeLagerplatz (DBGrid.DataSource.DataSet.FieldByName ('REF').AsInteger,
                                   DBGrid.DataSource.DataSet.FieldByName ('UPDATE_COUNT').AsInteger,
                                   DBGrid.DataSource.DataSet.FieldByName ('REF_LB').AsInteger,
                                   lpname,
                                   lpart,
                                   l, b, h,
                                   ltanz,
                                   gw,
                                   LPPrioUpDown.Position,
                                   hkl, stabelfaktor, abc, scan)
        else if (StapelFaktorEdit.Visible and StapelFaktorEdit.Enabled) then
          res := ChangeLagerplatz (DBGrid.DataSource.DataSet.FieldByName ('REF').AsInteger,
                                   DBGrid.DataSource.DataSet.FieldByName ('UPDATE_COUNT').AsInteger,
                                   DBGrid.DataSource.DataSet.FieldByName ('REF_LB').AsInteger,
                                   lpname,
                                   lpart,
                                   l, b, h,
                                   ltanz,
                                   gw,
                                   LPPrioUpDown.Position,
                                   hkl, stabelfaktor)
        else
          res := ChangeLagerplatz (DBGrid.DataSource.DataSet.FieldByName ('REF').AsInteger,
                                   DBGrid.DataSource.DataSet.FieldByName ('UPDATE_COUNT').AsInteger,
                                   DBGrid.DataSource.DataSet.FieldByName ('REF_LB').AsInteger,
                                   lpname,
                                   lpart,
                                   l, b, h,
                                   ltanz,
                                   gw,
                                   LPPrioUpDown.Position,
                                   hkl);
      end;

      query.Close;
    finally
    end;
  end;

  Result := res;
end;

//******************************************************************************
//* Function Name: ChangeLPLB
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function TLagerTopologieForm.ChangeLPLB (DBGrid : TDBGridPro; Daten : Pointer; InfoForm : TForm; var Option : String; var Done : Boolean; var Abort : Boolean) : Integer;
var
  res : Integer;
begin
  res := 0;

  with TChangeLPLBForm (Daten) do begin
    if ((GetComboBoxRef (LBComboBox) <> DBGrid.DataSource.DataSet.FieldByName ('REF_LB').AsInteger) or
        (GetComboBoxRef (LBZoneComboBox) <> DBGetReferenz (DBGrid.DataSource.DataSet.FieldByName ('REF_LB_ZONE')))) then begin
      res := ChangeLPBereich (DBGrid.DataSource.DataSet.FieldByName ('REF').AsInteger, GetComboBoxRef (LBComboBox), GetComboBoxRef (LBZoneComboBox));
    end;
  end;

  Result := res;
end;

//******************************************************************************
//* Function Name: ChangeLPLB
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function TLagerTopologieForm.AssignARLP (DBGrid : TDBGridPro; Daten : Pointer; InfoForm : TForm; var Option : String; var Done : Boolean; var Abort : Boolean) : Integer;
var
  res,
  ref : Integer;
begin
  res := 0;

  with TArtikeDaten (Daten^) do begin
    res := AssignArtikelLP (RefArtikel, RefEinheit, DBGrid.DataSource.DataSet.FieldByName('REF').AsInteger, ref);
  end;

  Result := res;
end;

//******************************************************************************
//* Function Name: ChangeLPLBButtonClick
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TLagerTopologieForm.ChangeLPLBButtonClick(Sender: TObject);
var
  res     : Integer;
  dlgform : TChangeLPLBForm;
  opt     : String;
  flag,
  abort   : Boolean;
begin
  dlgform := TChangeLPLBForm.Create (Self);

  LoadLBComboboxNullEntry (dlgform.LBComboBox, '', '', GetComboBoxRef (LagerComboBox2));

  dlgform.LBComboBox.ItemIndex := FindComboboxRef (dlgform.LBComboBox, LPOraQuery.FieldByName ('REF_LB').AsInteger);
  dlgform.LBComboBoxChange (Sender);

  if not (LPOraQuery.FieldByName ('REF_LB_ZONE').IsNull) then
    dlgform.LBZoneComboBox.ItemIndex := FindComboboxRef (dlgform.LBZoneComboBox, LPOraQuery.FieldByName ('REF_LB_ZONE').AsInteger);
  if (dlgform.LBZoneComboBox.ItemIndex = -1) then dlgform.LBZoneComboBox.ItemIndex := 0;

  if (dlgform.ShowModal = mrOk) Then begin
    if (LPDBGrid.SelectedRows.Count > 0) then
      res := DBGridUtils.DoSelectedRows (LPDBGrid, ChangeLPLB, '', dlgform)
    else begin
      flag  := True;
      abort := False;

      res := ChangeLPLB (LPDBGrid, dlgform, Nil, opt, flag, abort);

      if (res = 0) then
        LPOraQuery.RefreshRecord;
    end;

    if (res <> 0) then
      FrontendMessages.MessageDlg (FormatMessageText (1249, [LVSDatenModul.LastLVSErrorText]), mtError, [mbOK], 0)
  end;

  dlgform.Release;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TLagerTopologieForm.LPDBGridDblClick(Sender: TObject);
var
  EditLPForm: TEditLPForm;
  res,
  hkl,
  ltanz,
  gw,
  l, b, h,
  stabelfaktor : Integer;
  folge_nr  : Int64;
  scan         : TCheckBoxState;
  abc,
  reihe : String;
  feld, platz, ebene, tiefe : Integer;
  data : TChangeLPData;
begin
  if (LPOraQuery.Active) and ((LPOraQuery.RecNo > 0) or (LPDBGrid.SelectedRows.Count > 0)) then begin
    EditLPForm := TEditLPForm.Create (Self);

    with EditLPForm do begin
      Caption := GetResourceText(1446);

      if not (LVSConfigModul.UseLPTiefe) then
        TiefeEdit.Visible := False;

      LBZoneComboBox.Enabled := False;
      if not (LPOraQuery.FieldByName ('REF_LB_ZONE').IsNull) then begin
        LoadLBZoneCombobox (LBZoneComboBox, LPOraQuery.FieldByName ('REF_LB').AsInteger);
        LBZoneComboBox.ItemIndex := FindComboboxRef (LBZoneComboBox, LPOraQuery.FieldByName ('REF_LB_ZONE').AsInteger);
      end;

      LoadLPArten (GetComboBoxRef (LagerComboBox2), LPArtComboBox);
      LPArtComboBox.ItemIndex := FindComboboxRef (LPArtComboBox, LPOraQuery.FieldByName ('REF_LP_TYPE').AsInteger);
      if (LPArtComboBox.ItemIndex = -1) then LPArtComboBox.ItemIndex := 0;

      LoadComboxDBItems (LPHKLComboBox, 'LAGER', 'HOEHEN_KLASSE');
      LPHKLComboBox.Items.Insert (0, '');

      if (LPDBGrid.SelectedRows.Count > 0) then begin
        //Nassenupdate
        NameEdit.Enabled := False;
        NrEdit.Enabled := False;
        FolgeEdit.Enabled := False;
        RBGKoorEdit.Enabled := False;
        WWSPlatzEdit.Enabled := False;
        ReiheEdit.Enabled := False;
        FeldEdit.Enabled  := False;
        FachEdit.Enabled  := False;
        EbeneEdit.Enabled := False;
        TiefeEdit.Enabled := False;
        BarcodeEdit.Enabled := False;
        StellplatzNrEdit.Enabled := False;

        LPArtComboBox.Items.Insert (0, GetResourceText (1318));

        //Alle Felder leeren, es werden nur gefüllte Felder geändert
        LPHKLComboBox.ItemIndex := 0;
        LTAnzEdit.Text := '';
        LPPrioEdit.Text := '';
        StapelFaktorEdit.Text := '';
        MaxGewichtEdit.Text :=  '';
        FBLEdit.Text :=  '';
        FBBEdit.Text :=  '';
        FBHEdit.Text :=  '';
      end else begin
        NameEdit.Enabled := True;
        NameEdit.Text := LPOraQuery.FieldByName ('NAME').AsString;

        NrEdit.Enabled := True;
        NrEdit.Text := LPOraQuery.FieldByName ('LP_NR').AsString;

        ReiheEdit.Enabled := True;
        ReiheEdit.Text := StripString (LPOraQuery.FieldByName ('REIHE').AsString);
        FeldEdit.Enabled  := True;
        FeldEdit.Text := LPOraQuery.FieldByName ('FELD').AsString;
        FachEdit.Enabled  := True;
        FachEdit.Text := LPOraQuery.FieldByName ('PLATZ').AsString;
        EbeneEdit.Enabled := True;
        EbeneEdit.Text := LPOraQuery.FieldByName ('EBENE').AsString;
        TiefeEdit.Enabled := True;
        TiefeEdit.Text := LPOraQuery.FieldByName ('TIEFE').AsString;

        if Assigned (LPOraQuery.FindField ('BARCODE_ID')) then
          BarcodeEdit.Text := LPOraQuery.FieldByName ('BARCODE_ID').AsString
        else BarcodeEdit.Visible := False;

        if Assigned (LPOraQuery.FindField ('STELLPLATZ_NR')) then
          StellplatzNrEdit.Text := LPOraQuery.FieldByName ('STELLPLATZ_NR').AsString
        else StellplatzNrEdit.Visible := False;

        if Assigned (LPOraQuery.FindField ('OPT_SCANABLE')) then
          ScanableCheckBox.State := CheckOptState (LPOraQuery.FieldByName ('OPT_SCANABLE').AsString, 1)
        else ScanableCheckBox.Visible := False;

        LPHKLComboBox.ItemIndex := FindComboboxDBItemWert (LPHKLComboBox, LPOraQuery.FieldByName ('HOEHEN_KLASSE').AsString);
        if (LPHKLComboBox.ItemIndex = -1) then LPHKLComboBox.ItemIndex := 0;

        if not (LPOraQuery.FieldByName ('LT_CAPACITY').IsNull) then
          LTAnzUpDown.Position := LPOraQuery.FieldByName ('LT_CAPACITY').AsInteger
        else
          LTAnzUpDown.Position := -1;

        if not (LPOraQuery.FieldByName ('EINLAGER_PRIO').IsNull) then
          LPPrioUpDown.Position := LPOraQuery.FieldByName ('EINLAGER_PRIO').AsInteger
        else begin
          LPPrioUpDown.Position := 0;
          LPPrioEdit.Text := '';
        end;

        if not (Assigned (LPOraQuery.FindField ('ABC_KLASSE'))) then
          ABCComboBox.Visible := False
        else if LPOraQuery.FieldByName ('ABC_KLASSE').IsNull then
          ABCComboBox.ItemIndex := 0
        else begin
          ABCComboBox.ItemIndex := ABCComboBox.Items.IndexOf (LPOraQuery.FieldByName ('ABC_KLASSE').AsString);
          if (ABCComboBox.ItemIndex = -1) then ABCComboBox.ItemIndex := 0;
        end;

        if not (ScanableCheckBox.Visible) then
          scan := cbGrayed
        else
          scan := ScanableCheckBox.State;

        if not (Assigned (LPOraQuery.FindField ('STAPEL_FAKTOR'))) then
          StapelFaktorEdit.Visible := False
        else if LPOraQuery.FieldByName ('STAPEL_FAKTOR').IsNull then
          StapelFaktorEdit.Text := ''
        else
          StapelFaktorUpDown.Position := LPOraQuery.FieldByName ('STAPEL_FAKTOR').AsInteger;

        MaxGewichtEdit.Text := LPOraQuery.FieldByName ('MAX_GEWICHT').AsString;

        FBLEdit.Text := LPOraQuery.FieldByName ('L').AsString;
        FBBEdit.Text := LPOraQuery.FieldByName ('B').AsString;
        FBHEdit.Text := LPOraQuery.FieldByName ('H').AsString;

        FolgeEdit.Enabled := True;
        FolgeEdit.Text := LPOraQuery.FieldByName ('FOLGE_NR').AsString;
        RBGKoorEdit.Enabled := True;
        RBGKoorEdit.Text := LPOraQuery.FieldByName ('RBG_KOOR').AsString;
        WWSPlatzEdit.Enabled := True;
        WWSPlatzEdit.Text := LPOraQuery.FieldByName ('WWS_KOOR').AsString;
      end;
    end;

    if (EditLPForm.ShowModal = mrOk) Then begin
      if (LPDBGrid.SelectedRows.Count > 0) then begin
        fProgressForm := TInfoWin.Create (Self);

        try
          fProgressForm.Label1.Caption := GetResourceText (1332);
          fProgressForm.ButtonPanel.Visible := True;
          fProgressForm.ProgressPanel.Visible := True;
          fProgressForm.ProgressBar1.Position := 0;
          fProgressForm.ProgressBar1.Max := LPDBGrid.SelectedRows.Count;

          data.EditForm    := EditLPForm;
          data.ChangeQuery := TADOQuery.Create (Self);

          fProgressForm.BeginShowModal;

          try
            data.ChangeQuery.LockType := ltReadOnly;
            data.ChangeQuery.Connection := LVSDatenModul.MainADOConnection;

            res := DBGridUtils.DoSelectedRows (LPDBGrid, ChangeLP, '', @data)
          finally
            data.ChangeQuery.Free;
          end;

          fProgressForm.EndShowModal;
        finally
          if Assigned (fProgressForm) then
            fProgressForm.Free;

          fProgressForm := Nil;
        end;
      end else begin
        with EditLPForm do begin
          if (LPHKLComboBox.ItemIndex = 0) then
            hkl := -1
          else if not (TryStrToInt (LPHKLComboBox.GetItemText, hkl)) then
            hkl := -1;

          if (LTAnzUpDown.Position = 0) then
            ltanz := -1
          else
            ltanz := LTAnzUpDown.Position;

          if (Length (FolgeEdit.Text) = 0) then
            folge_nr := -1
          else if not (TryStrToInt64 (FolgeEdit.Text, folge_nr)) then
            folge_nr := -1;

          if (Length (MaxGewichtEdit.Text) = 0) then
            gw := -1
          else if not (TryStrToInt (MaxGewichtEdit.Text, gw)) then
            gw := -1
          else
            gw := gw * 1000;

          if (Length (FBLEdit.Text) = 0) then
            l := -1
          else
            l := StrToInt (FBLEdit.Text);

          if (Length (FBBEdit.Text) = 0) then
            b := -1
          else
            b := StrToInt (FBBEdit.Text);

          if (Length (FBHEdit.Text) = 0) then
            h := -1
          else
            h := StrToInt (FBHEdit.Text);

          if not (ReiheEdit.Enabled) then
            reihe := ''
          else
            reihe := StripString (ReiheEdit.Text);

          if not (FeldEdit.Enabled) then
            feld := -1
          else if (Length (FeldEdit.Text) = 0) then
            feld := -1
          else
            feld := StrToInt (FeldEdit.Text);

          if not (FachEdit.Enabled) then
            platz := -1
          else if (Length (FachEdit.Text) = 0) then
            platz := -1
          else
            platz := StrToInt (FachEdit.Text);

          if not (EbeneEdit.Enabled) then
            ebene := -1
          else if (Length (EbeneEdit.Text) = 0) then
            ebene := -1
          else
            ebene := StrToInt (EbeneEdit.Text);

          if not (TiefeEdit.Enabled) then
            tiefe := -1
          else if (Length (TiefeEdit.Text) = 0) then
            tiefe := -1
          else
            tiefe := StrToInt (TiefeEdit.Text);

          if (ABCComboBox.Visible and (ABCComboBox.ItemIndex >= 0)) then
            abc := ABCComboBox.Text
          else
            abc := '';

          if not (ScanableCheckBox.Visible) then
            scan := cbGrayed
          else
            scan := ScanableCheckBox.State;

          if not (StapelFaktorEdit.Visible and StapelFaktorEdit.Enabled) then
            stabelfaktor := -1
          else if (StapelFaktorUpDown.Position = 0) then
            stabelfaktor := -1
          else
            stabelfaktor := StapelFaktorUpDown.Position;

          if (ABCComboBox.Visible) then
            res := ChangeLagerplatzKoord (LPOraQuery.FieldByName ('REF').AsInteger,
                                          LPOraQuery.FieldByName ('UPDATE_COUNT').AsInteger,
                                          LPOraQuery.FieldByName ('REF_LB').AsInteger,
                                          DBGetReferenz (LPOraQuery.FieldByName ('REF_LB_ZONE')),
                                          NrEdit.Text,
                                          StellplatzNrEdit.Text,
                                          reihe,
                                          feld, platz, ebene, tiefe,
                                          NameEdit.Text,
                                          GetComboBoxRef (LPArtComboBox),
                                          l, b, h,
                                          ltanz,
                                          gw,
                                          folge_nr,
                                          LPPrioUpDown.Position,
                                          hkl,
                                          stabelfaktor,
                                          abc,
                                          scan,
                                          BarcodeEdit.Text,
                                          RBGKoorEdit.Text,
                                          WWSPlatzEdit.Text)
          else if (StapelFaktorEdit.Visible) then
            res := ChangeLagerplatzKoord (LPOraQuery.FieldByName ('REF').AsInteger,
                                          LPOraQuery.FieldByName ('UPDATE_COUNT').AsInteger,
                                          LPOraQuery.FieldByName ('REF_LB').AsInteger,
                                          DBGetReferenz (LPOraQuery.FieldByName ('REF_LB_ZONE')),
                                          NrEdit.Text,
                                          StellplatzNrEdit.Text,
                                          reihe,
                                          feld, platz, ebene, tiefe,
                                          NameEdit.Text,
                                          GetComboBoxRef (LPArtComboBox),
                                          l, b, h,
                                          ltanz,
                                          gw,
                                          folge_nr,
                                          LPPrioUpDown.Position,
                                          hkl,
                                          stabelfaktor,
                                          BarcodeEdit.Text,
                                          RBGKoorEdit.Text,
                                          WWSPlatzEdit.Text)
          else
            res := ChangeLagerplatzKoord (LPOraQuery.FieldByName ('REF').AsInteger,
                                          LPOraQuery.FieldByName ('UPDATE_COUNT').AsInteger,
                                          LPOraQuery.FieldByName ('REF_LB').AsInteger,
                                          DBGetReferenz (LPOraQuery.FieldByName ('REF_LB_ZONE')),
                                          NrEdit.Text,
                                          StellplatzNrEdit.Text,
                                          reihe,
                                          feld, platz, ebene, tiefe,
                                          NameEdit.Text,
                                          GetComboBoxRef (LPArtComboBox),
                                          l, b, h,
                                          ltanz,
                                          gw,
                                          folge_nr,
                                          LPPrioUpDown.Position,
                                          hkl,
                                          BarcodeEdit.Text,
                                          RBGKoorEdit.Text,
                                          WWSPlatzEdit.Text)
        end;

        if (res = 0) Then
          LPOraQuery.RefreshRecord;
      end;

      if (res <> 0) Then
        FrontendMessages.MessageDlg (FormatMessageText (1250, [LVSDatenModul.LastLVSErrorText]), mtError, [mbOK], 0);
    end;

    EditLPForm.Free;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TLagerTopologieForm.LPDBGridPopupMenuPopup(Sender: TObject);
begin
  ExportLPDataMenuItem.Enabled := (LPDBGrid.SelectedRows.Count > 0);
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TLagerTopologieForm.DBGridHotTrackKeyPress(Sender: TObject; var Key: Char);
begin
  if (Sender is TDBGridPro) then begin
    with (Sender as TDBGridPro) do begin
      DBGridUtils.HotTrackKeyPress(Sender, Key);
    end;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TLagerTopologieForm.DBGridDrawColumnCell(Sender: TObject; const Rect: TRect; DataCol: Integer; Column: TColumn; State: TGridDrawState);
var
  bm: TBitmap;
  idx: Integer;
begin
  if (PosEx ('STATUS', Column.FieldName) > 0) then begin
    if (LVSConfigModul.FrontendConfig.cfgStatIcons) then begin
      if (Column.Field.AsString = 'ANG') then begin
        if ((Sender = LPDBGrid) or (Sender = KommDBGrid)) then
          idx := 17
        else
          idx := 0;
      end else if (Column.Field.AsString = 'ABG') then
        idx := 1
      else if (Column.Field.AsString = 'BER') then
        idx := 3
      else if (Column.Field.AsString = 'BES') then
        idx := 2
      else if (Column.Field.AsString = 'KOM') then
        idx := 4
      else if (Column.Field.AsString = 'WA') then
        idx := 5
      else if (Column.Field.AsString = 'TRA') then
        idx := 6
      else if (Column.Field.AsString = 'FIN') then
        idx := 7
      else if (Column.Field.AsString = 'STO') then
        idx := 8
      else if (Column.Field.AsString = 'RLO') then
        idx := 10
      else if (Column.Field.AsString = 'PLA') then
        idx := 12
      else if (Column.Field.AsString = 'UMP') then
        idx := 13
      else if (Column.Field.AsString = 'IFC') then
        idx := 11
      else if (Column.Field.AsString = 'DEL') then
        idx := 9
      else if (Column.Field.AsString = 'SPERR') then
        idx := 16
      else if (Column.Field.AsString = 'BEL') then
        idx := 18
      else if (Column.Field.AsString = 'FREI') then
        idx := 17
      else if (Column.Field.AsString = 'CRO') then
        idx := 15
      else idx := -1;

      if (idx <> -1) then begin
        (Sender as TDBGridPro).Canvas.FillRect(rect);

        bm := TBitmap.Create;

        try
          if (idx = 0) then
            bm.handle := loadbitmap(hinstance, 'angelegt')
          else if (idx = 1) then
            bm.handle := loadbitmap(hinstance, 'abgeschlossen')
          else if (idx = 2) then
            bm.handle := loadbitmap(hinstance, 'fehler')
          else if (idx = 3) then
            bm.handle := loadbitmap(hinstance, 'transform')
          else if (idx = 4) then
            bm.handle := loadbitmap(hinstance, 'kommissionieren')
          else if (idx = 5) then
            bm.handle := loadbitmap(hinstance, 'warenausgang')
          else if (idx = 6) then
            bm.handle := loadbitmap(hinstance, 'interntransport')
          else if (idx = 7) then
            bm.handle := loadbitmap(hinstance, 'beendet')
          else if (idx = 8) then
            bm.handle := loadbitmap(hinstance, 'storniert')
          else if (idx = 9) then
            bm.handle := loadbitmap(hinstance, 'delete')
          else if (idx = 10) then
            bm.handle := loadbitmap(hinstance, 'reload')
          else if (idx = 11) then
            bm.handle := loadbitmap(hinstance, 'ifcerror')
          else if (idx = 12) then
            bm.handle := loadbitmap(hinstance, 'geplant')
          else if (idx = 13) then
            bm.handle := loadbitmap(hinstance, 'umpack')
          else if (idx = 14) then
            bm.handle := loadbitmap(hinstance, 'gabelstapler')
          else if (idx = 15) then
            bm.handle := loadbitmap(hinstance, 'crossdock')
          else if (idx = 16) then
            bm.handle := loadbitmap(hinstance, 'redcross')
          else if (idx = 17) then
            bm.handle := loadbitmap(hinstance, 'tick')
          else if (idx = 18) then begin
            bm.handle := loadbitmap(hinstance, 'oneway');
          end;

          DrawStatusBitmap ((Sender as TDBGridPro).Canvas, Rect, bm);
        finally
          bm.Free;
        end;
      end;
    end;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TLagerTopologieForm.NeuLPButtonClick(Sender: TObject);
var
  res,
  ref,
  hkl,
  gw,
  ltanz,
  l, b, h      : Integer;
  folge_nr     : Int64;
  abc,
  reihe        : String;
  reflb,
  feld, platz,
  ebene, tiefe : Integer;
  stabelfaktor : Integer;
  scan         : TCheckBoxState;
  EditLPForm   : TEditLPForm;
begin
  EditLPForm := TEditLPForm.Create (Self);

  with EditLPForm do begin
    Caption := GetResourceText(1447);

    StapelFaktorEdit.Visible := Assigned (LPOraQuery.FindField('STAPEL_FAKTOR'));
    StellplatzNrEdit.Visible := Assigned (LPOraQuery.FindField('STELLPLATZ_NR'));
    BarcodeEdit.Visible      := Assigned (LPOraQuery.FindField('BARCODE_ID'));
    ABCComboBox.Visible      := Assigned (LPOraQuery.FindField('ABC_KLASSE'));
    ScanableCheckBox.Visible := Assigned (LPOraQuery.FindField('OPT_SCANABLE'));

    if not (LVSConfigModul.UseLPTiefe) then
      TiefeEdit.Visible := False;

    LoadLBZoneCombobox (LBZoneComboBox, GetComboBoxRef (LBComboBox1));
    if (LBZoneComboBox.Items.Count > 0) then
      LBZoneComboBox.ItemIndex := 0;
    LBZoneComboBox.Enabled := (LBZoneComboBox.Items.Count > 1);

    LoadLPArten (GetComboBoxRef (LagerComboBox2), LPArtComboBox);

    LoadComboxDBItems (LPHKLComboBox, 'LAGER', 'HOEHEN_KLASSE');
    LPHKLComboBox.Items.Insert (0, '');

    if (Sender = LPCreateSameMenuItem) then begin
      reflb := LPOraQuery.FieldByName('REF_LB').AsInteger;

      LPArtComboBox.ItemIndex := FindComboboxRef (LPArtComboBox, LPOraQuery.FieldByName('REF_LP_TYPE').AsInteger);
      if (LPArtComboBox.ItemIndex = -1) then LPArtComboBox.ItemIndex := 0;

      LPHKLComboBox.ItemIndex := FindComboboxDBItemWert (LPHKLComboBox, LPOraQuery.FieldByName('HOEHEN_KLASSE').AsString);
      if (LPHKLComboBox.ItemIndex = -1) then LPHKLComboBox.ItemIndex := 0;

      NrEdit.Text := LPOraQuery.FieldByName('LP_NR').AsString;
      NameEdit.Text := LPOraQuery.FieldByName('NAME').AsString;

      ReiheEdit.Enabled := True;
      FeldEdit.Enabled  := True;
      FachEdit.Enabled  := True;
      EbeneEdit.Enabled := True;
      TiefeEdit.Enabled := TiefeEdit.Visible;

      ReiheEdit.Text := LPOraQuery.FieldByName('REIHE').AsString;
      FeldEdit.Text := LPOraQuery.FieldByName('FELD').AsString;
      FachEdit.Text := LPOraQuery.FieldByName('PLATZ').AsString;
      EbeneEdit.Text := LPOraQuery.FieldByName('EBENE').AsString;
      TiefeEdit.Text := LPOraQuery.FieldByName('TIEFE').AsString;

      if (BarcodeEdit.Visible) then
        BarcodeEdit.Text := LPOraQuery.FieldByName ('BARCODE_ID').AsString;

      if (ScanableCheckBox.Visible) then
        ScanableCheckBox.State := CheckOptState (LPOraQuery.FieldByName ('OPT_SCANABLE').AsString, 1);

      FolgeEdit.Text := LPOraQuery.FieldByName('FOLGE_NR').AsString;

      FBLEdit.Text := LPOraQuery.FieldByName('L').AsString;
      FBBEdit.Text := LPOraQuery.FieldByName('B').AsString;
      FBHEdit.Text := LPOraQuery.FieldByName('H').AsString;

      LTAnzUpDown.Position  := DBGetIntegerNull (LPOraQuery.FieldByName('LT_CAPACITY'));
      LPPrioUpDown.Position := LPOraQuery.FieldByName('EINLAGER_PRIO').AsInteger;

      MaxGewichtEdit.Text := LPOraQuery.FieldByName('MAX_GEWICHT').AsString;

      RBGKoorEdit.Text := LPOraQuery.FieldByName('RBG_KOOR').AsString;
      WWSPlatzEdit.Text := LPOraQuery.FieldByName('WWS_KOOR').AsString;

      if (ABCComboBox.Visible) then begin
        if LPOraQuery.FieldByName ('ABC_KLASSE').IsNull then
          ABCComboBox.ItemIndex := 0
        else begin
          ABCComboBox.ItemIndex := ABCComboBox.Items.IndexOf (LPOraQuery.FieldByName ('ABC_KLASSE').AsString);
          if (ABCComboBox.ItemIndex = -1) then ABCComboBox.ItemIndex := 0;
        end;
      end;

      if not (ScanableCheckBox.Visible) then
        scan := cbGrayed
      else
        scan := ScanableCheckBox.State;

      if StapelFaktorEdit.Visible then begin
        if LPOraQuery.FieldByName ('STAPEL_FAKTOR').IsNull then
          StapelFaktorEdit.Text := ''
        else
          StapelFaktorUpDown.Position := LPOraQuery.FieldByName ('STAPEL_FAKTOR').AsInteger;
      end;
    end else begin
      reflb := GetComboBoxRef (LBComboBox1);

      LPArtComboBox.ItemIndex := FindComboboxRef (LPArtComboBox, GetLBArtLPType (GetComboBoxRef (LBComboBox1)));
      if (LPArtComboBox.ItemIndex = -1) then LPArtComboBox.ItemIndex := 0;

      if (LPHKLComboBox.ItemIndex = -1) then LPHKLComboBox.ItemIndex := 0;

      ReiheEdit.Enabled := True;
      FeldEdit.Enabled  := True;
      FachEdit.Enabled  := True;
      EbeneEdit.Enabled := True;
      TiefeEdit.Enabled := TiefeEdit.Visible;

      NrEdit.Text := '';
      NameEdit.Text := '';

      LTAnzUpDown.Position := 1;
    end;
  end;

  if (EditLPForm.ShowModal = mrOk) Then begin
    with EditLPForm do begin
      if (LPHKLComboBox.ItemIndex = 0) then
        hkl := -1
      else if not (TryStrToInt (LPHKLComboBox.GetItemText, hkl)) then
        hkl := -1;

      if (LTAnzUpDown.Position = 0) then
        ltanz := -1
      else
        ltanz := LTAnzUpDown.Position;

      if (Length (FolgeEdit.Text) = 0) then
        folge_nr := -1
      else if not (TryStrToInt64 (FolgeEdit.Text, folge_nr)) then
        folge_nr := -1;

      if (Length (MaxGewichtEdit.Text) = 0) then
        gw := -1
      else if not (TryStrToInt (MaxGewichtEdit.Text, gw)) then
        gw := -1
      else
        gw := gw * 1000;

      if (Length (FBLEdit.Text) = 0) then
        l := -1
      else
        l := StrToInt (FBLEdit.Text);

      if (Length (FBBEdit.Text) = 0) then
        b := -1
      else
        b := StrToInt (FBBEdit.Text);

      if (Length (FBHEdit.Text) = 0) then
        h := -1
      else
        h := StrToInt (FBHEdit.Text);


      if not (ReiheEdit.Enabled) then
        reihe := ''
      else
        reihe := Copy (ReiheEdit.Text, 1, 3);

      if not (FeldEdit.Enabled) then
        feld := -1
      else if (Length (FeldEdit.Text) = 0) then
        feld := -1
      else
        feld := StrToInt (FeldEdit.Text);

      if not (FachEdit.Enabled) then
        platz := -1
      else if (Length (FachEdit.Text) = 0) then
        platz := -1
      else
        platz := StrToInt (FachEdit.Text);

      if not (EbeneEdit.Enabled) then
        ebene := -1
      else if (Length (EbeneEdit.Text) = 0) then
        ebene := -1
      else
        ebene := StrToInt (EbeneEdit.Text);

      if not (TiefeEdit.Visible) then
        tiefe := -1
      else if not (TiefeEdit.Enabled) then
        tiefe := -1
      else if (Length (TiefeEdit.Text) = 0) then
        tiefe := -1
      else
        tiefe := StrToInt (TiefeEdit.Text);

      if (ABCComboBox.Visible and (ABCComboBox.ItemIndex >= 0)) then
        abc := ABCComboBox.Text
      else
        abc := '';

      if not (ScanableCheckBox.Visible) then
        scan := cbGrayed
      else
        scan := ScanableCheckBox.State;

      if not (StapelFaktorEdit.Visible and StapelFaktorEdit.Enabled) then
        stabelfaktor := -1
      else if (StapelFaktorUpDown.Position = 0) then
        stabelfaktor := -1
      else
        stabelfaktor := StapelFaktorUpDown.Position;

      if ABCComboBox.Visible then
        res := CreateLagerplatzKoord (reflb,
                                      GetComboBoxRef (LBZoneComboBox),
                                      NrEdit.Text,
                                      StellplatzNrEdit.Text,
                                      reihe, feld, platz, ebene, tiefe,
                                      NameEdit.Text,
                                      GetComboBoxRef (LPArtComboBox),
                                      l, b, h,
                                      ltanz,
                                      gw,
                                      folge_nr,
                                      LPPrioUpDown.Position,
                                      hkl,
                                      stabelfaktor,
                                      abc,
                                      scan,
                                      BarcodeEdit.Text,
                                      RBGKoorEdit.Text,
                                      WWSPlatzEdit.Text,
                                      ref)
      else if StapelFaktorEdit.Visible then
        res := CreateLagerplatzKoord (reflb,
                                      GetComboBoxRef (LBZoneComboBox),
                                      NrEdit.Text,
                                      StellplatzNrEdit.Text,
                                      reihe, feld, platz, ebene, tiefe,
                                      NameEdit.Text,
                                      GetComboBoxRef (LPArtComboBox),
                                      l, b, h,
                                      ltanz,
                                      gw,
                                      folge_nr,
                                      LPPrioUpDown.Position,
                                      hkl,
                                      stabelfaktor,
                                      BarcodeEdit.Text,
                                      RBGKoorEdit.Text,
                                      WWSPlatzEdit.Text)
      else
        res := CreateLagerplatzKoord (reflb,
                                      GetComboBoxRef (LBZoneComboBox),
                                      NrEdit.Text,
                                      StellplatzNrEdit.Text,
                                      reihe, feld, platz, ebene, tiefe,
                                      NameEdit.Text,
                                      GetComboBoxRef (LPArtComboBox),
                                      l, b, h,
                                      ltanz,
                                      gw,
                                      folge_nr,
                                      LPPrioUpDown.Position,
                                      hkl,
                                      BarcodeEdit.Text,
                                      RBGKoorEdit.Text,
                                      WWSPlatzEdit.Text);
    end;

    if (res = 0) Then
      LPDBGrid.Reload
    else FrontendMessages.MessageDlg (FormatMessageText (1224, [LVSDatenModul.LastLVSErrorText]), mtError, [mbOK], 0);
  end;

  EditLPForm.Free;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TLagerTopologieForm.LagerCfgButtonClick(Sender: TObject);
var
  cfgsetfrom : TConfigSetupForm;
begin
  cfgsetfrom := TConfigSetupForm.Create (Self);

  cfgsetfrom.Label1.Caption := GetResourceText (1227) + ' : ' + LagerStringGrid.Cells [2, LagerStringGrid.Row];
  cfgsetfrom.Label2.Caption := '';

  cfgsetfrom.Prepare ('', False, TRUE, FALSE, LagerStringGrid.Cells [2, LagerStringGrid.Row], TLagerGridEntry (LagerStringGrid.Rows [LagerStringGrid.Row].Objects [0]).RefLoc);
  cfgsetfrom.ShowModal;

  cfgsetfrom.Free;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TLagerTopologieForm.LagerCommButtonClick(Sender: TObject);
var
  partnerform : TKomPartnerForm;
begin
  if (LagerStringGrid.Row <> -1) then begin
    partnerform := TKomPartnerForm.Create (Self);

    partnerform.Mandant := '';
    partnerform.Lager   := LagerStringGrid.Cells [2, LagerStringGrid.Row];

    try
      partnerform.ShowModal;
    except
    end;

    partnerform.Release;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TLagerTopologieForm.LocComboBoxChange(Sender: TObject);
begin
  fAktLocationRef := GetComboBoxRef(LocComboBox);

  UpdateLagerGrid (Sender);
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TLagerTopologieForm.TabSheet5Show(Sender: TObject);
var
  idx   : Integer;
begin
  ClearGridObjects (LocStringGrid);

  ADOQuery1.SQL.Clear;
  ADOQuery1.SQL.Add ('select REF, NAME, BESCHREIBUNG from V_PCD_LOCATION');

  if (LVSDatenModul.AktLocationRef <> -1) then
    ADOQuery1.SQL.Add ('where REF='+IntToStr (LVSDatenModul.AktLocationRef));

  ADOQuery1.SQL.Add ('order by NAME');

  idx := 0;

  ADOQuery1.Open;
  while not (ADOQuery1.Eof) do begin
    LocStringGrid.Cells [1, LocStringGrid.FixedRows + idx] := ADOQuery1.Fields [1].AsString;
    LocStringGrid.Cells [2, LocStringGrid.FixedRows + idx] := ADOQuery1.Fields [2].AsString;

    LocStringGrid.Rows[LocStringGrid.FixedRows + idx].Objects [0] := TGridRef.Create (ADOQuery1.Fields [0].AsInteger);

    Inc (idx);

    ADOQuery1.Next;
  end;
  ADOQuery1.Close;

  if (idx > 0) then
    LocStringGrid.RowCount := LocStringGrid.FixedRows + idx
  else begin
    LocStringGrid.RowCount := LocStringGrid.FixedRows + 1;
    LocStringGrid.Rows [LocStringGrid.FixedRows].Clear;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TLagerTopologieForm.TabSheet5Resize(Sender: TObject);
begin
  with LocStringGrid do begin
    ColWidths [1] := ((ClientWidth - ColCount * 1 - ColWidths [0]) * 30) div 100;
    ColWidths [2] := ((ClientWidth - ColCount * 1 - ColWidths [0]) * 70) div 100;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TLagerTopologieForm.LocStringGridDblClick(Sender: TObject);
var
  ref       : Integer;
  editform  : TEditLocationForm;
begin
  if Assigned (LocStringGrid.Rows [LocStringGrid.Row].Objects [0]) then begin
    ref := TGridRef (LocStringGrid.Rows [LocStringGrid.Row].Objects [0]).Ref;

    editform := TEditLocationForm.Create (Self);

    editform.PrepareEdit (ref);

    if (editform.ShowModal = mrOK) then begin
      TabSheet5Show (Self)
    end;

    editform.Free;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TLagerTopologieForm.NewLocButtonClick(Sender: TObject);
var
  editform  : TEditLocationForm;
begin
  editform := TEditLocationForm.Create (Self);

  editform.PrepareEdit (-1);

  if (editform.ShowModal = mrOK) then begin
    TabSheet5Show (Self)
  end;

  editform.Free;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TLagerTopologieForm.SperrenLPButtonClick(Sender: TObject);
var
  res,
  count,
  dlgres    : Integer;
  flag      : Boolean;
  abort     : Boolean;
  opt       : String;
  query     : TADOQuery;
  grundform : TSperrGrundForm;
begin
  if (LPOraQuery.Active) and (LPOraQuery.RecNo > 0) then begin
    grundform := TSperrGrundForm.Create (Self);

    if (LPDBGrid.SelectedRows.Count > 0) then begin
      grundform.Prepare (-1, GetResourceText (1443), GetResourceText (1444), 'LOCK_LP');

      if (grundform.ShowModal = mrOk) Then begin
        LPDBGrid.DataSource.DataSet.DisableControls;

        try
          res := DBGridUtils.DoSelectedRows (LPDBGrid, SperrenLP, '00', Pointer (grundform.Grund))
        finally
          LPDBGrid.DataSource.DataSet.EnableControls;
        end;

        if (res <> 0) then
          FrontendMessages.MessageDlg (FormatMessageText (1225, [LVSDatenModul.LastLVSErrorText]), mtError, [mbOK], 0)
        else begin
          (*
          Screen.Cursor := crSQLWait;

          try
            LPDBGrid.Reload;
          finally
            Screen.Cursor := crDefault;
          end;
          *)
        end;
      end;
    end else begin
      query := TADOQuery.Create (Self);

      try
        query.LockType := ltReadOnly;
        query.Connection := LVSDatenModul.MainADOConnection;

        query.SQL.Add ('select count (*) from V_BES where STATUS<>''DEL'' and nvl (REF_LE_LP, REF_LP)=:ref_lp');
        query.Parameters [0].Value := LPOraQuery.FIeldByName ('REF').AsInteger;

        query.Open;

        count := query.Fields [0].AsInteger;

        query.Close;
      finally
        query.Free;
      end;

      if (count = 0) then
        dlgres := mrYes
      else
        dlgres := FrontendMessages.MessageDlg (FormatMessageText (1797, []), mtConfirmation, [mbYes,mbNo,mbCancel], 0);

      if (dlgres = mrYes) then begin
        grundform.Prepare (-1, GetResourceText (1445), GetResourceText (1444), 'LOCK_LP');

        if (grundform.ShowModal = mrOk) Then begin
          flag  := True;
          abort := False;
          opt   := '10';

          res := SperrenLP (LPDBGrid, Pointer (grundform.GrundComboBox.Text), Nil, opt, flag, abort);
          if (res <> 0) then
            FrontendMessages.MessageDlg (FormatMessageText (1225, [LVSDatenModul.LastLVSErrorText]), mtError, [mbOK], 0)
          else begin
            LPOraQuery.RefreshRecord;
          end;
        end;
      end;
    end;

    grundform.Release;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TLagerTopologieForm.LPListBoxDragOver(Sender, Source: TObject; X, Y: Integer; State: TDragState; var Accept: Boolean);
begin

end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TLagerTopologieForm.KommDBGridDragOver(Sender, Source: TObject; X, Y: Integer; State: TDragState; var Accept: Boolean);
begin
  Accept := (Source = LPListBox);
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TLagerTopologieForm.KommDBGridDragDrop(Sender, Source: TObject; X, Y: Integer);
begin
  if (Source = LPListBox) then begin
    KommLPAnlegen;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function TLagerTopologieForm.KommLPAnlegen : Integer;
var
  res,
  idx   : Integer;
begin
  res := 0;

  if (LPListBox.SelCount > 0) then begin
    idx := 0;

    while (idx < LPListBox.Items.Count) and (res = 0) do begin
      if (LPListBox.Selected [idx]) then begin
        res := CreateKommPlatz (GetComboBoxRef (LagerComboBox3), GetListBoxRef (LPListBox, idx));
      end;

      Inc (idx);
    end;

    if (res <> 0) then
      FrontendMessages.MessageDlg (FormatMessageText (1226, [LVSDatenModul.LastLVSErrorText]), mtError, [mbOK], 0);

    LBComboBoxChange (LBComboBox2);
  end else if (LPListBox.ItemIndex <> -1) then begin
    idx := LPListBox.ItemIndex;

    res := CreateKommPlatz (GetComboBoxRef (LagerComboBox3), GetListBoxRef (LPListBox, idx));

    if (res <> 0) then
      FrontendMessages.MessageDlg (FormatMessageText (1226, [LVSDatenModul.LastLVSErrorText]), mtError, [mbOK], 0)
    else begin
      LPListBox.ItemIndex := idx + 1;

      LPListBox.Items.Delete(idx);

      KommDBGrid.Reload;

      KommDBGrid.DataSource.DataSet.Locate('REF_LP', GetListBoxRef (LPListBox, idx), []);
    end;
  end;

  Result := res;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TLagerTopologieForm.PrintLPButtonClick(Sender: TObject);
var
  prtform : TPrintLPLableForm;
  idx     : Integer;
  bookptr,
  savemark: TBookmark;
begin
  prtform := TPrintLPLableForm.Create (self);

  prtform.Prepare(UserReg.ReadRegValue ('LP-PRINTER'), 'E9', -1, LVSDatenModul.AktLocationRef, LPOraQuery.FieldByName ('REF_LAGER').AsInteger, -1);

  if (prtform.PrinterComboBox.Items.Count = 0) Then
    FrontendMessages.MessageDlg(FormatMessageText (1227, ['Format E9']), mtError, [mbOK], 0)
  else begin
    if (LPDBGrid.SelectedRows.Count = 0) Then begin
      prtform.LPList.Add (LPDBGrid.DataSource.DataSet.FieldByName ('REF').AsString);
    end else begin
      LPDBGrid.DataSource.DataSet.DisableControls;

      try
        try
          savemark := LPDBGrid.DataSource.DataSet.GetBookmark;

          idx := 0;

          while (idx < LPDBGrid.SelectedRows.Count) do begin
            bookptr := {$ifndef DELPHIXE10_UP} pointer {$endif}  (LPDBGrid.SelectedRows.Items[idx]);

            if not (Assigned (bookptr)) then
              Inc (idx)
            else begin
              LPDBGrid.DataSource.DataSet.GotoBookmark(bookptr);

              prtform.LPList.Add (LPDBGrid.DataSource.DataSet.FieldByName ('REF').AsString);

              LPDBGrid.SelectedRows.CurrentRowSelected := False;
            end;
          end;

          LPDBGrid.DataSource.DataSet.GotoBookmark(savemark);
          LPDBGrid.DataSource.DataSet.FreeBookmark(savemark);
        except
        end;
      finally
        LPDBGrid.DataSource.DataSet.EnableControls;
      end;
    end;

    prtform.KommLPTabSheet.TabVisible := False;
    prtform.LPTabSheet.TabVisible := True;
    prtform.PageControl1.ActivePage := prtform.LPTabSheet;

    prtform.ShowModal;
  end;

  UserReg.WriteRegValue ('LP-PRINTER', prtform.GetSelectedPrinter);

  prtform.Free;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TLagerTopologieForm.KommLPPrintButtonClick(Sender: TObject);
var
  prtform : TPrintLPLableForm;
begin
  prtform := TPrintLPLableForm.Create (self);

  prtform.DownRadioButton.Checked := True;

  prtform.KommLPTabSheet.TabVisible := True;
  prtform.PageControl1.ActivePage := prtform.KommLPTabSheet;

  prtform.KommLPNrLabel.Caption := KommADOQuery.FieldByName ('LP_NR').AsString;

  ADOQuery1.SQL.Clear;
  ADOQuery1.SQL.Add ('select ARTIKEL_NR, ARTIKEL_TEXT, EINHEIT, REF_AR_MAND, AR_AZP from V_KOMM_LP_ZUORDNUNG where REF_PLANUNG is null and REF_KOMM_LP='+KommADOQuery.FieldByName ('REF').AsString);
  ADOQuery1.SQL.Add ('order by REF_KOMM_REL');

  try
    ADOQuery1.Open;

    if (ADOQuery1.Fields [3].IsNull) Then
      prtform.Prepare (PrintModule.VPELabelPrinter.Name, 'E7', -1, LVSDatenModul.AktLocationRef, KommADOQuery.FieldByName ('REF_LAGER').AsInteger, -1)
    else prtform.Prepare (PrintModule.VPELabelPrinter.Name, 'E7', ADOQuery1.Fields [3].AsInteger, LVSDatenModul.AktLocationRef, KommADOQuery.FieldByName ('REF_LAGER').AsInteger, -1);

    if (prtform.PrinterComboBox.Items.Count = 0) Then
      FrontendMessages.MessageDlg(FormatMessageText (1227, ['Format E7']), mtError, [mbOK], 0)
    else begin
      prtform.LPList.Add (KommADOQuery.FieldByName ('REF').AsString);

      if (ADOQuery1.Fields [3].IsNull) Then begin
        prtform.KommArNrLabel.Caption    := '';
        prtform.KommArTextLabel.Caption  := '';
        prtform.KommArVPELabel.Caption   := '';
        prtform.KommArAZPLabel.Caption   := '';
        prtform.KommArSTVPELabel.Caption := '';
      end else begin
        prtform.KommArNrLabel.Caption   := ADOQuery1.Fields [0].AsString;
        prtform.KommArTextLabel.Caption := ADOQuery1.Fields [1].AsString;
        prtform.KommArVPELabel.Caption  := ADOQuery1.Fields [2].AsString;
        prtform.KommArAZPLabel.Caption  := ADOQuery1.Fields [4].AsString;

        if (ADOQuery1.RecordCount > 1) then begin
          ADOQuery1.Next;
          prtform.KommArSTVPELabel.Caption := ADOQuery1.Fields [2].AsString + ' = ' + ADOQuery1.Fields [0].AsString;
        end else
          prtform.KommArSTVPELabel.Caption := '';
      end;

      prtform.LPTabSheet.TabVisible := False;
      prtform.PageControl1.ActivePage := prtform.KommLPTabSheet;

      prtform.ShowModal;
    end;
  except
    FrontendMessages.MessageDlg(FormatMessageText (1251, [LVSDatenModul.LastLVSErrorText]), mtError, [mbOK], 0)
  end;

  ADOQuery1.Close;

  prtform.Free;
end;

procedure TLagerTopologieForm.LagerCheckConfigMenuItemClick(Sender: TObject);
var
  ref       : Integer;
  checkform : TCheckConfigForm;
begin
  if Assigned (LagerStringGrid.Rows [LagerStringGrid.Row].Objects [0]) then begin
    ref := TLagerGridEntry (LagerStringGrid.Rows [LagerStringGrid.Row].Objects [0]).Ref;

    checkform := TCheckConfigForm.Create (Self);

    checkform.RefMand  := LVSDatenModul.AktMandantRef;
    checkform.RefLager := ref;

    checkform.ShowModal;

    checkform.Release;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TLagerTopologieForm.LPListBoxDrawItem(Control: TWinControl; Index: Integer; Rect: TRect; State: TOwnerDrawState);
var
  line : String;
  strpos : Integer;
begin
  line := (Control as TListBox).Items [Index];
  strpos := Pos ('|', line);

  with (Control as TListBox).Canvas do begin
    FillRect(Rect);

    TextOut (Rect.Left, Rect.Top, Copy (line,1,strpos - 1));
    TextOut (Rect.Left + 80, Rect.Top, Copy (line,strpos + 1, Length (line) - strpos));
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 29.02.2024
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TLagerTopologieForm.LPNotUniqueMenuItemClick(Sender: TObject);
begin
  if (LBComboBox1.ItemIndex > 0) Then
    UpdateLPQuery (GetComboBoxRef (LagerComboBox2), GetComboBoxRef (LBComboBox1))
  else UpdateLPQuery (GetComboBoxRef (LagerComboBox2), -1);
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TLagerTopologieForm.ChangeTypenButtonClick(Sender: TObject);
var
  changeform : TChangeLPZuordnungForm;
begin
  changeform := TChangeLPZuordnungForm.Create(Self);

  changeform.Prepare (GetComboBoxRef (LagerComboBox2));

  changeform.ShowModal;

  changeform.Release;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 21.05.2017
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TLagerTopologieForm.CompTranslateForm1ChangeLanguage(Sender: TObject);
begin
  LocStringGrid.TitelTexte [1] := GetResourceText(1223);
  LocStringGrid.TitelTexte [2] := GetResourceText(1287);

  LagerStringGrid.TitelTexte [1] := GetResourceText(1223);
  LagerStringGrid.TitelTexte [2] := GetResourceText(1227);
  LagerStringGrid.TitelTexte [3] := GetResourceText(1288);
  LagerStringGrid.TitelTexte [4] := GetResourceText(1287);
  LagerStringGrid.TitelTexte [5] := GetResourceText(1289);
  LagerStringGrid.TitelTexte [6] := GetResourceText(1290);
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TLagerTopologieForm.CopyLocButtonClick(Sender: TObject);
var
  origref   : Integer;
  editform  : TEditLocationForm;
begin
  if Assigned (LocStringGrid.Rows [LocStringGrid.Row].Objects [0]) then begin
    origref := TGridRef (LocStringGrid.Rows [LocStringGrid.Row].Objects [0]).Ref;

    editform := TEditLocationForm.Create (Self);

    editform.PrepareCopy (origref);

    if (editform.ShowModal = mrOK) then begin
      TabSheet5Show (Self)
    end;

    editform.Free;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function TLagerTopologieForm.SperrenLP (DBGrid : TDBGridPro; Daten : Pointer; InfoForm : TForm; var Option : String; var Done : Boolean; var Abort : Boolean) : Integer;
var
  res,
  count,
  dlgres : Integer;
  query  : TADOQuery;
begin
  res := 0;

  if not (Abort) then begin
    query := TADOQuery.Create (Self);

    try
      query.LockType := ltReadOnly;
      query.Connection := LVSDatenModul.MainADOConnection;

      query.SQL.Add ('select count (*) from V_BES where STATUS<>''DEL'' and nvl (REF_LE_LP, REF_LP)=:ref_lp');
      query.Parameters [0].Value := DBGrid.DataSource.DataSet.FIeldByName ('REF').AsInteger;

      query.Open;

      count := query.Fields [0].AsInteger;

      query.Close;
    finally
      query.Free;
    end;

    if (count = 0) then
      dlgres := mrYes
    else
      dlgres := FrontendMessages.MessageDlg (FormatMessageText (1797, []), mtConfirmation, [mbYes,mbNo,mbCancel], 0);

    if (dlgres = mrCancel) then begin
      Abort := true;
      Done := False;
    end else if (dlgres = mrYes) then begin
      res := SperrenLagerplatz (DBGrid.DataSource.DataSet.FieldByName ('REF').AsInteger, String (Daten));

      Done := (res = 0);
    end else begin
      Done := False;
    end;
  end;

  Result := res;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function TLagerTopologieForm.FreigebenLP (DBGrid : TDBGridPro; Daten : Pointer; InfoForm : TForm; var Option : String; var Done : Boolean; var Abort : Boolean) : Integer;
var
  res : Integer;
begin
  res := 0;

  if not (Abort) then begin
    res := FreigabeLagerplatz (DBGrid.DataSource.DataSet.FieldByName ('REF').AsInteger);

    Done := (res = 0);
  end else begin
    Done := False;
  end;

  Result := res;
end;

procedure TLagerTopologieForm.FreigebenLPButtonClick(Sender: TObject);
var
  res       : Integer;
  flag      : Boolean;
  abort     : Boolean;
  opt       : String;
begin
  if (LPOraQuery.Active) and (LPOraQuery.RecNo > 0) then begin
    if (LPDBGrid.SelectedRows.Count > 0) then begin
      LPDBGrid.DataSource.DataSet.DisableControls;

      try
        res := DBGridUtils.DoSelectedRows (LPDBGrid, FreigebenLP, '')
      finally
        LPDBGrid.DataSource.DataSet.EnableControls;
      end;

      if (res <> 0) then
        FrontendMessages.MessageDlg (FormatMessageText (1252, [LVSDatenModul.LastLVSErrorText, LPDBGrid.DataSource.DataSet.FieldByName ('LP_DISP').AsString]), mtError, [mbOK], 0)
    end else begin
      flag  := True;
      abort := False;
      opt   := '';

      res := FreigebenLP (LPDBGrid, Nil, Nil, opt, flag, abort);
      if (res <> 0) then
        FrontendMessages.MessageDlg (FormatMessageText (1252, [LVSDatenModul.LastLVSErrorText, LPDBGrid.DataSource.DataSet.FieldByName ('LP_DISP').AsString]), mtError, [mbOK], 0)
      else begin
        LPOraQuery.RefreshRecord;
      end;
    end;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function TLagerTopologieForm.DeleteLP (DBGrid : TDBGridPro; Daten : Pointer; InfoForm : TForm; var Option : String; var Done : Boolean; var Abort : Boolean) : Integer;
var
  res,
  count,
  dlgres : Integer;
begin
  res := 0;

  if Assigned (fProgressForm) and (fProgressForm.AbortFlag) then begin
    Done := False;
    Abort := True
  end else begin
    Done := True;

    //Prüfen, ob Bestand auf dem LP liegt
    ADOQuery1.SQL.Clear;
    ADOQuery1.SQL.Add ('select count (*) from V_BES_LP where REF_LP=:ref');
    ADOQuery1.Parameters.Items [0].Value := DBGrid.DataSource.DataSet.FieldByName ('REF').AsInteger;

    ADOQuery1.Open;

    count := ADOQuery1.Fields [0].AsInteger;

    ADOQuery1.Close;

    if (count > 0) then begin
      if ((Length (Option) >= 4) and (Option [4] = '1')) then
        dlgres := mrYesToAll
      else begin
        dlgres := FrontendMessages.MessageDlg (FormatMessageText (1168, [DBGrid.DataSource.DataSet.FieldByName ('LP_NR').AsString]), mtConfirmation, [mbYes,mbYesToAll,mbNo,mbCancel], 0);

        if (dlgres = mrYesToAll) then begin
          while (Length (Option) < 4) do Option := Option + '0';
          Option [4] := '1';
        end;
      end;

      if (dlgres = mrCancel) then
        Abort := True
      else if (dlgres = mrNo) then
        Done := False
      else begin
        ADOQuery1.SQL.Clear;
        ADOQuery1.SQL.Add ('select REF from V_BES_LP where REF_LP=:ref');
        ADOQuery1.Parameters.Items [0].Value := DBGrid.DataSource.DataSet.FieldByName ('REF').AsInteger;

        ADOQuery1.Open;

        while not (ADOQuery1.Eof) and (res = 0) do begin
          res := BestandAbbuchenLP (ADOQuery1.Fields[0].AsInteger, DBGrid.DataSource.DataSet.FieldByName ('REF').AsInteger, 'Delete LP');

          ADOQuery1.Next;
        end;

        ADOQuery1.Close;
      end;
    end;

    if (res = 0) and (Done) and not (Abort) then begin
      //Prüfen ob LEs auf dem Platz stehen
      ADOQuery1.SQL.Clear;
      ADOQuery1.SQL.Add ('select count (*) from V_LE where STATUS<>''DEL'' and REF_LP=:ref');
      ADOQuery1.Parameters.Items [0].Value := DBGrid.DataSource.DataSet.FieldByName ('REF').AsInteger;

      ADOQuery1.Open;

      count := ADOQuery1.Fields [0].AsInteger;

      ADOQuery1.Close;

      if (count > 0) then begin
        if ((Length (Option) >= 3) and (Option [3] = '1')) then
          dlgres := mrYesToAll
        else begin
          if (count > 1) then
            dlgres := FrontendMessages.MessageDlg (FormatMessageText (1167, [DBGrid.DataSource.DataSet.FieldByName ('LP_DISP').AsString, IntToStr (count)]), mtConfirmation, [mbYes,mbYesToAll,mbNo,mbCancel], 0)
          else dlgres := FrontendMessages.MessageDlg (FormatMessageText (1166, [DBGrid.DataSource.DataSet.FieldByName ('LP_DISP').AsString]), mtConfirmation, [mbYes,mbYesToAll,mbNo,mbCancel], 0);

          if (dlgres = mrYesToAll) then begin
            while (Length (Option) < 3) do Option := Option + '0';
            Option [3] := '1';
          end;
        end;

        if (dlgres = mrCancel) then
          Abort := True
        else if (dlgres = mrNo) then
          Done := False
        else begin
          ADOQuery1.SQL.Clear;
          ADOQuery1.SQL.Add ('select REF, REF_LAGER from V_LE where STATUS<>''DEL'' and REF_LP=:ref');
          ADOQuery1.Parameters.Items [0].Value := DBGrid.DataSource.DataSet.FieldByName ('REF').AsInteger;

          ADOQuery1.Open;

          while not (ADOQuery1.Eof) and (res = 0) do begin
            res := LEUmbuchen (ADOQuery1.Fields[1].AsInteger, ADOQuery1.Fields[0].AsInteger, -1, -1);

            ADOQuery1.Next;
          end;

          ADOQuery1.Close;
        end;
      end;
    end;

    if (res = 0) and (Done) and not (Abort) then begin
      count := 0;

      if ((Length (Option) < 1) or (Option [1] = '0')) then begin
        //Prüfen ob dem Komm-Platz Artikelzugeordnet sind
        ADOQuery1.SQL.Clear;
        ADOQuery1.SQL.Add ('select count (*) from V_KOMM_LP_AR where REF_LP=:ref');
        ADOQuery1.Parameters.Items [0].Value := DBGrid.DataSource.DataSet.FieldByName ('REF').AsInteger;

        ADOQuery1.Open;

        count := ADOQuery1.Fields [0].AsInteger;

        ADOQuery1.Close;
      end;

      if (count > 0) then begin
        dlgres := FrontendMessages.MessageDlg(FormatMessageText (1126, [DBGrid.DataSource.DataSet.FieldByName ('LP_DISP').AsString]), mtConfirmation, [mbYes,mbYesToAll,mbNo,mbCancel], 0);

        if (dlgres = mrYesToAll) then begin
          while (Length (Option) < 1) do Option := Option + '0';
          Option [1] := '1';
        end;
      end else begin
        count := 0;

        if ((Length (Option) < 2) or (Option [2] = '0')) then begin
          //Prüfen ob der LP ein Komm-Platz ist
          ADOQuery1.SQL.Clear;
          ADOQuery1.SQL.Add ('select count (*) from V_KOMM_LP where REF_LP=:ref');
          ADOQuery1.Parameters.Items [0].Value := DBGrid.DataSource.DataSet.FieldByName ('REF').AsInteger;

          ADOQuery1.Open;

          count := ADOQuery1.Fields [0].AsInteger;

          ADOQuery1.Close;
        end;

        if (count > 0) then begin
          dlgres := FrontendMessages.MessageDlg (FormatMessageText (1125, [DBGrid.DataSource.DataSet.FieldByName ('LP_DISP').AsString]), mtConfirmation, [mbYes,mbYesToAll,mbNo,mbCancel], 0);

          if (dlgres = mrYesToAll) then begin
            while (Length (Option) < 2) do Option := Option + '0';
            Option [2] := '1';
          end;
        end else if (Length (Option) < 1) or (Option [1] = '0') then begin
          dlgres := FrontendMessages.MessageDlg (FormatMessageText (1124, [DBGrid.DataSource.DataSet.FieldByName ('LP_DISP').AsString]), mtConfirmation, [mbYes,mbYesToAll,mbNo,mbCancel], 0);

          if (dlgres = mrYesToAll) then begin
            while (Length (Option) < 1) do Option := Option + '0';
            Option [1] := '1';
          end;
        end else begin
          dlgres := mrYes;
        end;
      end;

      if (dlgres = mrYes) or (dlgres = mrYesToAll) then begin
        Done := True;

        res := DeleteLagerplatz (DBGrid.DataSource.DataSet.FieldByName ('REF').AsInteger);

        if Assigned (fProgressForm) then begin
          fProgressForm.ProgressBar1.Position := fProgressForm.ProgressBar1.Position + 1;

          Application.ProcessMessages;
        end;
      end else if (dlgres = mrCancel) then begin
        Abort := True;
      end else begin
        Done := False;
      end;
    end;
  end;

  Result := res;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TLagerTopologieForm.DelLPButtonClick(Sender: TObject);
var
  res     : Integer;
  opt     : String;
  flag,
  abort   : Boolean;
begin
  if (LPOraQuery.Active) and (LPOraQuery.RecNo > 0) then begin
    res := 0;

    if (LPDBGrid.SelectedRows.Count > 0) then begin
      fProgressForm := TInfoWin.Create (Self);

      try
        fProgressForm.Label1.Caption := GetResourceText (1325);
        fProgressForm.ButtonPanel.Visible := True;
        fProgressForm.ProgressPanel.Visible := True;
        fProgressForm.ProgressBar1.Position := 0;
        fProgressForm.ProgressBar1.Max := LPDBGrid.SelectedRows.Count;

        fProgressForm.BeginShowModal;

        Screen.Cursor := crSQLWait;

        LPOraQuery.DisableControls;

        try
          LVSDatenModul.BeginTransaction (LVSDatenModul.MainADOConnection);

          try
            res := DBGridUtils.DoSelectedRows (LPDBGrid, DeleteLP, '');

            if (res = 0) then
              LVSDatenModul.EndTransaction (LVSDatenModul.MainADOConnection, trCommit)
            else begin
              LVSDatenModul.EndTransaction (LVSDatenModul.MainADOConnection, trRollback);
            end;
          except
            res := -9;

            LVSDatenModul.EndTransaction (LVSDatenModul.MainADOConnection, trRollback);
          end;
        finally
          LPOraQuery.EnableControls;

          Screen.Cursor := crDefault;
        end;

        fProgressForm.EndShowModal;
      finally
        if Assigned (fProgressForm) then
          fProgressForm.Free;

        fProgressForm := Nil;
      end;

      if (res <> 0) then
        FrontendMessages.MessageDlg (FormatMessageText (1228, [LVSDatenModul.LastLVSErrorText, LPDBGrid.DataSource.DataSet.FieldByName ('LP_DISP').AsString]), mtError, [mbOK], 0)
      else begin
        if (LPDBGrid.DataSource.DataSet.Eof) then
          LPDBGrid.DataSource.DataSet.Prior
        else LPDBGrid.DataSource.DataSet.Next;
      end;

      LPDBGrid.Reload
    end else begin
      flag  := True;
      abort := False;

      res := DeleteLP (LPDBGrid, Nil, Nil, opt, flag, abort);

      if (res <> 0) then
        FrontendMessages.MessageDlg (FormatMessageText (1228, [LVSDatenModul.LastLVSErrorText, LPDBGrid.DataSource.DataSet.FieldByName ('LP_DISP').AsString]), mtError, [mbOK], 0)
      else begin
        if (LPDBGrid.DataSource.DataSet.Eof) then
          LPDBGrid.DataSource.DataSet.Prior
        else LPDBGrid.DataSource.DataSet.Next;

        LPDBGrid.Reload
      end;
    end;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TLagerTopologieForm.EditLPButtonClick(Sender: TObject);
begin
  LPDBGridDblClick (Sender);
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TLagerTopologieForm.Einlagerplatzanlegen1Click(Sender: TObject);
var
  res          : Integer;
  reihe        : String;
  lpnr,
  feld, platz,
  ebene,
  reflp        : Integer;
  stabelfaktor : Integer;
  query        : TADOQuery;
  createform   : TCreateNachschubPlatzForm;
begin
  if (KommDBGrid.SelectedRows.Count > 0) or (KommDBGrid.DataSource.DataSet.RecNo > 0) then begin
    createform := TCreateNachschubPlatzForm.Create (Self);
    createform.LagerRef := KommADOQuery.FieldByName ('REF_LAGER').AsInteger;

    LoadLBComboboxNullEntry (createform.LBComboBox, '', '', createform.LagerRef);

    if (createform.ShowModal = mrOk) Then begin
      query := TADOQuery.Create (Self);

      try
        query.LockType := ltReadOnly;
        query.Connection := LVSDatenModul.MainADOConnection;

        query.SQL.Add ('select * from V_LP where REF=:ref');
        query.Parameters.ParamByName('ref').Value := KommADOQuery.FieldByName ('REF_LP').AsInteger;

        query.Open;

        res := CreateLagerplatz      (GetComboBoxRef (createform.LBComboBox),
                                      GetComboBoxRef (createform.LBZoneComboBox),
                                      '',
                                      '',
                                      createform.NameEdit.Text,
                                      copy (createform.RegalEdit.Text,1 , 3),
                                      DBGetIntegerNull (query.FieldByName ('FELD')), DBGetIntegerNull (query.FieldByName ('PLATZ')), DBGetIntegerNull (query.FieldByName ('EBENE')), -1,
                                      GetComboBoxRef (createform.LPArtComboBox),
                                      -1, -1, -1,
                                      -1,
                                      -1,
                                      -1,
                                      -1,
                                      -1,
                                      -1,
                                      '',
                                      '',
                                      '',
                                      reflp);
        if (res <> 0) then
          FrontendMessages.MessageDlg (FormatMessageText (1224, [LVSDatenModul.LastLVSErrorText]), mtError, [mbOK], 0)
        else begin
          res := SetKommNachschubPlatz (KommADOQuery.FieldByName ('REF').AsInteger, reflp);

          if (res <> 0) then
            FrontendMessages.MessageDlg (FormatMessageText (1229, [LVSDatenModul.LastLVSErrorText]), mtError, [mbOK], 0)
          else
            KommDBGrid.Reload;
        end;

        query.Close
      finally
        query.Free;
      end;
    end;

    createform.Free;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 06.05.2017
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TLagerTopologieForm.ExportLPDataMenuItemClick(Sender: TObject);
var
  idx,
  res       : Integer;
  outstr    : AnsiString;
  bookptr,
  savemark  : TBookmark;
  fname     : String;
  fstream   : TFileStream;
begin
  res := 0;

  fname := OpenExcelFile (UserReg.ReadRegValue ('ExportPath'), '', GetResourceText (1800), false);

  if (Length (fname) > 0) then begin
    UserReg.WriteRegValue ('ExportPath', ExtractFilePath (fname));

    try
      fstream := TFileStream.Create (fname, fmCreate);
    except
      fstream := Nil;
      FrontendMessages.MessageDlg (FormatMessageText (1526, [fname]), mtError, [mbOk], 0);
    end;

    if Assigned (fstream) then begin
      Screen.Cursor := crHourGlass;

      try
        LPDBGrid.DataSource.DataSet.DisableControls;

        try
          try
            outstr := GetResourceText (1801);

            outstr := outstr + #13 + #10;

            fstream.Write (outstr[1], Length (outstr));

            savemark := LPDBGrid.DataSource.DataSet.GetBookmark;

            idx := 0;

            while (idx < LPDBGrid.SelectedRows.Count) do begin
              bookptr := {$ifndef DELPHIXE10_UP} pointer {$endif} (LPDBGrid.SelectedRows.Items[idx]);

              if not (Assigned (bookptr)) then
                Inc (idx)
              else begin
                LPDBGrid.DataSource.DataSet.GotoBookmark(bookptr);

                with LPDBGrid.DataSource.DataSet do begin
                  outstr := '"'+FieldByName('BEREICH').AsString+'";"'+FieldByName('BEREICH_ZONE').AsString+'"';
                  outstr := outstr + ';"'+FieldByName('REIHE').AsString+'";'+FieldByName('FELD').AsString+';'+FieldByName('PLATZ').AsString+';'+FieldByName('EBENE').AsString;
                  outstr := outstr + ';"'+FieldByName('NAME').AsString+'";'+FieldByName('LP_NR').AsString+';'+FieldByName('FOLGE_NR').AsString+';"'+FieldByName('STELLPLATZ_NR').AsString+'";"'+FieldByName('BARCODE_ID').AsString+'"';
                  outstr := outstr + ';"'+FieldByName('HOEHEN_KLASSE').AsString+'";"'+FieldByName('LP_ART').AsString+'";'+FieldByName('LT_CAPACITY').AsString;

                  if (FieldByName('STELLPLATZ_NR').AsString = 'SPERR') then
                    outstr := outstr + ';1'
                  else
                    outstr := outstr + ';';

                  outstr := outstr + ';'+FieldByName('STAPEL_FAKTOR').AsString;

                  if Assigned (FindField ('ABC_KLASSE')) then
                    outstr := outstr+';"'+FieldByName('ABC_KLASSE').AsString+'"'
                  else
                    outstr := outstr+';';

                  if Assigned (FindField ('OPT_SCANABLE')) then
                    outstr := outstr+';'+FieldByName('OPT_SCANABLE').AsString
                  else
                    outstr := outstr + ';';

                  if not Assigned (FindField ('L')) then
                    outstr := outstr + ';'
                  else if FieldByName('L').IsNull then
                    outstr := outstr + ';'
                  else
                    outstr := outstr+';'+FieldByName('L').AsString;

                  if not Assigned (FindField ('B')) then
                    outstr := outstr + ';'
                  else if FieldByName('B').IsNull then
                    outstr := outstr + ';'
                  else
                    outstr := outstr+';'+FieldByName('B').AsString;

                  if not Assigned (FindField ('H')) then
                    outstr := outstr + ';'
                  else if FieldByName('H').IsNull then
                    outstr := outstr + ';'
                  else
                    outstr := outstr+';'+FieldByName('H').AsString;
                end;

                outstr := UTF8Encode (outstr) + #13 + #10;

                fstream.Write (outstr[1], Length (outstr));

                LPDBGrid.SelectedRows.CurrentRowSelected := False;
              end;
            end;

            LPDBGrid.DataSource.DataSet.GotoBookmark(savemark);
            LPDBGrid.DataSource.DataSet.FreeBookmark(savemark);
          except
          end;
        finally
          LPDBGrid.DataSource.DataSet.EnableControls;
        end;

        if (res <> 0) then
          FrontendMessages.MessageDlg (FormatMessageText (1388, [fname]), mtError, [mbOK], 0);
      finally
        fstream.Free;

        Screen.Cursor := crDefault;
      end;
    end;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TLagerTopologieForm.FormClose(Sender: TObject; var Action: TCloseAction);
begin
  LPOraQuery.Close;
  KommADOQuery.Close;

  if Assigned (DBGridUtils) Then
    DBGridUtils.StoreDBGrids (Self);

  LVSConfigModul.SaveFormParameter (Self, 'LPTabSplitter', LPDatenPanel.Height);

  LVSConfigModul.SaveGridInfo (Self, LocStringGrid);
  LVSConfigModul.SaveGridInfo (Self, LagerStringGrid);

  LVSConfigModul.SaveFormInfo (Self);
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TLagerTopologieForm.KommFolgeButtonClick(Sender: TObject);
var
  res,
  dlgres  : Integer;
  art     : String;
  defform : TDefKommFolgeForm;
begin
  defform := TDefKommFolgeForm.Create (Self);

  LoadLagerCombobox (defform.LagerComboBox);
  defform.LagerComboBox.ItemIndex := FindComboboxRef (defform.LagerComboBox, GetComboBoxRef (LagerComboBox2));
  defform.LagerComboBox.OnChange (defform.LagerComboBox);

  if (LBComboBox1.ItemIndex > 0) then
    defform.LBComboBox.ItemIndex := FindComboboxDBItemWert (defform.LBComboBox, GetComboBoxDBItemWert (LBComboBox1))
  else defform.LBComboBox.ItemIndex := 0;
  defform.LBComboBox.OnChange (defform.LBComboBox);

  if (defform.ShowModal = mrOk) Then begin
    if not (defform.SetLPNrCheckBox.Checked) then
      dlgres := mrYes
    else
      dlgres := FrontendMessages.MessageDlg (FormatMessageText (1129, []), mtWarning, [mbYes, mbNo, mbCancel], 0);

    if (dlgres = mrYes) then begin
      with defform do begin
        if (AufRadioButton.Checked) Then
          art := 'A'
        else art := 'D';

        if (ProFachRadioButton.Checked) Then
          art := art + 'E'
        else art := art + 'F';

        res := SetFolgeNummern (GetComboBoxRef(LBComboBox),
                                ReiheComboBox.Text,
                                StrToInt (PlatzVonEdit.Text),
                                StrToInt (PlatzBisEdit.Text),
                                -1,
                                -1,
                                StrToInt (EbeneVonEdit.Text),
                                StrToInt (EbeneBisEdit.Text),
                                art,
                                StrToInt (StartEdit.Text),
                                StrToInt (StepEdit.Text),
                                SetLPNrCheckBox.Checked);
      end;

      if (res = 0) Then
        LPDBGrid.Reload
      else FrontendMessages.MessageDlg (FormatMessageText (1438, [LVSDatenModul.LastLVSErrorText]), mtError, [mbOK], 0);
    end;
  end;

  defform.Release;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function TLagerTopologieForm.LPDBGridColumnSort(Sender: TCustomDBGridPro; const ColumnName: String): String;
begin
  if (ColumnName = 'FOLGE_NR') then
    Result := 'LPAD ('+ColumnName+',9,''0'')'
  else if (ColumnName = 'LP_NR') then
    Result := 'LPAD ('+ColumnName+',9,''0'')'
  else Result := ColumnName;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TLagerTopologieForm.LagerMandButtonClick(Sender: TObject);
var
  res,
  idx     : Integer;
  relfrom : TMandantRelLagerForm;
begin
  relfrom := TMandantRelLagerForm.Create (Self);

  relfrom.Caption := FormatResourceText (1517, [LagerStringGrid.Cells [1,LagerStringGrid.Row]]);

  ADOQuery1.SQL.Clear;
  ADOQuery1.SQL.Add ('select REF,NAME,BESCHREIBUNG from V_PCD_MANDANT');

  try
    ADOQuery1.Open;

    while not (ADOQuery1.Eof) do begin
      relfrom.MandantCheckListBox.Items.AddObject (ADOQuery1.Fields [1].AsString+'|'+ADOQuery1.Fields [2].AsString, TListBoxRef.Create(ADOQuery1.Fields [0].AsInteger));

      ADOQuery1.Next;
    end;

    ADOQuery1.Close;

    ADOQuery1.SQL.Clear;
    ADOQuery1.SQL.Add ('select REF_MAND from V_MANDANT_REL_LAGER where REF_LAGER=:ref');
    ADOQuery1.Parameters [0].Value := TGridRef (LagerStringGrid.Rows [LagerStringGrid.Row].Objects [0]).Ref;

    ADOQuery1.Open;

    while not (ADOQuery1.Eof) do begin
      idx := 0;

      while (idx < relfrom.MandantCheckListBox.Items.Count) do begin
        if (GetListBoxRef (relfrom.MandantCheckListBox, idx) = ADOQuery1.Fields [0].AsInteger) Then begin
          relfrom.MandantCheckListBox.Checked [idx] := True;

          idx := relfrom.MandantCheckListBox.Items.Count;
        end else Inc (idx);
      end;

      ADOQuery1.Next;
    end;

    ADOQuery1.Close;
  except
  end;

  if (relfrom.ShowModal = mrOk) then begin
    idx := 0;
    res := 0;

    while (idx < relfrom.MandantCheckListBox.Items.Count) and (res = 0) do begin
      if (relfrom.MandantCheckListBox.Checked [idx]) then
        res := DefMandantLager (GetListBoxRef (relfrom.MandantCheckListBox, idx), TGridRef (LagerStringGrid.Rows [LagerStringGrid.Row].Objects [0]).Ref, '1')
      else res := DefMandantLager (GetListBoxRef (relfrom.MandantCheckListBox, idx), TGridRef (LagerStringGrid.Rows [LagerStringGrid.Row].Objects [0]).Ref, '0');

      Inc (Idx);
    end;
  end;

  relfrom.Release;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function TLagerTopologieForm.KommDBGridColumnSort(Sender: TCustomDBGridPro; const ColumnName: string): string;
begin
  if (ColumnName = 'FOLGE_NR') then
    Result := 'LPAD ('+ColumnName+',9,''0'')'
  else if (ColumnName = 'LP_NR') then
    Result := 'LPAD ('+ColumnName+',9,''0'')'
  else Result := ColumnName;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf                                         abacus1

//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function UpdateKommLP (DBGrid : TDBGridPro; Daten : Pointer; InfoForm : TForm; var Option : String; var Done : Boolean; var Abort : Boolean) : Integer;
var
  res : Integer;
begin
  res := SetKommPlatzEigenschaften (DBGrid.DataSource.DataSet.FieldByName ('REF').AsInteger, TKommLPDaten (Daten^).KommFolge, TKommLPDaten (Daten^).MaxAnzahl);

  Done  := (res = 0);
  Abort := (res <> 0);

  Result := res;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TLagerTopologieForm.KommDBGridDblClick(Sender: TObject);
var
  res      : Integer;
  editform : TEditKommLPForm;
  ResRec   : TKommLPDaten;
begin
  if (KommDBGrid.SelectedRows.Count > 0) or (KommDBGrid.DataSource.DataSet.RecNo > 0) then begin
    editform := TEditKommLPForm.Create (Self);

    try
      if (KommDBGrid.SelectedRows.Count > 0) then begin
        editform.Caption := GetResourceText (1545);

        editform.AnzahlEdit.Text := '';
        editform.FolgeEdit.Text  := '';
      end else begin
        editform.Caption := FormatResourceText (1546, [KommADOQuery.FieldByName ('LP_NR').AsString]);

        editform.AnzahlEdit.Text := KommADOQuery.FieldByName ('MAX_ARTIKEL_COUNT').AsString;
        editform.FolgeEdit.Text  := KommADOQuery.FieldByName ('KOMM_FOLGE').AsString;
      end;

      if (editform.ShowModal = mrOk) then begin
        if (Length (editform.AnzahlEdit.Text) = 0) then
          ResRec.MaxAnzahl := 0
        else if not (TryStrToInt (editform.AnzahlEdit.Text, ResRec.MaxAnzahl)) then
          ResRec.MaxAnzahl := 1;

        if (Length (editform.FolgeEdit.Text) = 0) then
          ResRec.KommFolge := -1
        else if not (TryStrToInt (editform.FolgeEdit.Text, ResRec.KommFolge)) then
          ResRec.KommFolge := -1;

        if (KommDBGrid.SelectedRows.Count > 0) then begin
          KommDBGrid.DataSource.DataSet.DisableControls;

          try
            res := DBGridUtils.DoSelectedRows (KommDBGrid, UpdateKommLP, '', @ResRec)
          finally
            KommDBGrid.DataSource.DataSet.EnableControls;
          end;

          if (res <> 0) Then
            FrontendMessages.MessageDlg (FormatMessageText (1437, [LVSDatenModul.LastLVSErrorText]), mtError, [mbOK], 0);
        end else begin
          res := SetKommPlatzEigenschaften (KommADOQuery.FieldByName ('REF').AsInteger, ResRec.KommFolge, ResRec.MaxAnzahl);

          if (res <> 0) Then
            FrontendMessages.MessageDlg (FormatMessageText (1436, [LVSDatenModul.LastLVSErrorText]), mtError, [mbOK], 0);
        end;

        KommDBGrid.Reload;
      end;
    finally
      editform.Release;
    end;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TLagerTopologieForm.DrawLPButtonClick(Sender: TObject);
var
  drawform: TDrawLPForm;
begin
  drawform := TDrawLPForm.Create (Self);

  try
    drawform.Prepare;
    drawform.LagerComboBox.ItemIndex := FindComboboxRef (drawform.LagerComboBox, GetComboBoxRef (LagerComboBox2));

    drawform.LagerComboBox.OnChange (drawform.LagerComboBox);

    drawform.LBComboBox.ItemIndex := FindComboboxDBItemWert (drawform.LBComboBox, GetComboBoxDBItemWert (LBComboBox1));

    drawform.ShowModal;
  finally
    drawform.Release;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 03.0.2022
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TLagerTopologieForm.LPArAddButtonClick(Sender: TObject);
var
  res,
  ref    : Integer;
  ardata : TArtikeDaten;
  arlist : TArtikelListeForm;
begin
  if (LPOraQuery.Active and (LPOraQuery.RecNo > 0)) then begin
    arlist := TArtikelListeForm.Create (Self);

    try
      arlist.Caption := GetResourceText (1859);

      arlist.ShowCollis      := True;
      arlist.ShowMultiCollis := True;

      arlist.RefMand      := LVSDatenModul.AktMandantRef;
      arlist.RefLager     := GetComboBoxRef (LagerComboBox2);

      arlist.SubMandPanel.Visible := LVSConfigModul.UseSubMandanten;

      arlist.BeschaffungCheckBox.Checked := False;
      arlist.BeschaffungCheckBox.Enabled := True;

      if (arlist.ShowModal = mrOK) then begin
          ardata.RefArtikel := arlist.ArtikelQuery.FieldByName ('REF').AsInteger;
          ardata.RefEinheit := arlist.ArtikelQuery.FieldByName ('REF_EINHEIT').AsInteger;

        if LPDBGrid.SelectedRows.Count > 0 then begin
          res := DBGridUtils.DoSelectedRows (LPDBGrid, AssignARLP, '', @ardata);

          if (res <> 0) then
            FrontendMessages.MessageDlg (FormatMessageText (1798, [LVSDatenModul.LastLVSErrorText]), mtError, [mbOK], 0)
          else
            LPArtikelDBGrid.Reload
        end else if (LPOraQuery.RecNo > 0) then begin
          res := AssignArtikelLP (ardata.RefArtikel, ardata.RefEinheit, LPOraQuery.FieldByName('REF').AsInteger, ref);

          if (res <> 0) then
            FrontendMessages.MessageDlg (FormatMessageText (1798, [LVSDatenModul.LastLVSErrorText]), mtError, [mbOK], 0)
          else
            LPArtikelDBGrid.Reload (ref);
        end;
      end;
    finally
      arlist.Release;
    end;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 29.09.2022
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TLagerTopologieForm.LPARDelButtonClick(Sender: TObject);
var
  res     : Integer;
  opt     : String;
  flag,
  abort   : Boolean;
begin
  if (LPArtikelQuery.Active) and (LPArtikelQuery.RecNo > 0) then begin
    if (FrontendMessages.MessageDlg(FormatMessageText (1819, [LPDBGrid.DataSource.DataSet.FieldByName ('LP_DISP').AsString, LPArtikelQuery.FieldByName ('ARTIKEL_NR').AsString]), mtConfirmation, [mbYes,mbNo,mbCancel], 0) = mrYes) then begin
      res := DeleteArtikelLPAssign (LPArtikelQuery.FieldByName ('REF').AsInteger);

      if (res <> 0) then
        FrontendMessages.MessageDlg (FormatMessageText (1228, [LVSDatenModul.LastLVSErrorText, LPDBGrid.DataSource.DataSet.FieldByName ('LP_DISP').AsString, LPArtikelQuery.FieldByName ('ARTIKEL_NR').AsString]), mtError, [mbOK], 0)
      else begin
        if (LPDBGrid.DataSource.DataSet.Eof) then
          LPDBGrid.DataSource.DataSet.Prior
        else LPDBGrid.DataSource.DataSet.Next;

        LPDBGrid.Reload
      end;
    end;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 04.04.2022
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TLagerTopologieForm.LPARNachschubMenuItemClick(Sender: TObject);
var
  editform : TEditNachschubParamForm;
begin
  if (LPArtikelDataSource.DataSet.Active and (LPArtikelDataSource.DataSet.RecNo > 0)) then begin
    editform := TEditNachschubParamForm.Create (Self);

    try
      editform.RefLPRel  := LPArtikelDataSource.DataSet.FieldByName ('REF').AsInteger;

      if (editform.ShowModal = mrOk) then
        LPArtikelDBGrid.Reload;
    finally
      editform.Release;
    end;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TLagerTopologieForm.LPDataSourceDataChange(Sender: TObject; Field: TField);
begin
  EditLPButton.Enabled  := False;
  DelLPButton.Enabled   := False;
  PrintLPButton.Enabled := False;
  SperrenLPButton.Enabled := False;
  FreigebenLPButton.Enabled := False;

  if (LPOraQuery.Active) and (LPOraQuery.RecNo > 0) then begin
    EditLPButton.Enabled  := True;
    DelLPButton.Enabled   := True;
    PrintLPButton.Enabled := True;

    if (LPDBGrid.SelectedRows.Count > 0) then begin
      SperrenLPButton.Enabled := True;
      FreigebenLPButton.Enabled := True;
    end else begin
      SperrenLPButton.Enabled := (LPOraQuery.FieldByName ('STATUS').AsString <> 'SPERR');
      FreigebenLPButton.Enabled := not (SperrenLPButton.Enabled);
    end;

    if LPArtikelPanel.Visible then begin
      LPArtikelQuery.Close;
      LPArtikelQuery.Params.ParamByName('REF_LP').Value := LPOraQuery.FieldByName ('REF').AsInteger;
      LPArtikelQuery.Open;

      LPArtikelDBGrid.SetColumnVisible('MANDANT', not (LVSDatenModul.AktMandantRef > 0));
      LPArtikelDBGrid.SetColumnVisible('AUTO_NACHSCHUB', false);
    end;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TLagerTopologieForm.FormKeyDown(Sender: TObject; var Key: Word; Shift: TShiftState);
var
  flag : Boolean;
begin
  if (Key = VK_F5) then begin
    PageControl1Changing (Sender, flag);

    if Assigned (PageControl1.ActivePage.OnShow) then
      PageControl1.ActivePage.OnShow (Sender);
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TLagerTopologieForm.PageControl1Changing(Sender: TObject; var AllowChange: Boolean);
begin
  if (PageControl1.ActivePage = TabSheet5) Then begin
    if (LocStringGrid.Row <> -1) and Assigned (LocStringGrid.Rows [LocStringGrid.Row].Objects [0]) Then
      fAktLocationRef := TGridRef (LocStringGrid.Rows [LocStringGrid.Row].Objects [0]).Ref
    else
      fAktLocationRef := -1
  end else if (PageControl1.ActivePage = LagerTabSheet) Then begin
    fAktLocationRef := GetComboBoxRef(LocComboBox);

    if (LagerStringGrid.Row <> -1) and Assigned (LagerStringGrid.Rows [LagerStringGrid.Row].Objects [0]) Then
      fAktLagerRef := TGridRef (LagerStringGrid.Rows [LagerStringGrid.Row].Objects [0]).Ref
    else
      fAktLagerRef := -1;
  end else if (PageControl1.ActivePage = LBTabSheet) Then begin
    fAktLagerRef    := GetComboBoxRef(LagerComboBox1);

    if (LBQuery.Active) and (LBQuery.RecNo > 0) then
      fAktBereichRef  := LBQuery.FieldByName ('REF').AsInteger;
  end else if (PageControl1.ActivePage = LPTabSheet) Then begin
    fAktLagerRef    := GetComboBoxRef(LagerComboBox2);
    fAktBereichRef  := GetComboBoxRef(LBComboBox1);
  end else if (PageControl1.ActivePage = KommTabSheet) Then begin
    fAktLagerRef    := GetComboBoxRef(LagerComboBox3);
    fAktBereichRef  := GetComboBoxRef(LBComboBox2);
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TLagerTopologieForm.DelLBButtonClick(Sender: TObject);
var
  res : Integer;
begin
  if (LBQuery.Active) and (LBQuery.RecNo > 0) then begin
    if (FrontendMessages.MessageDlg(FormatMessageText (1130, []), mtConfirmation, [mbYes,mbNo,mbCancel], 0) = mrYes) then begin
      res := DeleteLB (LBQuery.FieldByName ('REF').AsInteger);

      if (res <> 0) Then
        FrontendMessages.MessageDlg (FormatMessageText (1435, [LVSDatenModul.LastLVSErrorText]), mtError, [mbOK], 0)
      else LBDBGrid.Reload;
    end;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TLagerTopologieForm.LBTabSheetResize(Sender: TObject);
begin
 LagerComboBox1.Width := LBDBGrid.Width;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TLagerTopologieForm.LPTabSheetResize(Sender: TObject);
begin
  LBComboBox1.Left := LPDBGrid.Left + LPDBGrid.Width - LBComboBox1.Width;
  Label2.Left := LBComboBox1.Left;

  LagerComboBox2.Width := LBComboBox1.Left - LagerComboBox2.Left - 10;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TLagerTopologieForm.KommTabSheetResize(Sender: TObject);
begin
  LBComboBox2.Left := KommDBGrid.Left + KommDBGrid.Width - LBComboBox2.Width;
  Label8.Left := LBComboBox2.Left;

  LagerComboBox3.Width := LBComboBox2.Left - LagerComboBox3.Left - 10;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TLagerTopologieForm.ScannerErfassung (var Message: TMessage);
var
  lpstr : String;
begin
  if (PageControl1.ActivePage = LPTabSheet) then begin
    if (((Length(ScanCode) = 13) and (ScanCode[1] = ITFID)) or ((Length(ScanCode) = 12) and (ScanCode[1] = Code128ID))) and (ScanCode[2] = '4') then begin
      lpstr := Copy (ScanCode, 3 , 3)+'-'+Copy (ScanCode, 6 , 2)+'-'+Copy (ScanCode, 8 , 2)+'-'+Copy (ScanCode, 10 , 2);

      try
        LPDBGrid.DataSource.DataSet.Locate('LP_KOOR', lpstr, []);
      except
      end;
    end else if CheckLPNrBarcode (ScanCode, lpstr) then begin
      try
        LPDBGrid.DataSource.DataSet.Locate('LP_NR', lpstr, []);
      except
      end;
    end;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 18.01.2021
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TLagerTopologieForm.StringGridColOptimalMenuItemClick(Sender: TObject);
var
  popup : TPopupMenu;
begin
  popup := ((Sender as TMenuItem).GetParentMenu as TPopupMenu);

  if Assigned (popup) and Assigned (popup.PopupComponent) and (popup.PopupComponent is TStringGridPro) then begin
    with (popup.PopupComponent as TStringGridPro) do
      SetGridOptimalColWidth;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 18.01.2021
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TLagerTopologieForm.StringGridCopyColMenuItemClick(Sender: TObject);
var
  acol,
  arow    : Integer;
  popup   : TPopupMenu;
  clipstr : String;
begin
  popup := ((Sender as TMenuItem).GetParentMenu as TPopupMenu);

  if Assigned (popup) and Assigned (popup.PopupComponent) and (popup.PopupComponent is TStringGridPro) then begin
    with (popup.PopupComponent as TStringGridPro) do begin
      MouseToCell (MouseX, MouseY, acol, arow);

      if (acol >= FixedCols) and (acol < ColCount) and (arow >= FixedRows) and (arow < RowCount) then begin
        clipstr := Cells [acol, arow];

        Clipboard.SetTextBuf (PChar (clipstr));
      end;
    end;
  end;
end;

end.
