object SpedGatewayForm: TSpedGatewayForm
  Left = 0
  Top = 0
  Caption = 'Konfiguratioen der Carrier Gateways'
  ClientHeight = 809
  ClientWidth = 981
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -12
  Font.Name = 'Segoe UI'
  Font.Style = []
  Position = poOwnerFormCenter
  OnActivate = FormActivate
  OnClose = FormClose
  OnCreate = FormCreate
  OnDestroy = FormDestroy
  OnShow = FormShow
  TextHeight = 15
  object Label76: TLabel
    Left = 16
    Top = 19
    Width = 32
    Height = 15
    Caption = 'Name'
  end
  object Label80: TLabel
    Left = 16
    Top = 75
    Width = 32
    Height = 15
    Caption = 'Name'
  end
  object SpedGatewayDBGrid: TDBGridPro
    AlignWithMargins = True
    Left = 8
    Top = 49
    Width = 965
    Height = 190
    Margins.Left = 8
    Margins.Top = 8
    Margins.Right = 8
    Margins.Bottom = 8
    Align = alClient
    DataSource = SpedGatewayDataSource
    Options = [dgTitles, dgIndicator, dgColumnResize, dgColLines, dgRowLines, dgTabs, dgRowSelect, dgAlwaysShowSelection, dgConfirmDelete, dgCancelOnExit]
    PopupMenu = SpedGatewayMenu
    ReadOnly = True
    TabOrder = 0
    TitleFont.Charset = DEFAULT_CHARSET
    TitleFont.Color = clWindowText
    TitleFont.Height = -12
    TitleFont.Name = 'Segoe UI'
    TitleFont.Style = []
    Flat = False
    BandsFont.Charset = DEFAULT_CHARSET
    BandsFont.Color = clWindowText
    BandsFont.Height = -11
    BandsFont.Name = 'Tahoma'
    BandsFont.Style = []
    Groupings = <>
    GridStyle.Style = gsCustom
    GridStyle.OddColor = clWindow
    GridStyle.EvenColor = clWindow
    TitleHeight.PixelCount = 24
    FooterColor = clBtnFace
    ExOptions = [eoENTERlikeTAB, eoKeepSelection, eoBLOBEditor, eoTitleWordWrap]
    RegistryKey = 'Software\CS'
    RegistrySection = 'DBGrids'
    WidthOfIndicator = 11
    DefaultRowHeight = 19
    ScrollBars = ssHorizontal
    ColCount = 2
    RowCount = 2
  end
  object TopPanel: TPanel
    Left = 0
    Top = 0
    Width = 981
    Height = 41
    Align = alTop
    BevelOuter = bvNone
    TabOrder = 1
  end
  object CarrierPageControl: TPageControl
    Left = 0
    Top = 397
    Width = 981
    Height = 371
    ActivePage = SevenTabSheet
    Align = alBottom
    TabOrder = 2
    TabPosition = tpBottom
    object SendITTabSheet: TTabSheet
      Caption = 'SendITTabSheet'
      DesignSize = (
        973
        343)
      object Label1: TLabel
        Left = 8
        Top = 50
        Width = 70
        Height = 15
        Caption = 'SendIT Client'
      end
      object Label2: TLabel
        Left = 8
        Top = 79
        Width = 85
        Height = 15
        Caption = 'SendIT Location'
      end
      object Label34: TLabel
        Left = 8
        Top = 8
        Width = 146
        Height = 21
        Caption = 'SendIT Konfiguration'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -16
        Font.Name = 'Segoe UI'
        Font.Style = []
        ParentFont = False
      end
      object Bevel6: TBevel
        Left = 3
        Top = 32
        Width = 967
        Height = 10
        Anchors = [akLeft, akTop, akRight]
        Shape = bsTopLine
      end
      object Label63: TLabel
        Left = 425
        Top = 50
        Width = 81
        Height = 15
        Alignment = taRightJustify
        Caption = 'SendIT Produkt'
      end
      object Label64: TLabel
        Left = 405
        Top = 79
        Width = 101
        Height = 15
        Alignment = taRightJustify
        Caption = 'SendIT Produkt Int.'
      end
      object SendITLocComboBox: TComboBoxPro
        Left = 112
        Top = 76
        Width = 273
        Height = 22
        Style = csOwnerDrawFixed
        TabOrder = 0
        OnChange = SendITChange
      end
      object SendITClientComboBox: TComboBoxPro
        Left = 112
        Top = 47
        Width = 273
        Height = 22
        Style = csOwnerDrawFixed
        TabOrder = 1
        OnChange = SendITChange
      end
      object SendITPageControl: TPageControl
        Left = 0
        Top = 127
        Width = 973
        Height = 216
        ActivePage = SendITDHLTabSheet
        Align = alBottom
        TabOrder = 4
        TabPosition = tpBottom
        object SendITDHLTabSheet: TTabSheet
          Caption = 'SendITDHLTabSheet'
          DesignSize = (
            965
            188)
          object Label3: TLabel
            Left = 8
            Top = 14
            Width = 20
            Height = 15
            Caption = 'EKP'
          end
          object Label9: TLabel
            Left = 256
            Top = 14
            Width = 77
            Height = 15
            Caption = 'Frachtzentrum'
          end
          object Label37: TLabel
            Left = 635
            Top = 14
            Width = 54
            Height = 15
            Alignment = taRightJustify
            Anchors = [akTop, akRight]
            Caption = 'EDI-Ticket'
          end
          object SendITDHLEKPEdit: TEdit
            Left = 60
            Top = 11
            Width = 157
            Height = 23
            TabOrder = 0
            Text = 'SendITDHLEKPEdit'
          end
          object SendITDHLServicePageControl: TPageControl
            AlignWithMargins = True
            Left = 8
            Top = 48
            Width = 949
            Height = 137
            Margins.Left = 8
            Margins.Right = 8
            ActivePage = SendITDHL01TabSheet
            Align = alBottom
            TabOrder = 1
            object SendITDHL01TabSheet: TTabSheet
              Caption = 'National (01)'
              object Label4: TLabel
                Left = 4
                Top = 34
                Width = 18
                Height = 15
                Caption = 'ILN'
              end
              object Label5: TLabel
                Left = 4
                Top = 6
                Width = 59
                Height = 15
                Caption = 'Teilnehmer'
              end
              object Bevel2: TBevel
                Left = 353
                Top = 16
                Width = 16
                Height = 50
                Shape = bsLeftLine
              end
              object Label21: TLabel
                Left = 359
                Top = 19
                Width = 24
                Height = 15
                Caption = 'Start'
              end
              object Label22: TLabel
                Left = 359
                Top = 48
                Width = 26
                Height = 15
                Caption = 'Ende'
              end
              object Label23: TLabel
                Left = 496
                Top = 48
                Width = 30
                Height = 15
                Caption = 'Stand'
              end
              object SendITDHL01ILNEdit: TEdit
                Left = 72
                Top = 31
                Width = 121
                Height = 23
                MaxLength = 12
                TabOrder = 0
                Text = 'SendITDHL01ILNEdit'
              end
              object SendITDHLTeil01Edit: TEdit
                Left = 72
                Top = 3
                Width = 33
                Height = 23
                MaxLength = 2
                TabOrder = 1
                Text = 'SendITDHLTeil01Edit'
              end
              object SendITDHL01StartEdit: TEdit
                Left = 392
                Top = 16
                Width = 90
                Height = 23
                TabOrder = 2
                Text = 'SendITDHL01StartEdit'
              end
              object SendITDHL01EndeEdit: TEdit
                Left = 392
                Top = 45
                Width = 90
                Height = 23
                TabOrder = 3
                Text = 'SendITDHL01EndeEdit'
              end
              object SendITDHL01StandEdit: TEdit
                Left = 528
                Top = 45
                Width = 90
                Height = 23
                TabOrder = 4
                Text = 'SendITDHL01StandEdit'
              end
            end
            object SendITDHL07TabSheet: TTabSheet
              Caption = 'Retouren (07)'
              ImageIndex = 3
              object Label10: TLabel
                Left = 12
                Top = 7
                Width = 59
                Height = 15
                Caption = 'Teilnehmer'
              end
              object Label11: TLabel
                Left = 12
                Top = 35
                Width = 18
                Height = 15
                Caption = 'ILN'
              end
              object Label24: TLabel
                Left = 12
                Top = 63
                Width = 48
                Height = 15
                Caption = 'Kennzahl'
              end
              object Bevel3: TBevel
                Left = 252
                Top = 6
                Width = 16
                Height = 88
                Shape = bsLeftLine
              end
              object Label25: TLabel
                Left = 263
                Top = 10
                Width = 24
                Height = 15
                Alignment = taRightJustify
                Caption = 'Start'
              end
              object Label26: TLabel
                Left = 263
                Top = 39
                Width = 26
                Height = 15
                Alignment = taRightJustify
                Caption = 'Ende'
              end
              object Label27: TLabel
                Left = 260
                Top = 68
                Width = 30
                Height = 15
                Alignment = taRightJustify
                Caption = 'Stand'
              end
              object SendITDHLTeil07Edit: TEdit
                Left = 80
                Top = 4
                Width = 121
                Height = 23
                TabOrder = 0
                Text = 'SendITDHLTeil07Edit'
              end
              object SendITDHL07ILNEdit: TEdit
                Left = 80
                Top = 32
                Width = 121
                Height = 23
                TabOrder = 1
                Text = 'SendITDHL07ILNEdit'
              end
              object SendITDHL07KennEdit: TEdit
                Left = 80
                Top = 60
                Width = 53
                Height = 23
                MaxLength = 4
                TabOrder = 2
                Text = 'SendITDHL07KennEdit'
              end
              object SendITDHL07StandEdit: TEdit
                Left = 296
                Top = 62
                Width = 90
                Height = 23
                TabOrder = 3
                Text = 'SendITDHL07StandEdit'
              end
              object SendITDHL07EndeEdit: TEdit
                Left = 296
                Top = 36
                Width = 90
                Height = 23
                TabOrder = 4
                Text = 'SendITDHL07EndeEdit'
              end
              object SendITDHL07StartEdit: TEdit
                Left = 296
                Top = 7
                Width = 90
                Height = 23
                TabOrder = 5
                Text = 'SendITDHL07StartEdit'
              end
            end
            object SendITDHL53TabSheet: TTabSheet
              Caption = 'International (53)'
              ImageIndex = 1
              object Label6: TLabel
                Left = 12
                Top = 12
                Width = 59
                Height = 15
                Caption = 'Teilnehmer'
              end
              object Label7: TLabel
                Left = 12
                Top = 39
                Width = 48
                Height = 15
                Caption = 'Kennzahl'
              end
              object Label8: TLabel
                Left = 42
                Top = 68
                Width = 23
                Height = 15
                Caption = 'UPU'
              end
              object Bevel1: TBevel
                Left = 244
                Top = 3
                Width = 16
                Height = 88
                Shape = bsLeftLine
              end
              object Label18: TLabel
                Left = 255
                Top = 11
                Width = 24
                Height = 15
                Alignment = taRightJustify
                Caption = 'Start'
              end
              object Label19: TLabel
                Left = 255
                Top = 40
                Width = 26
                Height = 15
                Alignment = taRightJustify
                Caption = 'Ende'
              end
              object Label20: TLabel
                Left = 252
                Top = 69
                Width = 30
                Height = 15
                Alignment = taRightJustify
                Caption = 'Stand'
              end
              object SendITDHL53KennEdit: TEdit
                Left = 76
                Top = 36
                Width = 53
                Height = 23
                MaxLength = 4
                TabOrder = 0
                Text = 'SendITDHL53KennEdit'
              end
              object SendITDHLTeil53Edit: TEdit
                Left = 76
                Top = 9
                Width = 41
                Height = 23
                MaxLength = 2
                TabOrder = 1
                Text = 'SendITDHLTeil53Edit'
              end
              object SendITDHL53UPUEdit: TEdit
                Left = 76
                Top = 63
                Width = 39
                Height = 23
                MaxLength = 2
                TabOrder = 2
                Text = 'SendITDHL53UPUEdit'
              end
              object SendITDHL53StartEdit: TEdit
                Left = 288
                Top = 8
                Width = 90
                Height = 23
                TabOrder = 3
                Text = 'SendITDHL53StartEdit'
              end
              object SendITDHL53EndeEdit: TEdit
                Left = 288
                Top = 37
                Width = 90
                Height = 23
                TabOrder = 4
                Text = 'SendITDHL53EndeEdit'
              end
              object SendITDHL53StandEdit: TEdit
                Left = 288
                Top = 66
                Width = 90
                Height = 23
                TabOrder = 5
                Text = 'SendITDHL53StandEdit'
              end
            end
            object SendITDHL62TabSheet: TTabSheet
              Caption = 'Kleinpaket (62)'
              ImageIndex = 4
              object Label38: TLabel
                Left = 12
                Top = 14
                Width = 59
                Height = 15
                Caption = 'Teilnehmer'
              end
              object Label39: TLabel
                Left = 12
                Top = 42
                Width = 18
                Height = 15
                Caption = 'ILN'
              end
              object Bevel7: TBevel
                Left = 361
                Top = 24
                Width = 16
                Height = 50
                Shape = bsLeftLine
              end
              object Label40: TLabel
                Left = 367
                Top = 27
                Width = 24
                Height = 15
                Caption = 'Start'
              end
              object Label41: TLabel
                Left = 367
                Top = 56
                Width = 26
                Height = 15
                Caption = 'Ende'
              end
              object Label42: TLabel
                Left = 504
                Top = 56
                Width = 30
                Height = 15
                Caption = 'Stand'
              end
              object SendITDHLTeil62Edit: TEdit
                Left = 80
                Top = 11
                Width = 33
                Height = 23
                MaxLength = 2
                TabOrder = 0
                Text = 'SendITDHLTeil62Edit'
              end
              object SendITDHL62ILNEdit: TEdit
                Left = 80
                Top = 39
                Width = 121
                Height = 23
                MaxLength = 12
                TabOrder = 1
                Text = 'SendITDHL62ILNEdit'
              end
              object SendITDHL62StartEdit: TEdit
                Left = 400
                Top = 24
                Width = 90
                Height = 23
                TabOrder = 2
                Text = 'SendITDHL62StartEdit'
              end
              object SendITDHL62EndeEdit: TEdit
                Left = 400
                Top = 53
                Width = 90
                Height = 23
                TabOrder = 3
                Text = 'SendITDHL62EndeEdit'
              end
              object SendITDHL62StandEdit: TEdit
                Left = 536
                Top = 53
                Width = 90
                Height = 23
                TabOrder = 4
                Text = 'SendITDHL62StandEdit'
              end
            end
            object SendITDHL66TabSheet: TTabSheet
              Caption = 'Warenpost (66)'
              ImageIndex = 2
              object SendIT66StdGroupBox: TGroupBox
                Left = 3
                Top = 0
                Width = 454
                Height = 104
                Caption = 'Standart'
                TabOrder = 0
                object Label15: TLabel
                  Left = 20
                  Top = 22
                  Width = 59
                  Height = 15
                  Caption = 'Teilnehmer'
                end
                object Label16: TLabel
                  Left = 20
                  Top = 49
                  Width = 48
                  Height = 15
                  Caption = 'Kennzahl'
                end
                object Label17: TLabel
                  Left = 40
                  Top = 77
                  Width = 23
                  Height = 15
                  Caption = 'UPU'
                end
                object Bevel4: TBevel
                  Left = 260
                  Top = 14
                  Width = 16
                  Height = 88
                  Shape = bsLeftLine
                end
                object Label28: TLabel
                  Left = 271
                  Top = 18
                  Width = 24
                  Height = 15
                  Alignment = taRightJustify
                  Caption = 'Start'
                end
                object Label29: TLabel
                  Left = 271
                  Top = 47
                  Width = 26
                  Height = 15
                  Alignment = taRightJustify
                  Caption = 'Ende'
                end
                object Label30: TLabel
                  Left = 268
                  Top = 76
                  Width = 30
                  Height = 15
                  Alignment = taRightJustify
                  Caption = 'Stand'
                end
                object SendITDHLTeil66Edit: TEdit
                  Left = 84
                  Top = 18
                  Width = 37
                  Height = 23
                  MaxLength = 2
                  TabOrder = 0
                  Text = 'SendITDHLTeil66Edit'
                end
                object SendITDHL66KennEdit: TEdit
                  Left = 84
                  Top = 46
                  Width = 58
                  Height = 23
                  MaxLength = 4
                  TabOrder = 1
                  Text = 'SendITDHL66KennEdit'
                end
                object SendITDHL66UPUEdit: TEdit
                  Left = 84
                  Top = 74
                  Width = 35
                  Height = 23
                  MaxLength = 2
                  TabOrder = 2
                  Text = 'SendITDHL66UPUEdit'
                end
                object SendITDHL66StandEdit: TEdit
                  Left = 304
                  Top = 70
                  Width = 90
                  Height = 23
                  TabOrder = 3
                  Text = 'SendITDHL66StandEdit'
                end
                object SendITDHL66EndeEdit: TEdit
                  Left = 304
                  Top = 44
                  Width = 90
                  Height = 23
                  TabOrder = 4
                  Text = 'SendITDHL66EndeEdit'
                end
                object SendITDHL66StartEdit: TEdit
                  Left = 304
                  Top = 15
                  Width = 90
                  Height = 23
                  TabOrder = 5
                  Text = 'SendITDHL66StartEdit'
                end
              end
              object SendIT66PremiumGroupBox: TGroupBox
                Left = 520
                Top = 0
                Width = 426
                Height = 104
                Caption = 'Premium'
                TabOrder = 1
                object Label12: TLabel
                  Left = 12
                  Top = 18
                  Width = 59
                  Height = 15
                  Caption = 'Teilnehmer'
                end
                object Label13: TLabel
                  Left = 12
                  Top = 49
                  Width = 48
                  Height = 15
                  Caption = 'Kennzahl'
                end
                object Label14: TLabel
                  Left = 51
                  Top = 77
                  Width = 23
                  Height = 15
                  Caption = 'UPU'
                end
                object Bevel5: TBevel
                  Left = 268
                  Top = 12
                  Width = 16
                  Height = 88
                  Shape = bsLeftLine
                end
                object Label31: TLabel
                  Left = 279
                  Top = 22
                  Width = 24
                  Height = 15
                  Alignment = taRightJustify
                  Caption = 'Start'
                end
                object Label32: TLabel
                  Left = 279
                  Top = 51
                  Width = 26
                  Height = 15
                  Alignment = taRightJustify
                  Caption = 'Ende'
                end
                object Label33: TLabel
                  Left = 276
                  Top = 80
                  Width = 30
                  Height = 15
                  Alignment = taRightJustify
                  Caption = 'Stand'
                end
                object SendITDHLTeil66PremiumEdit: TEdit
                  Left = 80
                  Top = 15
                  Width = 41
                  Height = 23
                  MaxLength = 2
                  TabOrder = 0
                  Text = 'SendITDHLTeil66PremiumEdit'
                end
                object SendITDHL66PremiumKennEdit: TEdit
                  Left = 80
                  Top = 46
                  Width = 49
                  Height = 23
                  MaxLength = 4
                  TabOrder = 1
                  Text = 'SendITDHL66PremiumKennEdit'
                end
                object SendITDHL66PremiumUPUEdit: TEdit
                  Left = 80
                  Top = 74
                  Width = 32
                  Height = 23
                  MaxLength = 2
                  TabOrder = 2
                  Text = 'SendITDHL66PremiumUPUEdit'
                end
                object SendITDHL66PremiumStandEdit: TEdit
                  Left = 312
                  Top = 74
                  Width = 90
                  Height = 23
                  TabOrder = 3
                  Text = 'SendITDHL66PremiumStandEdit'
                end
                object SendITDHL66PremiumEndeEdit: TEdit
                  Left = 312
                  Top = 48
                  Width = 90
                  Height = 23
                  TabOrder = 4
                  Text = 'SendITDHL66PremiumEndeEdit'
                end
                object SendITDHL66PremiumStartEdit: TEdit
                  Left = 312
                  Top = 19
                  Width = 90
                  Height = 23
                  TabOrder = 5
                  Text = 'SendITDHL66PremiumStartEdit'
                end
              end
            end
          end
          object SendITDHLDepotEdit: TEdit
            Left = 340
            Top = 11
            Width = 117
            Height = 23
            TabOrder = 2
            Text = 'SendITDHLEKPEdit'
          end
          object SemdITDHLGoGreenCheckBox: TCheckBox
            Left = 508
            Top = 14
            Width = 97
            Height = 17
            Caption = 'GoGreen'
            TabOrder = 3
          end
          object SendITDHLTicketComboBox: TComboBoxPro
            Left = 696
            Top = 11
            Width = 257
            Height = 22
            Style = csOwnerDrawFixed
            Anchors = [akTop, akRight]
            TabOrder = 4
          end
        end
        object SendITDPDTabSheet: TTabSheet
          Caption = 'SendITDPDTabSheet'
          ImageIndex = 1
          object Label43: TLabel
            Left = 4
            Top = 19
            Width = 41
            Height = 15
            Caption = 'DelisNr.'
          end
          object Label44: TLabel
            Left = 4
            Top = 48
            Width = 48
            Height = 15
            Caption = 'DepotNr.'
          end
          object Label45: TLabel
            Left = 4
            Top = 77
            Width = 72
            Height = 15
            Caption = 'Nummerkreis'
          end
          object Bevel8: TBevel
            Left = 369
            Top = 32
            Width = 16
            Height = 50
            Shape = bsLeftLine
          end
          object Label46: TLabel
            Left = 375
            Top = 35
            Width = 24
            Height = 15
            Caption = 'Start'
          end
          object Label47: TLabel
            Left = 375
            Top = 64
            Width = 26
            Height = 15
            Caption = 'Ende'
          end
          object Label48: TLabel
            Left = 512
            Top = 64
            Width = 30
            Height = 15
            Caption = 'Stand'
          end
          object Label52: TLabel
            Left = 3
            Top = 118
            Width = 54
            Height = 15
            Alignment = taRightJustify
            Caption = 'EDI-Ticket'
          end
          object SendITDPDDelisEdit: TEdit
            Left = 108
            Top = 16
            Width = 121
            Height = 23
            TabOrder = 0
            Text = 'SendITDPDDelisEdit'
          end
          object SendITDPDDepotEdit: TEdit
            Left = 108
            Top = 45
            Width = 121
            Height = 23
            TabOrder = 1
            Text = 'SendITDPDDepotEdit'
          end
          object SendITDPDDepotNrEdit: TEdit
            Left = 108
            Top = 74
            Width = 121
            Height = 23
            TabOrder = 2
            Text = 'SendITDPDDepotNrEdit'
          end
          object SendITDPDEndeEdit: TEdit
            Left = 408
            Top = 61
            Width = 90
            Height = 23
            TabOrder = 3
            Text = 'SendITDPDEndeEdit'
          end
          object SendITDPDStartEdit: TEdit
            Left = 408
            Top = 32
            Width = 90
            Height = 23
            TabOrder = 4
            Text = 'SendITDPDStartEdit'
          end
          object SendITDPDStandEdit: TEdit
            Left = 544
            Top = 61
            Width = 90
            Height = 23
            TabOrder = 5
            Text = 'SendITDPDStandEdit'
          end
          object SendITDPDTicketComboBox: TComboBoxPro
            Left = 108
            Top = 115
            Width = 257
            Height = 22
            Style = csOwnerDrawFixed
            TabOrder = 6
          end
          object SemdITDPDGoGreenCheckBox: TCheckBox
            Left = 266
            Top = 19
            Width = 97
            Height = 17
            Caption = 'GoGreen'
            TabOrder = 7
          end
        end
        object SendITGLSTabSheet: TTabSheet
          Caption = 'SendITGLSTabSheet'
          ImageIndex = 2
          object Label49: TLabel
            Left = 12
            Top = 27
            Width = 83
            Height = 15
            Caption = 'GLS Kunden Nr.'
          end
          object Label50: TLabel
            Left = 12
            Top = 56
            Width = 55
            Height = 15
            Caption = 'Kunden ID'
          end
          object Label51: TLabel
            Left = 12
            Top = 85
            Width = 55
            Height = 15
            Caption = 'Kontakt ID'
          end
          object Label61: TLabel
            Left = 12
            Top = 120
            Width = 50
            Height = 15
            Caption = 'Web User'
          end
          object Label62: TLabel
            Left = 12
            Top = 149
            Width = 74
            Height = 15
            Caption = 'Web Passwort'
          end
          object SendITGLSKundenNrEdit: TEdit
            Left = 136
            Top = 24
            Width = 121
            Height = 23
            TabOrder = 0
            Text = 'SendITGLSKundenNrEdit'
          end
          object SendITGLSCustomerIDEdit: TEdit
            Left = 136
            Top = 53
            Width = 121
            Height = 23
            TabOrder = 1
            Text = 'SendITGLSCustomerIDEdit'
          end
          object SendITGLSContactIDEdit: TEdit
            Left = 136
            Top = 82
            Width = 121
            Height = 23
            TabOrder = 2
            Text = 'SendITGLSContactIDEdit'
          end
          object SendITGLSWenUserEdit: TEdit
            Left = 136
            Top = 117
            Width = 225
            Height = 23
            TabOrder = 3
            Text = 'SendITGLSWenUserEdit'
          end
          object SendITGLSWebPwEdit: TEdit
            Left = 136
            Top = 146
            Width = 225
            Height = 23
            TabOrder = 4
            Text = 'SendITGLSWebPwEdit'
          end
        end
        object SendITUPSTabSheet: TTabSheet
          Caption = 'SendITUPSTabSheet'
          ImageIndex = 3
          object Label55: TLabel
            Left = 20
            Top = 35
            Width = 40
            Height = 15
            Caption = 'UPS Nr.'
          end
          object Label56: TLabel
            Left = 20
            Top = 64
            Width = 40
            Height = 15
            Caption = 'API Key'
          end
          object Label57: TLabel
            Left = 20
            Top = 93
            Width = 53
            Height = 15
            Caption = 'Api Secret'
          end
          object Label58: TLabel
            Left = 511
            Top = 51
            Width = 24
            Height = 15
            Caption = 'Start'
          end
          object Bevel10: TBevel
            Left = 505
            Top = 48
            Width = 16
            Height = 50
            Shape = bsLeftLine
          end
          object Label59: TLabel
            Left = 511
            Top = 80
            Width = 26
            Height = 15
            Caption = 'Ende'
          end
          object Label60: TLabel
            Left = 648
            Top = 80
            Width = 30
            Height = 15
            Caption = 'Stand'
          end
          object Label53: TLabel
            Left = 19
            Top = 134
            Width = 54
            Height = 15
            Alignment = taRightJustify
            Caption = 'EDI-Ticket'
          end
          object SendITUPSNrEdit: TEdit
            Left = 128
            Top = 32
            Width = 121
            Height = 23
            TabOrder = 0
            Text = 'SendITUPSNrEdit'
          end
          object SendITUPSApiKeyEdit: TEdit
            Left = 128
            Top = 61
            Width = 337
            Height = 23
            TabOrder = 1
            Text = 'SendITUPSApiKeyEdit'
          end
          object SendITUPSApiSecretEdit: TEdit
            Left = 128
            Top = 90
            Width = 337
            Height = 23
            TabOrder = 2
            Text = 'SendITUPSApiSecretEdit'
          end
          object SendITUPSStartEdit: TEdit
            Left = 544
            Top = 48
            Width = 90
            Height = 23
            TabOrder = 3
            Text = 'SendITUPSStartEdit'
          end
          object SendITUPSEndeEdit: TEdit
            Left = 544
            Top = 77
            Width = 90
            Height = 23
            TabOrder = 4
            Text = 'SendITUPSEndeEdit'
          end
          object SendITUPSStandEdit: TEdit
            Left = 680
            Top = 77
            Width = 90
            Height = 23
            TabOrder = 5
            Text = 'SendITUPSStandEdit'
          end
          object SendITUPSTicketComboBox: TComboBoxPro
            Left = 128
            Top = 131
            Width = 257
            Height = 22
            Style = csOwnerDrawFixed
            TabOrder = 6
          end
          object SemdITUPSGoGreenCheckBox: TCheckBox
            Left = 300
            Top = 38
            Width = 97
            Height = 17
            Caption = 'GoGreen'
            TabOrder = 7
          end
        end
      end
      object SendITApplyButton: TButton
        Left = 870
        Top = 40
        Width = 95
        Height = 25
        Anchors = [akRight, akBottom]
        Caption = #220'bernehmen'
        TabOrder = 5
        Visible = False
      end
      object SendITProductComboBox: TComboBoxPro
        Left = 512
        Top = 47
        Width = 265
        Height = 22
        Style = csOwnerDrawFixed
        TabOrder = 2
        OnChange = SendITChange
      end
      object SendITProductIntComboBox: TComboBoxPro
        Left = 512
        Top = 76
        Width = 265
        Height = 22
        Style = csOwnerDrawFixed
        TabOrder = 3
      end
      object SendITDiscardButton: TButton
        Left = 870
        Top = 70
        Width = 95
        Height = 25
        Anchors = [akTop, akRight]
        Caption = 'Verwerfen'
        TabOrder = 6
      end
    end
    object VCETabSheet: TTabSheet
      Caption = 'VCETabSheet'
      ImageIndex = 1
      DesignSize = (
        973
        343)
      object Label35: TLabel
        Left = 3
        Top = 50
        Width = 34
        Height = 15
        Caption = 'Kunde'
      end
      object Label54: TLabel
        Left = 8
        Top = 8
        Width = 127
        Height = 21
        Caption = 'VCE Konfiguration'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -16
        Font.Name = 'Segoe UI'
        Font.Style = []
        ParentFont = False
      end
      object Bevel9: TBevel
        Left = -2
        Top = 32
        Width = 967
        Height = 10
        Anchors = [akLeft, akTop, akRight]
        Shape = bsTopLine
      end
      object VCEClientComboBox: TComboBoxPro
        Left = 67
        Top = 47
        Width = 345
        Height = 22
        Style = csOwnerDrawFixed
        TabOrder = 0
      end
      object VCEApplyButton: TButton
        Left = 870
        Top = 41
        Width = 95
        Height = 25
        Anchors = [akTop, akRight]
        Caption = #220'bernehmen'
        TabOrder = 1
        OnClick = VCEApplyButtonClick
      end
      object VCEProductGroupBox: TGroupBox
        Left = 3
        Top = 96
        Width = 962
        Height = 145
        Anchors = [akLeft, akTop, akRight]
        Caption = 'Produkt'
        TabOrder = 3
        object Label36: TLabel
          Left = 10
          Top = 31
          Width = 33
          Height = 15
          Caption = 'Inland'
        end
        object Label70: TLabel
          Left = 10
          Top = 71
          Width = 43
          Height = 15
          Caption = 'Ausland'
        end
        object Label73: TLabel
          Left = 10
          Top = 111
          Width = 41
          Height = 15
          Caption = 'Retoure'
        end
        object VCEProductNatEdit: TEdit
          Left = 64
          Top = 28
          Width = 169
          Height = 23
          TabOrder = 0
          Text = 'VCEProductNatEdit'
          OnChange = VCEProductNatEditChange
        end
        object VCEProductIntEdit: TEdit
          Left = 64
          Top = 68
          Width = 169
          Height = 23
          TabOrder = 4
          Text = 'VCEProductIntEdit'
          OnChange = VCEProductIntEditChange
        end
        object VCEProdNat1ComboBox: TComboBoxPro
          Left = 252
          Top = 28
          Width = 199
          Height = 22
          Style = csOwnerDrawFixed
          TabOrder = 1
          OnChange = VCEProdComboBoxChange
          OnDropDown = VCEProdComboBoxDropDown
        end
        object VCEProdNat2ComboBox: TComboBoxPro
          Left = 471
          Top = 28
          Width = 199
          Height = 22
          Style = csOwnerDrawFixed
          TabOrder = 2
          OnChange = VCEProdComboBoxChange
          OnDropDown = VCEProdComboBoxDropDown
        end
        object VCEProdNat3ComboBox: TComboBoxPro
          Left = 692
          Top = 28
          Width = 199
          Height = 22
          Style = csOwnerDrawFixed
          TabOrder = 3
          OnChange = VCEProdComboBoxChange
          OnDropDown = VCEProdComboBoxDropDown
        end
        object VCEProdInt1ComboBox: TComboBoxPro
          Left = 252
          Top = 68
          Width = 199
          Height = 22
          Style = csOwnerDrawFixed
          TabOrder = 5
          OnChange = VCEProdComboBoxChange
          OnDropDown = VCEProdComboBoxDropDown
        end
        object VCEProdInt2ComboBox: TComboBoxPro
          Left = 471
          Top = 68
          Width = 199
          Height = 22
          Style = csOwnerDrawFixed
          TabOrder = 6
          OnChange = VCEProdComboBoxChange
          OnDropDown = VCEProdComboBoxDropDown
        end
        object VCEProdInt3ComboBox: TComboBoxPro
          Left = 692
          Top = 68
          Width = 199
          Height = 22
          Style = csOwnerDrawFixed
          TabOrder = 7
          OnChange = VCEProdComboBoxChange
          OnDropDown = VCEProdComboBoxDropDown
        end
        object VCEProductRetEdit: TEdit
          Left = 64
          Top = 108
          Width = 169
          Height = 23
          TabOrder = 8
          Text = 'VCEProductRetEdit'
          OnChange = VCEProductRetEditChange
        end
        object VCEProdRet1ComboBox: TComboBoxPro
          Left = 252
          Top = 108
          Width = 199
          Height = 22
          Style = csOwnerDrawFixed
          TabOrder = 9
          OnChange = VCEProdComboBoxChange
          OnDropDown = VCEProdComboBoxDropDown
        end
        object VCEProdRet2ComboBox: TComboBoxPro
          Left = 471
          Top = 108
          Width = 199
          Height = 22
          Style = csOwnerDrawFixed
          TabOrder = 10
          OnChange = VCEProdComboBoxChange
          OnDropDown = VCEProdComboBoxDropDown
        end
        object VCEProdRet3ComboBox: TComboBoxPro
          Left = 692
          Top = 108
          Width = 199
          Height = 22
          Style = csOwnerDrawFixed
          TabOrder = 11
          OnChange = VCEProdComboBoxChange
          OnDropDown = VCEProdComboBoxDropDown
        end
      end
      object VCEDiscardButton: TButton
        Left = 870
        Top = 71
        Width = 95
        Height = 25
        Anchors = [akTop, akRight]
        Caption = 'Verwerfen'
        TabOrder = 2
        OnClick = VCEDiscardButtonClick
      end
    end
    object SevenTabSheet: TTabSheet
      Caption = 'SevenTabSheet'
      ImageIndex = 2
      DesignSize = (
        973
        343)
      object Label85: TLabel
        Left = 8
        Top = 50
        Width = 29
        Height = 15
        Caption = 'Lager'
      end
      object Label86: TLabel
        Left = 8
        Top = 8
        Width = 228
        Height = 21
        Caption = 'Seven Senders API Konfiguration'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -16
        Font.Name = 'Segoe UI'
        Font.Style = []
        ParentFont = False
      end
      object Bevel11: TBevel
        Left = 3
        Top = 32
        Width = 967
        Height = 10
        Anchors = [akLeft, akTop, akRight]
        Shape = bsTopLine
      end
      object SevenApplyButton: TButton
        Left = 870
        Top = 41
        Width = 95
        Height = 25
        Anchors = [akTop, akRight]
        Caption = #220'bernehmen'
        TabOrder = 1
        OnClick = SevenApplyButtonClick
      end
      object SevenDiscardButton: TButton
        Left = 870
        Top = 71
        Width = 95
        Height = 25
        Anchors = [akTop, akRight]
        Caption = 'Verwerfen'
        TabOrder = 2
        OnClick = SevenDiscardButtonClick
      end
      object GroupBox2: TGroupBox
        Left = 4
        Top = 97
        Width = 964
        Height = 114
        Anchors = [akLeft, akTop, akRight]
        Caption = 'Web-Servcie'
        TabOrder = 3
        DesignSize = (
          964
          114)
        object Label87: TLabel
          Left = 20
          Top = 68
          Width = 40
          Height = 15
          Caption = 'API Key'
        end
        object Label88: TLabel
          Left = 20
          Top = 20
          Width = 21
          Height = 15
          Caption = 'URL'
        end
        object SevenApiKeyEdit: TEdit
          Left = 20
          Top = 84
          Width = 925
          Height = 23
          Anchors = [akLeft, akTop, akRight]
          TabOrder = 1
          Text = 'SevenApiKeyEdit'
          OnChange = SevenChange
        end
        object SevenApiURLEdit: TEdit
          Left = 20
          Top = 36
          Width = 925
          Height = 23
          Anchors = [akLeft, akTop, akRight]
          TabOrder = 0
          Text = 'SevenApiURLEdit'
          OnChange = SevenChange
        end
      end
      object GroupBox3: TGroupBox
        Left = 6
        Top = 217
        Width = 964
        Height = 114
        Anchors = [akLeft, akTop, akRight]
        Caption = 'Produkt'
        TabOrder = 4
        DesignSize = (
          964
          114)
        object Label89: TLabel
          Left = 449
          Top = 21
          Width = 110
          Height = 15
          Caption = 'Produkt Key national'
        end
        object Label90: TLabel
          Left = 18
          Top = 20
          Width = 109
          Height = 15
          Caption = 'Nationaler Versender'
        end
        object Label91: TLabel
          Left = 229
          Top = 20
          Width = 142
          Height = 15
          Caption = 'Versender Produkt national'
        end
        object Label93: TLabel
          Left = 229
          Top = 68
          Width = 166
          Height = 15
          Caption = 'Versender Produkt international'
        end
        object Label94: TLabel
          Left = 449
          Top = 65
          Width = 134
          Height = 15
          Caption = 'Produkt Key international'
        end
        object Label95: TLabel
          Left = 18
          Top = 68
          Width = 131
          Height = 15
          Caption = 'Internationaler Versender'
        end
        object SevenCarrierEdit: TEdit
          Left = 18
          Top = 36
          Width = 177
          Height = 23
          TabOrder = 0
          Text = 'SevenCarrierEdit'
          OnChange = SevenChange
        end
        object SevenProductEdit: TEdit
          Left = 229
          Top = 36
          Width = 180
          Height = 23
          TabOrder = 1
          Text = 'SevenProductEdit'
          OnChange = SevenChange
        end
        object SevenProductKeyEdit: TEdit
          Left = 449
          Top = 36
          Width = 494
          Height = 23
          Anchors = [akLeft, akTop, akRight]
          TabOrder = 2
          Text = 'SevenProductKeyEdit'
          OnChange = SevenChange
        end
        object SevenIntProductEdit: TEdit
          Left = 229
          Top = 84
          Width = 180
          Height = 23
          TabOrder = 4
          Text = 'SevenIntProductEdit'
          OnChange = SevenChange
        end
        object SevenIntProductKeyEdit: TEdit
          Left = 449
          Top = 84
          Width = 494
          Height = 23
          Anchors = [akLeft, akTop, akRight]
          TabOrder = 5
          Text = 'SevenIntProductKeyEdit'
          OnChange = SevenChange
        end
        object SevenIntCarrierEdit: TEdit
          Left = 18
          Top = 84
          Width = 177
          Height = 23
          TabOrder = 3
          Text = 'SevenIntCarrierEdit'
          OnChange = SevenChange
        end
      end
      object SevenLocationEdit: TEdit
        Left = 64
        Top = 48
        Width = 189
        Height = 23
        TabOrder = 0
        Text = 'SevenLocationEdit'
        OnChange = SevenChange
      end
    end
  end
  object BattomPanel: TPanel
    Left = 0
    Top = 768
    Width = 981
    Height = 41
    Align = alBottom
    BevelOuter = bvNone
    TabOrder = 3
    DesignSize = (
      981
      41)
    object CloseButton: TButton
      Left = 865
      Top = 6
      Width = 104
      Height = 25
      Anchors = [akTop, akRight]
      Cancel = True
      Caption = 'Schlie'#223'en'
      ModalResult = 8
      TabOrder = 0
    end
  end
  object GroupBox1: TGroupBox
    Left = 0
    Top = 247
    Width = 981
    Height = 150
    Align = alBottom
    Caption = 'Gateway Konfiguration'
    TabOrder = 4
    DesignSize = (
      981
      150)
    object Label66: TLabel
      Left = 12
      Top = 80
      Width = 77
      Height = 15
      Caption = 'Untermandant'
    end
    object Label67: TLabel
      Left = 12
      Top = 108
      Width = 49
      Height = 15
      Caption = 'Verk'#228'ufer'
    end
    object Label65: TLabel
      Left = 12
      Top = 52
      Width = 48
      Height = 15
      Caption = 'Mandant'
    end
    object Label72: TLabel
      Left = 12
      Top = 24
      Width = 29
      Height = 15
      Caption = 'Lager'
    end
    object GatewayApplyButton: TButton
      Left = 874
      Top = 91
      Width = 95
      Height = 25
      Anchors = [akRight, akBottom]
      Caption = #220'bernehmen'
      TabOrder = 5
      OnClick = GatewayApplyButtonClick
    end
    object GatewayActiveCheckBox: TCheckBox
      Left = 876
      Top = 17
      Width = 97
      Height = 17
      Anchors = [akTop, akRight]
      Caption = 'Aktiv'
      TabOrder = 4
    end
    object MandantComboBox: TComboBoxPro
      Left = 115
      Top = 49
      Width = 273
      Height = 22
      Style = csOwnerDrawFixed
      TabOrder = 1
      OnChange = MandantComboBoxChange
    end
    object SubMandComboBox: TComboBoxPro
      Left = 115
      Top = 77
      Width = 273
      Height = 22
      Style = csOwnerDrawFixed
      TabOrder = 2
      OnChange = SubMandComboBoxChange
    end
    object TraderComboBox: TComboBoxPro
      Left = 116
      Top = 105
      Width = 273
      Height = 22
      Style = csOwnerDrawFixed
      TabOrder = 3
      OnChange = GatewayDataChange
    end
    object LagerComboBox: TComboBoxPro
      Left = 115
      Top = 21
      Width = 273
      Height = 22
      Style = csOwnerDrawFixed
      TabOrder = 0
      OnChange = MandantComboBoxChange
    end
    object GatewayDiscardButton: TButton
      Left = 874
      Top = 119
      Width = 95
      Height = 25
      Anchors = [akRight, akBottom]
      Caption = 'Verwerfen'
      TabOrder = 6
      OnClick = GatewayDiscardButtonClick
    end
    object ConfigPageControl: TPageControl
      Left = 408
      Top = 11
      Width = 452
      Height = 133
      ActivePage = ConfigTabSheet
      TabOrder = 7
      object ConfigTabSheet: TTabSheet
        Caption = 'Einstellungen'
        object Label68: TLabel
          Left = 10
          Top = 14
          Width = 50
          Height = 15
          Caption = 'G'#252'ltig f'#252'r'
        end
        object Label71: TLabel
          Left = 10
          Top = 75
          Width = 42
          Height = 15
          Caption = 'Settings'
        end
        object Label69: TLabel
          Left = 284
          Top = 43
          Width = 48
          Height = 15
          Alignment = taRightJustify
          Caption = 'Incoterm'
        end
        object Label92: TLabel
          Left = 291
          Top = 14
          Width = 41
          Height = 15
          Alignment = taRightJustify
          Caption = 'Zoll-Nr.'
        end
        object CountryEdit: TEdit
          Left = 71
          Top = 11
          Width = 195
          Height = 23
          MaxLength = 128
          TabOrder = 0
          Text = 'CountryEdit'
          OnChange = GatewayDataChange
        end
        object SettingEdit: TEdit
          Left = 71
          Top = 72
          Width = 354
          Height = 23
          TabOrder = 2
          Text = 'SettingEdit'
          OnChange = GatewayDataChange
        end
        object IncotermEdit: TEdit
          Left = 340
          Top = 40
          Width = 85
          Height = 23
          MaxLength = 32
          TabOrder = 3
          Text = 'IncotermEdit'
          OnChange = GatewayDataChange
        end
        object CustomNoEdit: TEdit
          Left = 340
          Top = 11
          Width = 85
          Height = 23
          MaxLength = 32
          TabOrder = 1
          Text = 'CustomNoEdit'
          OnChange = GatewayDataChange
        end
      end
      object AbsTabSheet: TTabSheet
        Caption = 'Absender'
        ImageIndex = 1
        DesignSize = (
          444
          103)
        object Label74: TLabel
          Left = 8
          Top = 9
          Width = 32
          Height = 15
          Caption = 'Name'
        end
        object Label75: TLabel
          Left = 8
          Top = 41
          Width = 36
          Height = 15
          Caption = 'Strasse'
        end
        object Label77: TLabel
          Left = 8
          Top = 73
          Width = 20
          Height = 15
          Caption = 'PLZ'
        end
        object Label78: TLabel
          Left = 320
          Top = 42
          Width = 41
          Height = 15
          Anchors = [akTop, akRight]
          Caption = 'Hausnr.'
        end
        object Label79: TLabel
          Left = 136
          Top = 73
          Width = 17
          Height = 15
          Alignment = taRightJustify
          Caption = 'Ort'
        end
        object Label81: TLabel
          Left = 320
          Top = 73
          Width = 26
          Height = 15
          Anchors = [akTop, akRight]
          Caption = 'Land'
        end
        object AbsNameEdit: TEdit
          Left = 62
          Top = 6
          Width = 179
          Height = 23
          Anchors = [akLeft, akTop, akRight]
          TabOrder = 0
          Text = 'AbsNameEdit'
          OnChange = GatewayDataChange
        end
        object AbsStreetEdit: TEdit
          Left = 62
          Top = 38
          Width = 243
          Height = 23
          Anchors = [akLeft, akTop, akRight]
          TabOrder = 2
          Text = 'AbsStreetEdit'
          OnChange = GatewayDataChange
        end
        object AbsPLZ: TEdit
          Left = 62
          Top = 70
          Width = 51
          Height = 23
          TabOrder = 4
          Text = 'VCEAbsNameEdit'
          OnChange = GatewayDataChange
        end
        object AbsHouseNoEdit: TEdit
          Left = 368
          Top = 39
          Width = 63
          Height = 23
          Anchors = [akTop, akRight]
          TabOrder = 3
          Text = 'VCEAbsNameEdit'
          OnChange = GatewayDataChange
        end
        object AbsOrt: TEdit
          Left = 166
          Top = 70
          Width = 139
          Height = 23
          Anchors = [akLeft, akTop, akRight]
          TabOrder = 5
          Text = 'VCEAbsNameEdit'
          OnChange = GatewayDataChange
        end
        object AbsLand: TEdit
          Left = 368
          Top = 70
          Width = 65
          Height = 23
          Anchors = [akTop, akRight]
          TabOrder = 6
          Text = 'VCEAbsNameEdit'
          OnChange = GatewayDataChange
        end
        object AbsNameAddEdit: TEdit
          Left = 247
          Top = 6
          Width = 184
          Height = 23
          Anchors = [akTop, akRight]
          TabOrder = 1
          Text = 'AbsNameAddEdit'
          OnChange = GatewayDataChange
        end
      end
      object NotifyTabSheet: TTabSheet
        Caption = 'Benachrichtigungen'
        ImageIndex = 2
        object Label82: TLabel
          Left = 16
          Top = 59
          Width = 110
          Height = 15
          Caption = 'Default Mail-Adresse'
        end
        object Label83: TLabel
          Left = 296
          Top = 59
          Width = 94
          Height = 15
          Caption = 'Default Telefonnr.'
        end
        object Label84: TLabel
          Left = 296
          Top = 3
          Width = 43
          Height = 15
          Caption = 'Avis-Art'
        end
        object NotifyByMailCheckBox: TCheckBox
          Left = 16
          Top = 8
          Width = 201
          Height = 17
          AllowGrayed = True
          Caption = 'Benachrrichtigung per Mail'
          State = cbGrayed
          TabOrder = 0
        end
        object NotifyByPhoneCheckBox: TCheckBox
          Left = 16
          Top = 27
          Width = 193
          Height = 17
          AllowGrayed = True
          Caption = 'Benachrichtigung per Telefon'
          State = cbGrayed
          TabOrder = 1
        end
        object DefaultMailEdit: TEdit
          Left = 16
          Top = 75
          Width = 241
          Height = 23
          MaxLength = 64
          TabOrder = 3
          Text = 'DefaultMailEdit'
          OnChange = GatewayDataChange
        end
        object DefaultPhoneEdit: TEdit
          Left = 296
          Top = 75
          Width = 137
          Height = 23
          MaxLength = 32
          TabOrder = 4
          Text = 'DefaultPhoneEdit'
          OnChange = GatewayDataChange
        end
        object AvisArtEdit: TEdit
          Left = 296
          Top = 19
          Width = 137
          Height = 23
          MaxLength = 16
          TabOrder = 2
          Text = 'AvisArtEdit'
          OnChange = GatewayDataChange
        end
      end
    end
  end
  object SpedGatewayQuery: TOraQuery
    Options.DynamicReadThreshold = 0
    Left = 448
    Top = 216
  end
  object SpedGatewayDataSource: TOraDataSource
    DataSet = SpedGatewayQuery
    OnDataChange = SpedGatewayDataSourceDataChange
    Left = 536
    Top = 144
  end
  object SendITADOConnection: TADOConnection
    Left = 744
    Top = 8
  end
  object SpedGatewayMenu: TPopupMenu
    OnPopup = SpedGatewayMenuPopup
    Left = 168
    Top = 88
    object CreateSpedGatewayMenuItem: TMenuItem
      Caption = 'Anlegen'
      OnClick = CreateSpedGatewayMenuItemClick
    end
    object CopySpedGatewayMenuItem: TMenuItem
      Caption = 'Kopieren'
      OnClick = CopySpedGatewayMenuItemClick
    end
    object N1: TMenuItem
      Caption = '-'
    end
    object DelSpedGatewayMenuItem: TMenuItem
      Caption = 'L'#246'schen...'
      OnClick = DelSpedGatewayMenuItemClick
    end
  end
end
