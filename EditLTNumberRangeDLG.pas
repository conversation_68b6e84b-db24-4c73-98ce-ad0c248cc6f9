unit EditLTNumberRangeDLG;

interface

uses
  Windows, Messages, SysUtils, Variants, Classes, Graphics, Controls, Forms,
  Dialogs, StdCtrls, ExtCtrls, DB, ADODB, ComboBoxPro, Menus, ComCtrls;

type
  TEditLTNumberRangeForm = class(TForm)
    Label1: TLabel;
    LocationLabel: TLabel;
    Bevel1: TBevel;
    Label4: TLabel;
    LTTypeLabel: TLabel;
    AbortButton: TButton;
    OkButton: TButton;
    ADOQuery1: TADOQuery;
    PopupMenu1: TPopupMenu;
    LagerComboBox: TComboBoxPro;
    Bevel3: TBevel;
    Bevel4: TBevel;
    Label2: TLabel;
    DescEdit: TEdit;
    Label10: TLabel;
    Label11: TLabel;
    PageControl1: TPageControl;
    LTNumberTabSheet: TTabSheet;
    SerialTabSheet: TTabSheet;
    Label3: TLabel;
    BeginEdit: TEdit;
    Label5: TLabel;
    EndEdit: TEdit;
    Label7: TLabel;
    LenEdit: TEdit;
    CycleCheckBox: TCheckBox;
    Bevel5: TBevel;
    Label8: TLabel;
    PrefixEdit: TEdit;
    Label6: TLabel;
    SequenceEdit: TEdit;
    Label9: TLabel;
    PostfixEdit: TEdit;
    SerialPostfixEdit: TEdit;
    Label12: TLabel;
    SerialSequenceEdit: TEdit;
    Label13: TLabel;
    Bevel6: TBevel;
    SerialPrefixEdit: TEdit;
    Label14: TLabel;
    SerialLenEdit: TEdit;
    Label15: TLabel;
    SerialEndEdit: TEdit;
    Label16: TLabel;
    SerialBeginEdit: TEdit;
    Label17: TLabel;
    LTIDEdit: TEdit;
    Label18: TLabel;
    procedure IntEditKeyPress(Sender: TObject; var Key: Char);
    procedure FormCreate(Sender: TObject);
    procedure FormShow(Sender: TObject);
    procedure LagerComboBoxChange(Sender: TObject);
    procedure EditChange(Sender: TObject);
    procedure FormCloseQuery(Sender: TObject; var CanClose: Boolean);
  private
    fChangeFlag : Boolean;
    fLastLagerIndex : Integer;

    fRef        : Integer;
    fRefLoc     : Integer;
    fRefLager   : Integer;
    fRefLTType  : Integer;

    fLTID       : String;
  public
    property Ref       : Integer read fRef;
    property RefLoc    : Integer read fRefLoc    write fRefLoc;
    property RefLager  : Integer read fRefLager  write fRefLager;
    property RefLTType : Integer read fRefLTType write fRefLTType;
  end;

implementation

{$R *.dfm}

uses
  VCLUtilitys, DatenModul, FrontendUtils, LVSLadungstraeger, SprachModul, ResourceText;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TEditLTNumberRangeForm.EditChange(Sender: TObject);
begin
  if (Sender is TEdit) then
    (Sender as TEdit).Color := clWindow;

  fChangeFlag := True;
end;

procedure TEditLTNumberRangeForm.FormCloseQuery(Sender: TObject; var CanClose: Boolean);

  function GetNumber (NrEdit : TEdit; var value : Integer) : Boolean;
  begin
    if (Length (NrEdit.Text) = 0) then begin
      value := -1;
      Result := True;
    end else if not TryStrToInt (NrEdit.Text, value) then begin
      value := -1;
      Result := False;
    end else
      Result := True;
  end;

var
  res,
  nrend,
  nrseq,
  nrlen,
  nrbegin,
  serend,
  serseq,
  serlen,
  serbegin : Integer;
begin
  if (ModalResult <> mrOk) then
    CanClose := True
  else begin
    if (fChangeFlag) then begin
      CanClose := False;

      if not GetNumber (BeginEdit, nrbegin) then begin
        BeginEdit.Color := clRed;
        BeginEdit.SetFocus
      end else if not GetNumber (EndEdit, nrend) then begin
        EndEdit.SetFocus
      end else if not GetNumber (LenEdit, nrlen) then begin
        LenEdit.SetFocus
      end else if SequenceEdit.Enabled and not GetNumber (SequenceEdit, nrseq) then begin
        SequenceEdit.Color := clRed;
        SequenceEdit.SetFocus
      end else if (SequenceEdit.Enabled and (Length (SequenceEdit.Text) > 0) or (Length (PrefixEdit.Text) > 0)) and (nrseq = -1) then begin
        SequenceEdit.Color := clRed;
        SequenceEdit.SetFocus
      end else if SerialTabSheet.TabVisible and not GetNumber (SerialBeginEdit, serbegin) then begin
        SerialBeginEdit.Color := clRed;
        SerialBeginEdit.SetFocus
      end else if SerialTabSheet.TabVisible and not GetNumber (SerialEndEdit, serend) then begin
        SerialEndEdit.SetFocus;
      end else if SerialTabSheet.TabVisible and not GetNumber (SerialLenEdit, serlen) then begin
        SerialLenEdit.SetFocus;
      end else if SerialTabSheet.TabVisible and not GetNumber (SerialSequenceEdit, serseq) then begin
        SerialSequenceEdit.SetFocus;
      end else if SerialTabSheet.TabVisible and ((Length (SerialSequenceEdit.Text) > 0) or (Length (SerialPrefixEdit.Text) > 0)) and (serseq = -1) then begin
        SerialSequenceEdit.SetFocus
      end else begin
        if LTIDEdit.Visible then
          res := SetLTNummerRange (fRefLoc, GetComboBoxRef (LagerComboBox), fRefLTType, LTIDEdit.Text, DescEdit.Text, nrbegin, nrend, nrlen, GetCheckboxStat (CycleCheckBox), nrseq, PrefixEdit.Text, PostfixEdit.Text)
        else
          res := SetLTNummerRange (fRefLoc, GetComboBoxRef (LagerComboBox), fRefLTType, DescEdit.Text, nrbegin, nrend, nrlen, GetCheckboxStat (CycleCheckBox), nrseq, PrefixEdit.Text, PostfixEdit.Text);

        if (res = 0) and SerialTabSheet.TabVisible then begin
          res := SetLTSerialRange (fRefLoc, GetComboBoxRef (LagerComboBox), fRefLTType, serbegin, serend, serlen, serseq, SerialPrefixEdit.Text, SerialPostfixEdit.Text);
        end;

        if (res = 0) then
          CanClose := True
        else
          MessageDLG (FormatMessageText (1223, [LVSDatenModul.LastLVSErrorText]), mtError, [mbOk], 0);
      end;
    end;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TEditLTNumberRangeForm.FormCreate(Sender: TObject);
begin
  fLastLagerIndex := -1;

  fRef       := -1;
  fRefLoc    := -1;
  fRefLager  := -1;
  fRefLTType := -1;

  fLTID      := '';

  LocationLabel.Caption := '';
  LTTypeLabel.Caption := '';

  LTIDEdit.Text := '';
  DescEdit.Text := '';

  BeginEdit.Text := '';
  EndEdit.Text := '';
  LenEdit.Text := '';
  SequenceEdit.Text := '';
  PrefixEdit.Text := '';
  PostfixEdit.Text := '';

  LVSSprachModul.InitForm (Self);

  {$ifdef TranslateHelper}
    LVSSprachModul.SetNoTranslate (Self, LagerComboBox);
    LVSSprachModul.SetNoTranslate (Self, Label11);
  {$endif}
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TEditLTNumberRangeForm.FormShow(Sender: TObject);
begin
  ADOQuery1.SQL.Clear;

  if (fRefLoc > 0) then begin
    ADOQuery1.SQL.Add ('select loc.NAME, lt.NAME, lt.LT_ID from V_LT_TYPEN lt, V_LOCATION loc where loc.REF='+IntToStr (fRefLoc)+' and lt.REF='+IntToStr (fRefLTType));

    ADOQuery1.Open;

    fLTID := ADOQuery1.Fields [2].AsString;

    LocationLabel.Caption := ADOQuery1.Fields [0].AsString;
    LTTypeLabel.Caption   := ADOQuery1.Fields [1].AsString;

    ADOQuery1.Close;

    LoadLagerCombobox (LagerComboBox, fRefLoc);
    LagerComboBox.Items.Insert (0, '');

    LagerComboBox.ItemIndex := 0;
  end else begin
    LocationLabel.Caption := '';
    LagerComboBox.Enabled := False;

    ADOQuery1.SQL.Add ('select lt.NAME, lt.LT_ID from V_LT_TYPEN lt where lt.REF='+IntToStr (fRefLTType));

    ADOQuery1.Open;

    fLTID := ADOQuery1.Fields [1].AsString;

    LTTypeLabel.Caption := ADOQuery1.Fields [0].AsString;

    ADOQuery1.Close;
  end;

  fChangeFlag := False;

  Label18.Visible := LTIDEdit.Visible;

  LagerComboBoxChange (Sender);
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TEditLTNumberRangeForm.IntEditKeyPress(Sender: TObject; var Key: Char);
begin
  if not (Key in [#8,^C,^V,'0'..'9']) then begin
    Key := #0;
    Beep;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TEditLTNumberRangeForm.LagerComboBoxChange(Sender: TObject);
begin
  if (fChangeFlag) then begin
    if (MessageDLG (FormatMessageText (1107, []), mtConfirmation, [mbYes, mbNo, mbCancel], 0) = mrYes) then
      fChangeFlag := False
    else
      LagerComboBox.ItemIndex := fLastLagerIndex;
  end;

  if not (fChangeFlag) then begin
    fLastLagerIndex := LagerComboBox.ItemIndex;

    ADOQuery1.SQL.Clear;
    ADOQuery1.SQL.Add ('select * from V_LT_NUMBER_RANGE where STATUS=''AKT''');

    if (fRefLTType <= 0) then
      ADOQuery1.SQL.Add ('and REF_LT_TYP is null')
    else begin
      ADOQuery1.SQL.Add ('and REF_LT_TYP=:ref_lt');
      ADOQuery1.Parameters.ParamByName('ref_lt').Value := fRefLTType;
    end;

    if (GetComboBoxRef (LagerComboBox) <> -1) then
      ADOQuery1.SQL.Add ('and (REF_LAGER='+IntToStr (fRefLager)+')')
    else if (RefLoc > 0) then
      ADOQuery1.SQL.Add ('and (REF_LAGER is null and REF_LOCATION='+IntToStr (RefLoc)+')')
    else
      ADOQuery1.SQL.Add ('and (REF_LAGER is null and REF_LOCATION is null)');

    ADOQuery1.Open;

    LTIDEdit.Visible          := Assigned (ADOQuery1.FindField ('LT_ID'));
    SerialTabSheet.TabVisible := Assigned (ADOQuery1.FindField ('SERIAL_BEGIN'));

    if (ADOQuery1.RecordCount = 0) then begin
      fRef := -1;

      if (GetComboBoxRef (LagerComboBox) <> -1) then
        Label11.Caption := GetResourceText (1305)
      else if (RefLoc > 0) then
        Label11.Caption := GetResourceText (1306)
      else
        Label11.Caption := GetResourceText (1337);

      LTIDEdit.Text := '';
      DescEdit.Text     := '';

      BeginEdit.Text    := '';
      EndEdit.Text      := '';
      LenEdit.Text      := '';
      SequenceEdit.Text := '';
      PrefixEdit.Text   := '';
      PostfixEdit.Text  := '';

      SerialBeginEdit.Text    := '';
      SerialEndEdit.Text      := '';
      SerialLenEdit.Text      := '';
      SerialSequenceEdit.Text := '';
      SerialPrefixEdit.Text   := '';
      SerialPostfixEdit.Text  := '';
    end else begin
      fRef := ADOQuery1.FieldByName('REF').AsInteger;

      Label11.Caption := '';

      if (LTIDEdit.Visible) then
        LTIDEdit.Text := ADOQuery1.FieldByName('LT_ID').AsString;

      DescEdit.Text     := ADOQuery1.FieldByName('DESCRIPTION').AsString;

      BeginEdit.Text    := ADOQuery1.FieldByName('RANGE_BEGIN').AsString;
      EndEdit.Text      := ADOQuery1.FieldByName('RANGE_END').AsString;
      LenEdit.Text      := ADOQuery1.FieldByName('FIXED_LEN').AsString;
      SequenceEdit.Text := ADOQuery1.FieldByName('LAST_NUMBER').AsString;
      PrefixEdit.Text   := ADOQuery1.FieldByName('PREFIX').AsString;
      PostfixEdit.Text  := ADOQuery1.FieldByName('POSTFIX').AsString;

      CycleCheckBox.Checked := (ADOQuery1.FieldByName('CYCLE').AsString = '1');

      if SerialTabSheet.TabVisible then begin
        SerialBeginEdit.Text    := ADOQuery1.FieldByName('SERIAL_BEGIN').AsString;
        SerialEndEdit.Text      := ADOQuery1.FieldByName('SERIAL_END').AsString;
        SerialLenEdit.Text      := ADOQuery1.FieldByName('SERIAL_LEN').AsString;
        SerialSequenceEdit.Text := ADOQuery1.FieldByName('LAST_SERIAL').AsString;
        SerialPrefixEdit.Text   := ADOQuery1.FieldByName('SERIAL_PREFIX').AsString;
        SerialPostfixEdit.Text  := ADOQuery1.FieldByName('SERIAL_POSTFIX').AsString;
      end else begin
        SerialBeginEdit.Text    := '';
        SerialEndEdit.Text      := '';
        SerialLenEdit.Text      := '';
        SerialSequenceEdit.Text := '';
        SerialPrefixEdit.Text   := '';
        SerialPostfixEdit.Text  := '';
      end;
    end;

    ADOQuery1.Close;

    fChangeFlag := False;
  end;
end;

end.
