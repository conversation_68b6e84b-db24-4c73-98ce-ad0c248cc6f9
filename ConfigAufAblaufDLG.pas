﻿unit ConfigAufAblaufDLG;

interface

uses
  Windows, Messages, SysUtils, Variants, Classes, Graphics, Controls, Forms,
  Dialogs, StdCtrls, Grids, DBGrids, SMDBGrid, DBGridPro, DB, ADODB, ExtCtrls,
  ComCtrls, ComboBoxPro, IntegerUpDown, Vcl.Menus;

type
  TConfigAufAblaufForm = class(TForm)
    CloseButton: TButton;
    AufAblaufQuery: TADOQuery;
    AufAblaufDataSource: TDataSource;
    DialogPageControl: TPageControl;
    ConfigTabSheet: TTabSheet;
    PlanTabSheet: TTabSheet;
    GroupBox1: TGroupBox;
    PlanApplyButton: TButton;
    PlanQuashButton: TButton;
    AufAblaufDBGrid: TDBGridPro;
    AufCfgQuery: TADOQuery;
    AufCfgDataSource: TDataSource;
    AufCfgDBGrid: TDBGridPro;
    ConfigDataGroupBox: TGroupBox;
    LTComboBox: TComboBoxPro;
    Label7: TLabel;
    Label15: TLabel;
    SpedPrefComboBox: TComboBoxPro;
    Label5: TLabel;
    SpedDefaultComboBox: TComboBoxPro;
    DruckartEdit: TEdit;
    Label6: TLabel;
    ConfigApplyButton: TButton;
    ConfigQuashButton: TButton;
    SpedRoleEdit: TEdit;
    Label8: TLabel;
    PlanPageControl: TPageControl;
    PlanPlanTabSheet: TTabSheet;
    PlanKommTabSheet: TTabSheet;
    PlanWATabSheet: TTabSheet;
    PlanPanel: TPanel;
    Bevel1: TBevel;
    Bevel5: TBevel;
    KommPlanBereichCheckBox: TCheckBox;
    PALKommPlanBereichCheckBox: TCheckBox;
    KommPlanZoneCheckBox: TCheckBox;
    PALKommPlanZoneCheckBox: TCheckBox;
    PlanFiFoCheckBox: TCheckBox;
    PlanFullPALCheckBox: TCheckBox;
    PlanTeilPALCheckBox: TCheckBox;
    Panel1: TPanel;
    Bevel4: TBevel;
    Label4: TLabel;
    KommPlanCombobox: TComboBox;
    KommPanel: TPanel;
    Bevel2: TBevel;
    Label1: TLabel;
    Label2: TLabel;
    KommPackComboBox: TComboBox;
    WAAblaufComboBox: TComboBox;
    PrintMDENVECheckBox: TCheckBox;
    PrintMDENVEInhaltCheckBox: TCheckBox;
    WAPanel: TPanel;
    Bevel3: TBevel;
    Label3: TLabel;
    WARelComboBox: TComboBoxPro;
    PrintPanel: TPanel;
    LSPrintCheckBox: TCheckBox;
    AutoLSPrintCheckBox: TCheckBox;
    NVEPrintCheckBox: TCheckBox;
    AutoNVEPrintCheckBox: TCheckBox;
    NVEInhaltPrintCheckBox: TCheckBox;
    AutoNVEInhaltPrintCheckBox: TCheckBox;
    Label9: TLabel;
    WAPackplatzComboBox: TComboBoxPro;
    Panel2: TPanel;
    LHMCheckBox: TCheckBox;
    AutoLHMCheckBox: TCheckBox;
    WAMasterCheckBox: TCheckBox;
    Label10: TLabel;
    WABufferComboBox: TComboBoxPro;
    Label12: TLabel;
    WACrossdockComboBox: TComboBoxPro;
    Label13: TLabel;
    WAUmpackComboBox: TComboBoxPro;
    NamePanel: TPanel;
    Label14: TLabel;
    NameEdit: TEdit;
    Label16: TLabel;
    DescEdit: TEdit;
    MDELHMCheckBox: TCheckBox;
    PlanKommVerdichtenCheckBox: TCheckBox;
    GlobalTabSheet: TTabSheet;
    BatchPlanCheckBox: TCheckBox;
    SetWALPCheckBox: TCheckBox;
    DefWALPCheckBox: TCheckBox;
    TAPanelPanel: TPanel;
    Bevel6: TBevel;
    Label20: TLabel;
    TAGroupComboBox: TComboBoxPro;
    Label17: TLabel;
    KommPlanGrpComboBox: TComboBox;
    NVECopyEdit: TEdit;
    Kopien: TLabel;
    NVECopyUpDown: TUpDown;
    Bevel7: TBevel;
    Bevel8: TBevel;
    Bevel9: TBevel;
    LSCopyEdit: TEdit;
    LSCopyUpDown: TUpDown;
    Label18: TLabel;
    Panel3: TPanel;
    Bevel10: TBevel;
    Label19: TLabel;
    KommArtComboBox: TComboBox;
    KonfGroupBox: TGroupBox;
    KonfAufCheckBox: TCheckBox;
    KonfBestCheckBox: TCheckBox;
    KommMHDCheckBox: TCheckBox;
    KommChargeCheckBox: TCheckBox;
    KommScanSKUCheckBox: TCheckBox;
    KommScanPlaceCheckBox: TCheckBox;
    PrioEdit: TEdit;
    Label22: TLabel;
    PrioUpDown: TIntegerUpDown;
    Label28: TLabel;
    PackTypeGroupComboBox: TComboBoxPro;
    KommSplitPackageCheckBox: TCheckBox;
    EmptyCheckBox: TCheckBox;
    AutoEmptyCheckBox: TCheckBox;
    OptGroupBox: TGroupBox;
    VorResCheckBox: TCheckBox;
    PrintGroupBox: TGroupBox;
    PrintInvoiceCheckBox: TCheckBox;
    PrintProformaCheckBox: TCheckBox;
    PrintCMRCheckBox: TCheckBox;
    PartDeliveryCheckBox: TCheckBox;
    PickShipCheckBox: TCheckBox;
    ShipLabelComboBox: TComboBox;
    OnePackageCheckBox: TCheckBox;
    DeliverNotifyCheckBox: TCheckBox;
    LSDMSCheckBox: TCheckBox;
    DeliveryNoteMailCheckBox: TCheckBox;
    Label29: TLabel;
    Label21: TLabel;
    BBDRemainingEdit: TEdit;
    BBDRemainingUpDown: TIntegerUpDown;
    Label24: TLabel;
    SpedRetComboBox: TComboBoxPro;
    RetPDFPathEdit: TEdit;
    Label25: TLabel;
    DetermineLTCheckBox: TCheckBox;
    SpedRetLandEdit: TEdit;
    Label23: TLabel;
    SubMandComboBox: TComboBoxPro;
    SubMandLabel: TLabel;
    Label11: TLabel;
    MandComboBox: TComboBoxPro;
    AufVersTabSheet: TTabSheet;
    AufVersDBGrid: TDBGridPro;
    GroupBox2: TGroupBox;
    AufVersQuery: TADOQuery;
    AufVersDataSource: TDataSource;
    RetSendNrCheckBox: TCheckBox;
    Label34: TLabel;
    AufVersDBGridPopupMenu: TPopupMenu;
    VersNewMenuItem: TMenuItem;
    VersCopyMenuItem: TMenuItem;
    N1: TMenuItem;
    VersDelMenuItem: TMenuItem;
    NVEinhaltLabelCheckBox: TCheckBox;
    PlanPrintTabSheet: TTabSheet;
    VersConfigPageControl: TPageControl;
    VersParamTabSheet: TTabSheet;
    Label68: TLabel;
    Label69: TLabel;
    Label33: TLabel;
    Label35: TLabel;
    Label36: TLabel;
    Label37: TLabel;
    Label41: TLabel;
    Label42: TLabel;
    Label44: TLabel;
    IncotermEdit: TEdit;
    VATEdit: TEdit;
    EORIEdit: TEdit;
    ZollNrEdit: TEdit;
    VersLandComboBox: TComboBoxPro;
    VersSpedComboBox: TComboBoxPro;
    DocGroupBox: TGroupBox;
    Label38: TLabel;
    Label39: TLabel;
    Label40: TLabel;
    DeliverNoteCheckBox: TCheckBox;
    DeliverNoteCopiesEdit: TEdit;
    DeliverNoteCopiesUpDown: TUpDown;
    ProformaCheckBox: TCheckBox;
    ProformaCopiesEdit: TEdit;
    ProformaCopiesUpDown: TUpDown;
    DeliverNoteFormComboBox: TComboBoxPro;
    ProformaFormComboBox: TComboBoxPro;
    CN23CheckBox: TCheckBox;
    CN23CopiesEdit: TEdit;
    CN23CopiesUpDown: TUpDown;
    CN23FormComboBox: TComboBoxPro;
    DutiesAccountEdit: TEdit;
    SenderAccountEdit: TEdit;
    ChargesAccountEdit: TEdit;
    VersFiskalTabSheet: TTabSheet;
    Label26: TLabel;
    Label31: TLabel;
    Label32: TLabel;
    GroupBox3: TGroupBox;
    Label74: TLabel;
    Label75: TLabel;
    Label77: TLabel;
    Label79: TLabel;
    Label78: TLabel;
    Label81: TLabel;
    Label27: TLabel;
    Label30: TLabel;
    FiskalPLZ: TEdit;
    FiskalStreetEdit: TEdit;
    FiskalNameEdit: TEdit;
    FiskalOrt: TEdit;
    FiskalNameAddEdit: TEdit;
    FiskalLand: TEdit;
    FiskalHouseNoEdit: TEdit;
    FiskalMailEdit: TEdit;
    FiskalPhoneEdit: TEdit;
    FiskalVATEdit: TEdit;
    FiskalEORIEdit: TEdit;
    FiskalZollNrEdit: TEdit;
    FiskalCheckBox: TCheckBox;
    Label43: TLabel;
    FiskalAccountEdit: TEdit;
    procedure FormShow(Sender: TObject);
    procedure FormClose(Sender: TObject; var Action: TCloseAction);
    procedure FormCreate(Sender: TObject);
    procedure AufAblaufDataSourceDataChange(Sender: TObject; Field: TField);
    procedure FormDestroy(Sender: TObject);
    procedure PlanQuashButtonClick(Sender: TObject);
    procedure PlanApplyButtonClick(Sender: TObject);
    procedure LSPrintCheckBoxClick(Sender: TObject);
    procedure NVEPrintCheckBoxClick(Sender: TObject);
    procedure ConfigTabSheetShow(Sender: TObject);
    procedure PlanTabSheetShow(Sender: TObject);
    procedure PlanFullPALCheckBoxClick(Sender: TObject);
    procedure NVEInhaltPrintCheckBoxClick(Sender: TObject);
    procedure LHMCheckBoxClick(Sender: TObject);
    procedure AufCfgDataSourceDataChange(Sender: TObject; Field: TField);
    procedure DefWALPCheckBoxClick(Sender: TObject);
    procedure WAComboBoxChange(Sender: TObject);
    procedure ConfigApplyButtonClick(Sender: TObject);
    procedure EmptyCheckBoxClick(Sender: TObject);
    procedure ConfigTabSheetResize(Sender: TObject);
    procedure FormResize(Sender: TObject);
    procedure MandComboBoxChange(Sender: TObject);
    procedure SubMandComboBoxChange(Sender: TObject);
    procedure AufVersTabSheetShow(Sender: TObject);
    procedure SpedRetComboBoxChange(Sender: TObject);
    procedure VersNewMenuItemClick(Sender: TObject);
    procedure AufVersDataSourceDataChange(Sender: TObject; Field: TField);
  private
    fCfgRef  : Integer;
    fPlanRef : Integer;
    fVersRef : Integer;

    fFirstFlag     : Boolean;

    fOldRefLager   : Integer;
    fOldRefMand    : Integer;
    fOldRefSubMand : Integer;

    procedure ClearVersTabSheet;

    function UpdateAufConfigQuery (Sender: TObject) : Integer;
    function UpdateAufAblaufQuery (Sender: TObject) : Integer;
    function UpdateAufVersandQuery (Sender: TObject) : Integer;
  public
    { Public-Deklarationen }
  end;

implementation

uses
  System.UITypes,
  VCLUtilitys, ConfigModul, DatenModul, DBGridUtilModule, FrontendUtils, LVSDatenInterface,
  SprachModul, ResourceText, SelectLagerMandantTraderDLG;

{$R *.dfm}

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TConfigAufAblaufForm.PlanApplyButtonClick(Sender: TObject);
var
  res : Integer;
  opt_plan, opt_lb, opt_zone : String;
  opt_komm, opt_wa : String;
  opt_ls, opt_nve, opt_inhalt : String;
  opt_grp : Char;
  countstr : String;
begin
  res := 0;

  if (fPlanRef > 0) then begin
    if NamePanel.Visible then
      res := SetAuftragPlanName (fPlanRef, NameEdit.Text, DescEdit.Text);

    if (res = 0) and (KommPlanGrpComboBox.Visible and KommPlanGrpComboBox.Enabled) then
      res := SetAuftragConfigKommPlanGroup (fPlanRef, GetComboBoxRef (KommPlanGrpComboBox));

    if (res = 0) and (TAGroupComboBox.Visible and TAGroupComboBox.Enabled) then
      res := SetAuftragConfigTAGroup (fPlanRef, GetComboBoxRef (TAGroupComboBox));

    if (res = 0) then begin
      opt_plan := '~~~~~~~~';  //1=OPT_FIFO, 2=OPT_VOLL_PAL_PLAN, 3=OPT_ANBRUCH_VOLL_PAL, 4=KOMM_LAUF_PLANUNG
      opt_lb   := '~~';
      opt_zone := '~~';

      if not PlanFiFoCheckBox.Enabled then
      else if PlanFiFoCheckBox.Checked then
        opt_plan [1] := '1'
      else
        opt_plan [1] := '0';

      if not PlanFullPALCheckBox.Enabled then
      else if PlanFullPALCheckBox.Checked then
        opt_plan [2] := '1'
      else
        opt_plan [2] := '0';

      if not PlanTeilPALCheckBox.Enabled then
      else if PlanTeilPALCheckBox.Checked then
        opt_plan [3] := '1'
      else
        opt_plan [3] := '0';

      if (KommPlanCombobox.ItemIndex = 0) then
        opt_plan [4] := 'F'
      else if (KommPlanCombobox.ItemIndex = 1) then
        opt_plan [4] := 'S'
      else if (KommPlanCombobox.ItemIndex = 2) then
        opt_plan [4] := 'P'
      else if (KommPlanCombobox.ItemIndex = 3) then
        opt_plan [4] := 'A';

      if not PlanKommVerdichtenCheckBox.Enabled or not PlanKommVerdichtenCheckBox.Visible then
      else if PlanKommVerdichtenCheckBox.Checked then
        opt_plan [5] := '1'
      else
        opt_plan [5] := '0';

      if BatchPlanCheckBox.Checked then
        opt_plan [6] := '1'
      else
        opt_plan [6] := '0';

      if not PickShipCheckBox.Enabled or not PickShipCheckBox.Visible then
      else if PickShipCheckBox.Checked then
        opt_plan [7] := '1'
      else
        opt_plan [7] := '0';


      if not KommPlanBereichCheckBox.Enabled then
      else if KommPlanBereichCheckBox.Checked then
        opt_lb [1] := '1'
      else
        opt_lb [1] := '0';

      if not KommPlanZoneCheckBox.Enabled then
      else if KommPlanZoneCheckBox.Checked then
        opt_zone [1] := '1'
      else
        opt_zone [1] := '0';

      if not PALKommPlanBereichCheckBox.Enabled then
      else if PALKommPlanBereichCheckBox.Checked then
        opt_lb [2] := '1'
      else
        opt_lb [2] := '0';

      if not PALKommPlanZoneCheckBox.Enabled then
      else if PALKommPlanZoneCheckBox.Checked then
        opt_zone [2] := '1'
      else
        opt_zone [2] := '0';

      opt_grp := '~';

      res := SetAuftragPlanOptKommPlan (fPlanRef, opt_plan, opt_lb, opt_zone, opt_grp);
    end;

    if (res = 0) then begin
      opt_wa   := '~';
      opt_komm := '~~~~~~~~~';
      opt_nve  := '~~';

      if (KommPackComboBox.ItemIndex = 0) then
        opt_komm [1] := 'W'
      else if (KommPackComboBox.ItemIndex = 1) then
        opt_komm [1] := 'P';

      if not KommMHDCheckBox.Enabled then
      else if KommMHDCheckBox.Checked then
        opt_komm [2] := '1'
      else
        opt_komm [2] := '0';

      if not KommChargeCheckBox.Enabled then
      else if KommChargeCheckBox.Checked then
        opt_komm [3] := '1'
      else
        opt_komm [3] := '0';

      if not KommScanPlaceCheckBox.Enabled then
      else if KommScanPlaceCheckBox.Checked then
        opt_komm [4] := '1'
      else
        opt_komm [4] := '0';

      if not KommScanSKUCheckBox.Enabled then
      else if KommScanSKUCheckBox.Checked then
        opt_komm [5] := '1'
      else
        opt_komm [5] := '0';

      if not KommSplitPackageCheckBox.Enabled then
      else if KommSplitPackageCheckBox.Checked then
        opt_komm [6] := '1'
      else
        opt_komm [6] := '0';

      if (KommArtComboBox.ItemIndex = 0) then
        opt_komm [7] := ' '
      else if (KommArtComboBox.ItemIndex = 1) then
        opt_komm [7] := 'P'
      else if (KommArtComboBox.ItemIndex = 2) then
        opt_komm [7] := 'K'
      else if (KommArtComboBox.ItemIndex = 3) then
        opt_komm [7] := 'L'
      else if (KommArtComboBox.ItemIndex = 4) then
        opt_komm [7] := 'F'
      else if (KommArtComboBox.ItemIndex = 5) then
        opt_komm [7] := 'W'
      else if (KommArtComboBox.ItemIndex = 6) then
        opt_komm [7] := 'O'
      else if (KommArtComboBox.ItemIndex = 7) then
        opt_komm [7] := 'S'
      else
        opt_komm [7] := '0';

      if (WAAblaufComboBox.ItemIndex = 0) then
        opt_wa := 'V'
      else if (WAAblaufComboBox.ItemIndex = 1) then
        opt_wa := 'W';

      if (PrintMDENVECheckBox.Checked) then
        opt_nve [1] := 'J'
      else
        opt_nve [1] := 'N';

      if (PrintMDENVEInhaltCheckBox.Checked) then
        opt_nve [2] := 'J'
      else
        opt_nve [2] := 'N';

      res := SetAuftragPlanOptKomm (fPlanRef, opt_komm, opt_wa, opt_nve);
    end;

   if (res = 0) then begin
      opt_wa   := '~~~~~~~~';  //1=OPT_LHM_ERFASSEN, 2=OPT_ABSCHLUSS_LHM_ERFASSEN, 3=OPT_ASSIGN_PACK, 4=OPT_NVE_MASTER_REQUIRED, 5=OPT_MDE_LHM_ERFASSEN

      if not LHMCheckBox.Enabled then
      else if LHMCheckBox.Checked then
        opt_wa [1] := '1'
      else
        opt_wa [1] := '0';

      if not AutoLHMCheckBox.Enabled then
      else if AutoLHMCheckBox.Checked then
        opt_wa [2] := '1'
      else
        opt_wa [2] := '0';

      if WAPackplatzComboBox.Visible then begin
        if WAPackplatzComboBox.ItemIndex = 1 then
          opt_wa [3] := '1'
        else
          opt_wa [3] := '0';
      end;

      if WAMasterCheckBox.Visible then begin
        if WAMasterCheckBox.Checked then
          opt_wa [4] := '1'
        else
          opt_wa [4] := '0';
      end;

      if not (MDELHMCheckBox.Visible and MDELHMCheckBox.Enabled) then
      else if MDELHMCheckBox.Checked then
        opt_wa [5] := '1'
      else
        opt_wa [5] := '0';

      if not (DefWALPCheckBox.Visible) then
      else if DefWALPCheckBox.Enabled and DefWALPCheckBox.Checked then
        opt_wa [6] := '2'
      else if DefWALPCheckBox.Enabled and SetWALPCheckBox.Checked then
        opt_wa [6] := '1'
      else
        opt_wa [6] := '0';

      if not EmptyCheckBox.Visible or not EmptyCheckBox.Enabled then
      else if EmptyCheckBox.Checked then
        opt_wa [7] := '1'
      else
        opt_wa [7] := '0';

      if not AutoEmptyCheckBox.Visible or not AutoEmptyCheckBox.Enabled then
      else if AutoEmptyCheckBox.Checked then
        opt_wa [8] := '1'
      else
        opt_wa [8] := '0';

      res := SetAuftragPlanOptWA (fPlanRef, opt_wa);

      if (res = 0) then
        res := SetAuftragPlanRelationWA (fPlanRef, GetComboBoxRef (WARelComboBox), GetComboBoxRef (WAPackplatzComboBox), GetComboBoxRef (WABufferComboBox), GetComboBoxRef (WACrossdockComboBox), GetComboBoxRef (WAUmpackComboBox));
    end;

    if (res = 0) then begin
      if (Length (LSCopyEdit.Text) = 0) then
        countstr := '1'
      else
        countstr := IntToStr (LSCopyUpDown.Position + 1);

      if not (LSPrintCheckBox.Checked) then
        opt_ls := '00'
      else if (AutoLSPrintCheckBox.Checked) then
        opt_ls := countstr+'1'
      else
        opt_ls := countstr+'0';

      if (Length (NVECopyEdit.Text) = 0) then
        countstr := '1'
      else
        countstr := IntToStr (NVECopyUpDown.Position + 1);

      if not (NVEPrintCheckBox.Checked) then
        opt_nve := '00'
      else if (AutoNVEPrintCheckBox.Checked) then
        opt_nve := countstr+'1'
      else
        opt_nve := countstr+'0';

      if not NVEInhaltPrintCheckBox.Checked then
        opt_inhalt := '00'
      else begin
        if NVEinhaltLabelCheckBox.Checked then
          opt_inhalt := 'B'
        else
          opt_inhalt := '1';

        if AutoNVEInhaltPrintCheckBox.Checked then
          opt_inhalt := opt_inhalt + '1'
        else
          opt_inhalt := opt_inhalt + '0';
      end;

      res := SetAuftragPlanOptPrint (fPlanRef, opt_ls, opt_nve, opt_inhalt);
    end;

    if (res = 0) and ((DeliverNotifyCheckBox.Visible or LSDMSCheckBox.Visible)) then begin
      if DeliverNotifyCheckBox.Checked then
        opt_ls := '1'
      else
        opt_ls := '0';

      if LSDMSCheckBox.Checked then
        opt_nve := '1'
      else
        opt_nve := '0';

      res := SetAuftragPlanOptMisc (fPlanRef, opt_ls, opt_nve, '~', '~', '~', '~', '~', '~');
   end;

    if (res = 0) then
      AufAblaufDBGrid.Reload (fPlanRef)
    else
      MessageDLG ('Fehler beim Ändern der Auftragesplanung' + #13 + #13 + LVSDatenModul.LastLVSErrorText, mtError, [mbOK], 0);
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TConfigAufAblaufForm.AufAblaufDataSourceDataChange(Sender: TObject; Field: TField);
var
  ch    : Char;
  query: TADOQuery;
begin
  if (AufAblaufQuery.Active and (AufAblaufQuery.RecNo > 0)) then begin
    if (fFirstFlag) then begin
      fFirstFlag := False;

      NamePanel.Visible := Assigned (AufAblaufQuery.FindField('NAME'));

      WAMasterCheckBox.Visible := Assigned (AufAblaufQuery.FindField('OPT_NVE_MASTER_REQUIRED'));
      WAPackplatzComboBox.Visible := Assigned (AufAblaufQuery.FindField('REF_PACKPLATZ'));
      WACrossdockComboBox.Visible := Assigned (AufAblaufQuery.FindField('REF_CROSSDOCK_RELATION'));
      WABufferComboBox.Visible := Assigned (AufAblaufQuery.FindField('REF_BUFFER_RELATION'));
      WAUmpackComboBox.Visible := Assigned (AufAblaufQuery.FindField('REF_UMPACK_RELATION'));
      MDELHMCheckBox.Visible := Assigned (AufAblaufQuery.FindField('OPT_MDE_LHM_ERFASSEN'));
      PlanKommVerdichtenCheckBox.Visible := Assigned (AufAblaufQuery.FindField('OPT_KOMM_POS_VERDICHTEN'));
      PickShipCheckBox.Visible := Assigned (AufAblaufQuery.FindField('OPT_PICK_AND_SHIP'));
      DefWALPCheckBox.Visible := Assigned (AufAblaufQuery.FindField('OPT_AUTO_WA_LP'));
      TAGroupComboBox.Visible := assigned(AufAblaufQuery.FindField('REF_TA_GROUP'));
      KommPlanGrpComboBox.Visible := assigned(AufAblaufQuery.FindField('REF_KOMM_PLAN_GRP'));
      EmptyCheckBox.Visible := assigned(AufAblaufQuery.FindField('OPT_LEER_VEREINNAHMEN'));
      AutoEmptyCheckBox.Visible := assigned(AufAblaufQuery.FindField('OPT_ABSCHLUSS_LEER_VEREINNAHMEN'));
      DeliverNotifyCheckBox.Visible := assigned(AufAblaufQuery.FindField('OPT_DELIVERY_NOTIFICATION'));
      LSDMSCheckBox.Visible := assigned(AufAblaufQuery.FindField('OPT_DMS_PDF_LS'));
      NVEInhaltPrintCheckBox.Visible := assigned(AufAblaufQuery.FindField('OPT_PRINT_NVE_INHALT'));

      AutoNVEPrintCheckBox.Visible   := NVEInhaltPrintCheckBox.Visible;
      NVEinhaltLabelCheckBox.Visible := NVEInhaltPrintCheckBox.Visible;

      SetWALPCheckBox.Visible := DefWALPCheckBox.Visible;

      Label9.Visible  := WAPackplatzComboBox.Visible;
      Label10.Visible := WACrossdockComboBox.Visible;
      Label12.Visible := WABufferComboBox.Visible;
      Label13.Visible := WAUmpackComboBox.Visible;
      Label20.Visible := TAGroupComboBox.Visible;
      Label17.Visible := KommPlanGrpComboBox.Visible;
    end;

    if (fOldRefLager <> AufAblaufQuery.FieldByName ('REF_LAGER').AsInteger) then begin
      fOldRefLager := AufAblaufQuery.FieldByName ('REF_LAGER').AsInteger;

      if (TAGroupComboBox.Visible) then begin
        ClearComboBoxObjects(TAGroupComboBox);

        query := TADOQuery.Create (Self);

        try
          query.LockType := ltReadOnly;
          query.Connection := LVSDatenModul.MainADOConnection;

          query.SQL.Add ('select REF_TA_GROUP,SECTION,DESCRIPTION from V_TA_GROUP where REF_PARENT is not null and REF_LB is null and REF_LB_ZONE is null and STATUS=''AKT'' and REF_LAGER=:ref_lager order by SORT_ORDER nulls last');
          query.Parameters [0].Value := fOldRefLager;

          query.Open;

          while not query.Eof do begin
            TAGroupComboBox.Items.AddObject(query.Fields [1].AsString+'|'+query.Fields [2].AsString, TComboBoxRef.Create(query.Fields [0].AsInteger));

            query.Next;
          end;

          query.Close;
        finally
          query.Free;
        end;

        if (TAGroupComboBox.Items.Count = 0) then
          TAGroupComboBox.Enabled := False
        else begin
          TAGroupComboBox.Enabled := True;
          TAGroupComboBox.Items.Insert(0, GetResourceText(1509))
        end;
      end;

      if (KommPlanGrpComboBox.Visible) then begin
        ClearComboBoxObjects(KommPlanGrpComboBox);

        query := TADOQuery.Create (Self);

        try
          query.LockType := ltReadOnly;
          query.Connection := LVSDatenModul.MainADOConnection;

          query.SQL.Add ('select * from V_KOMM_PLAN_GROUP where REF_LAGER=:ref_lager order by NAME');
          query.Parameters [0].Value := fOldRefLager;

          query.Open;

          while not query.Eof do begin
            KommPlanGrpComboBox.Items.AddObject(query.FieldByName ('NAME').AsString+'|'+query.FieldByName ('DESCRIPTION').AsString, TComboBoxRef.Create(query.FieldByName ('REF').AsInteger));

            query.Next;
          end;

          query.Close;
        finally
          query.Free;
        end;

        if (KommPlanGrpComboBox.Items.Count = 0) then
          KommPlanGrpComboBox.Enabled := False
        else begin
          KommPlanGrpComboBox.Enabled := True;
          KommPlanGrpComboBox.Items.Insert(0, GetResourceText(1509))
        end;
      end;

      if (WARelComboBox.Visible) then begin
        LoadRelationen (WARelComboBox, 'WA', fOldRefLager);
        WARelComboBox.Items.Insert (0, '');
      end;

      if (WABufferComboBox.Visible) then begin
        LoadRelationen (WABufferComboBox, 'WA', fOldRefLager);
        WABufferComboBox.Items.Insert (0, '');
      end;

      if (WACrossdockComboBox.Visible) then begin
        LoadRelationen (WACrossdockComboBox, 'WA', fOldRefLager);
        WACrossdockComboBox.Items.Insert (0, '');
      end;

      if (WAUmpackComboBox.Visible) then begin
        LoadRelationen (WAUmpackComboBox, 'WA', fOldRefLager);
        WAUmpackComboBox.Items.Insert (0, '');
      end;

      if (WAPackplatzComboBox.Visible) then begin
        LoadPackplatz (WAPackplatzComboBox, fOldRefLager);
        WAPackplatzComboBox.Items.Insert (0, '');
        WAPackplatzComboBox.Items.Insert (1, 'Automatisch zuordnen');
      end;
    end;

    fPlanRef := AufAblaufQuery.FieldByName ('REF').AsInteger;

    if NamePanel.Visible then begin
      NameEdit.Text := AufAblaufQuery.FieldByName ('NAME').AsString;
      DescEdit.Text := AufAblaufQuery.FieldByName ('DESCRIPTION').AsString;
    end;

    if not Assigned (AufAblaufQuery.FindField ('REF_TA_GROUP')) then
      TAGroupComboBox.Visible := false
    else if AufAblaufQuery.FieldByName('REF_TA_GROUP').IsNull then
      TAGroupComboBox.ItemIndex := 0
    else begin
      TAGroupComboBox.ItemIndex := FindComboboxRef(TAGroupComboBox, AufAblaufQuery.FieldByName('REF_TA_GROUP').AsInteger);
      if (TAGroupComboBox.ItemIndex = -1) then TAGroupComboBox.ItemIndex := 0;
    end;

    if not Assigned (AufAblaufQuery.FindField ('REF_KOMM_PLAN_GRP')) then
      KommPlanGrpComboBox.Visible := false
    else if AufAblaufQuery.FieldByName('REF_KOMM_PLAN_GRP').IsNull then
      KommPlanGrpComboBox.ItemIndex := 0
    else begin
      KommPlanGrpComboBox.ItemIndex := FindComboboxRef(KommPlanGrpComboBox, AufAblaufQuery.FieldByName('REF_KOMM_PLAN_GRP').AsInteger);
      if (KommPlanGrpComboBox.ItemIndex = -1) then KommPlanGrpComboBox.ItemIndex := 0;
    end;

    BatchPlanCheckBox.Checked  := AufAblaufQuery.FieldByName ('OPT_BATCH_PLANUNG').AsString = '1';

    if (AufAblaufQuery.FieldByName ('OPT_PACK_ABWICKLUNG').IsNull) then
      KommPackComboBox.ItemIndex := 0
    else if (AufAblaufQuery.FieldByName ('OPT_PACK_ABWICKLUNG').AsString = 'P') then
      KommPackComboBox.ItemIndex := 1
    else
      KommPackComboBox.ItemIndex := 0;

    if (AufAblaufQuery.FieldByName ('OPT_WA_ABWICKLUNG').IsNull) then
      WAAblaufComboBox.ItemIndex := 0
    else if (AufAblaufQuery.FieldByName ('OPT_WA_ABWICKLUNG').AsString = 'V') then
      WAAblaufComboBox.ItemIndex := 0
    else if (AufAblaufQuery.FieldByName ('OPT_WA_ABWICKLUNG').AsString = 'W') then
      WAAblaufComboBox.ItemIndex := 1
    else
      WAAblaufComboBox.ItemIndex := 0;

    if (AufAblaufQuery.FieldByName ('OPT_KOMM_ART').IsNull) then
      KommArtComboBox.ItemIndex := 0
    else if (AufAblaufQuery.FieldByName ('OPT_KOMM_ART').AsString = 'P') then
      KommArtComboBox.ItemIndex := 1
    else if (AufAblaufQuery.FieldByName ('OPT_KOMM_ART').AsString = 'K') then
      KommArtComboBox.ItemIndex := 2
    else if (AufAblaufQuery.FieldByName ('OPT_KOMM_ART').AsString = 'L') then
      KommArtComboBox.ItemIndex := 3
    else if (AufAblaufQuery.FieldByName ('OPT_KOMM_ART').AsString = 'F') then
      KommArtComboBox.ItemIndex := 4
    else if (AufAblaufQuery.FieldByName ('OPT_KOMM_ART').AsString = 'W') then
      KommArtComboBox.ItemIndex := 5
    else if (AufAblaufQuery.FieldByName ('OPT_KOMM_ART').AsString = 'O') then
      KommArtComboBox.ItemIndex := 6
    else if (AufAblaufQuery.FieldByName ('OPT_KOMM_ART').AsString = 'S') then //Pick & Ship
      KommArtComboBox.ItemIndex := 7
    else
      KommArtComboBox.ItemIndex := 0;

    if (AufAblaufQuery.FieldByName ('KOMM_LAUF_PLANUNG').IsNull) then
      KommPlanCombobox.ItemIndex := 0
    else if (AufAblaufQuery.FieldByName ('KOMM_LAUF_PLANUNG').AsString = 'F') then
      KommPlanCombobox.ItemIndex := 0
    else if (AufAblaufQuery.FieldByName ('KOMM_LAUF_PLANUNG').AsString = 'S') then
      KommPlanCombobox.ItemIndex := 1
    else if (AufAblaufQuery.FieldByName ('KOMM_LAUF_PLANUNG').AsString = 'P') then
      KommPlanCombobox.ItemIndex := 2
    else if (AufAblaufQuery.FieldByName ('KOMM_LAUF_PLANUNG').AsString = 'A') then
      KommPlanCombobox.ItemIndex := 3
    else
      KommPlanCombobox.ItemIndex := 0;

    if (PlanKommVerdichtenCheckBox.Visible) then
      PlanKommVerdichtenCheckBox.Checked := AufAblaufQuery.FieldByName ('OPT_KOMM_POS_VERDICHTEN').AsString = '1';

    if (PickShipCheckBox.Visible) then
      PickShipCheckBox.Checked := AufAblaufQuery.FieldByName ('OPT_PICK_AND_SHIP').AsString = '1';

    PrintMDENVECheckBox.Checked       := (AufAblaufQuery.FieldByName ('OPT_MDE_NVE_PRINT').AsString = 'J') or (AufAblaufQuery.FieldByName ('OPT_MDE_NVE_PRINT').AsString = 'Y') or (AufAblaufQuery.FieldByName ('OPT_MDE_NVE_PRINT').AsString = '1');
    PrintMDENVEInhaltCheckBox.Checked := (AufAblaufQuery.FieldByName ('OPT_MDE_NVE_INHALT_PRINT').AsString = 'J') or (AufAblaufQuery.FieldByName ('OPT_MDE_NVE_INHALT_PRINT').AsString = 'Y') or (AufAblaufQuery.FieldByName ('OPT_MDE_NVE_INHALT_PRINT').AsString = '1');

    KommPlanBereichCheckBox.Checked  := AufAblaufQuery.FieldByName ('KOMM_LB_PLANEN').AsString = '1';
    KommPlanZoneCheckBox.Checked     := AufAblaufQuery.FieldByName ('KOMM_ZONEN_PLANEN').AsString = '1';

    PALKommPlanBereichCheckBox.Checked  := AufAblaufQuery.FieldByName ('PAL_LB_PLANEN').AsString = '1';
    PALKommPlanZoneCheckBox.Checked     := AufAblaufQuery.FieldByName ('PAL_ZONEN_PLANEN').AsString = '1';

    PlanFiFoCheckBox.Checked          := AufAblaufQuery.FieldByName ('OPT_FIFO').AsString = '1';
    PlanFullPALCheckBox.Checked       := AufAblaufQuery.FieldByName ('OPT_VOLL_PAL_PLAN').AsString = '1';
    PlanTeilPALCheckBox.Checked       := AufAblaufQuery.FieldByName ('OPT_ANBRUCH_VOLL_PAL').AsString = '1';

    if Assigned (AufAblaufQuery.FindField('OPT_SPLIT_PACKAGE_KOMM')) then
      KommSplitPackageCheckBox.Checked := (AufAblaufQuery.FieldByName('OPT_SPLIT_PACKAGE_KOMM').AsString > '0')
    else begin
      KommSplitPackageCheckBox.Visible := false;
      KommSplitPackageCheckBox.Enabled := false;
    end;

    if Assigned(AufAblaufQuery.FindField('OPT_MHD_PICK')) then
      KommMHDCheckBox.Checked := AufAblaufQuery.FieldByName ('OPT_MHD_PICK').AsString = '1'
    else begin
      KommMHDCheckBox.Visible := False;
      KommMHDCheckBox.Enabled := False;
    end;

    if Assigned(AufAblaufQuery.FindField('OPT_CHARGE_PICK')) then
      KommChargeCheckBox.Checked := AufAblaufQuery.FieldByName ('OPT_CHARGE_PICK').AsString = '1'
    else begin
      KommChargeCheckBox.Visible := False;
      KommChargeCheckBox.Enabled := False;
    end;

    if Assigned(AufAblaufQuery.FindField('OPT_SCAN_PLACE')) then
      KommScanPlaceCheckBox.Checked := AufAblaufQuery.FieldByName ('OPT_SCAN_PLACE').AsString = '1'
    else begin
      KommScanPlaceCheckBox.Visible := False;
      KommScanPlaceCheckBox.Enabled := False;
    end;

    if Assigned(AufAblaufQuery.FindField('OPT_SCAN_SKU')) then
      KommScanSKUCheckBox.Checked := AufAblaufQuery.FieldByName ('OPT_SCAN_SKU').AsString = '1'
    else begin
      KommScanSKUCheckBox.Visible := False;
      KommScanSKUCheckBox.Enabled := False;
    end;

    if (AufAblaufQuery.FieldByName ('REF_RELATION').IsNull) then
      WARelComboBox.ItemIndex := 0
    else begin
      WARelComboBox.ItemIndex := FindComboboxRef(WARelComboBox, AufAblaufQuery.FieldByName ('REF_RELATION').AsInteger);
      if (WARelComboBox.ItemIndex = -1) then WARelComboBox.ItemIndex := 0;
    end;

    if (WAPackplatzComboBox.Visible) then begin
      if Assigned (AufAblaufQuery.FindField ('OPT_ASSIGN_PACK')) and (AufAblaufQuery.FieldByName ('OPT_ASSIGN_PACK').AsString = '1') then
        WAPackplatzComboBox.ItemIndex := 1
      else if (AufAblaufQuery.FieldByName ('REF_PACKPLATZ').IsNull) then
        WAPackplatzComboBox.ItemIndex := 0
      else begin
        WAPackplatzComboBox.ItemIndex := FindComboboxRef(WAPackplatzComboBox, AufAblaufQuery.FieldByName ('REF_PACKPLATZ').AsInteger);
        if (WAPackplatzComboBox.ItemIndex = -1) then WAPackplatzComboBox.ItemIndex := 0;
      end;
    end;

    if (WABufferComboBox.Visible) then begin
      if (AufAblaufQuery.FieldByName ('REF_BUFFER_RELATION').IsNull) then
        WABufferComboBox.ItemIndex := 0
      else begin
        WABufferComboBox.ItemIndex := FindComboboxRef(WABufferComboBox, AufAblaufQuery.FieldByName ('REF_BUFFER_RELATION').AsInteger);
        if (WABufferComboBox.ItemIndex = -1) then WABufferComboBox.ItemIndex := 0;
      end;
    end;

    if (WACrossdockComboBox.Visible) then begin
      if (AufAblaufQuery.FieldByName ('REF_CROSSDOCK_RELATION').IsNull) then
        WACrossdockComboBox.ItemIndex := 0
      else begin
        WACrossdockComboBox.ItemIndex := FindComboboxRef(WACrossdockComboBox, AufAblaufQuery.FieldByName ('REF_CROSSDOCK_RELATION').AsInteger);
        if (WACrossdockComboBox.ItemIndex = -1) then WACrossdockComboBox.ItemIndex := 0;
      end;
    end;

    if (WAUmpackComboBox.Visible) then begin
      if (AufAblaufQuery.FieldByName ('REF_UMPACK_RELATION').IsNull) then
        WAUmpackComboBox.ItemIndex := 0
      else begin
        WAUmpackComboBox.ItemIndex := FindComboboxRef(WAUmpackComboBox, AufAblaufQuery.FieldByName ('REF_UMPACK_RELATION').AsInteger);
        if (WAUmpackComboBox.ItemIndex = -1) then WAUmpackComboBox.ItemIndex := 0;
      end;
    end;

    LHMCheckBox.Checked          := AufAblaufQuery.FieldByName ('OPT_LHM_ERFASSEN').AsString = '1';
    AutoLHMCheckBox.Checked      := AufAblaufQuery.FieldByName ('OPT_ABSCHLUSS_LHM_ERFASSEN').AsString > '0';

    if (EmptyCheckBox.Visible) then begin
      EmptyCheckBox.Checked      := AufAblaufQuery.FieldByName ('OPT_LEER_VEREINNAHMEN').AsString = '1';
      AutoEmptyCheckBox.Checked  := AufAblaufQuery.FieldByName ('OPT_ABSCHLUSS_LEER_VEREINNAHMEN').AsString > '0';
    end;

    if (WAMasterCheckBox.Visible) then
      WAMasterCheckBox.Checked   := AufAblaufQuery.FieldByName ('OPT_NVE_MASTER_REQUIRED').AsString = '1';

    //Liefreschein drucken
    LSPrintCheckBox.Checked      := AufAblaufQuery.FieldByName ('OPT_PRINT_LS').AsString > '0';

    //Anzahl der Kopien ermitteln
    if (Length (AufAblaufQuery.FieldByName ('OPT_PRINT_LS').AsString) = 0) then
      LSCopyEdit.Text := ''
    else begin
      ch := AufAblaufQuery.FieldByName ('OPT_PRINT_LS').AsString [1];

      if (ch in ['1'..'9']) then
        LSCopyUpDown.Position := (Ord (ch) - Ord ('0')) - 1
      else
        LSCopyEdit.Text := '';
    end;

    AutoLSPrintCheckBox.Checked  := AufAblaufQuery.FieldByName ('OPT_AUTO_PRINT_LS').AsString = '1';

    //NVE-Label drucken
    NVEPrintCheckBox.Checked     := AufAblaufQuery.FieldByName ('OPT_PRINT_NVE').AsString > '0';

    //Anzahl der Kopien ermitteln
    if (Length (AufAblaufQuery.FieldByName ('OPT_PRINT_NVE').AsString) = 0) then
      NVECopyEdit.Text := ''
    else begin
      ch := AufAblaufQuery.FieldByName ('OPT_PRINT_NVE').AsString [1];

      if (ch in ['1'..'9']) then
        NVECopyUpDown.Position := (Ord (ch) - Ord ('0')) - 1
      else
        NVECopyEdit.Text := '';
    end;

    AutoNVEPrintCheckBox.Checked := AufAblaufQuery.FieldByName ('OPT_AUTO_PRINT_NVE').AsString = '1';

    if DeliverNotifyCheckBox.Visible then
      DeliverNotifyCheckBox.Checked := AufAblaufQuery.FieldByName ('OPT_DELIVERY_NOTIFICATION').AsString > '0';

    if LSDMSCheckBox.Visible then
      LSDMSCheckBox.Checked := AufAblaufQuery.FieldByName ('OPT_DMS_PDF_LS').AsString > '0';

    if MDELHMCheckBox.Visible then
      MDELHMCheckBox.Checked := AufAblaufQuery.FieldByName ('OPT_MDE_LHM_ERFASSEN').AsString = '1';

    if (DefWALPCheckBox.Visible) then begin
      DefWALPCheckBox.Checked   := AufAblaufQuery.FieldByName ('OPT_AUTO_WA_LP').AsString = '2';
      SetWALPCheckBox.Checked   := AufAblaufQuery.FieldByName ('OPT_AUTO_WA_LP').AsString = '1';
    end;


    if NVEInhaltPrintCheckBox.Visible then begin
      NVEInhaltPrintCheckBox.Checked          := AufAblaufQuery.FieldByName ('OPT_PRINT_NVE_INHALT').AsString > '0';
      AutoNVEInhaltPrintCheckBox.Checked      := AufAblaufQuery.FieldByName ('OPT_AUTO_PRINT_NVE_INHALT').AsString = '1';
      NVEinhaltLabelCheckBox.Checked          := AufAblaufQuery.FieldByName ('OPT_PRINT_NVE_INHALT').AsString > 'A';
    end;

    WAComboBoxChange (Sender);

    PlanFullPALCheckBoxClick (Sender);
    LHMCheckBoxClick (Sender);
    LSPrintCheckBoxClick (Sender);
    NVEPrintCheckBoxClick (Sender);
    NVEInhaltPrintCheckBoxClick (Sender);
    DefWALPCheckBoxClick(Sender);
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TConfigAufAblaufForm.AufCfgDataSourceDataChange(Sender: TObject; Field: TField);
var
  intwert : Integer;
  query   : TADOQuery;
begin
  if (AufCfgQuery.Active and (AufCfgQuery.RecNo > 0)) then begin
    fCfgRef := AufCfgQuery.FieldByName ('REF').AsInteger;

    if (fOldRefLager <> AufCfgQuery.FieldByName ('REF_LAGER').AsInteger) then begin
      fOldRefLager := AufCfgQuery.FieldByName ('REF_LAGER').AsInteger;

      query    := TADOQuery.Create (Self);

      try
        query.LockType := ltReadOnly;
        query.Connection := LVSDatenModul.MainADOConnection;

        query.SQL.Clear;
        query.SQl.Add ('SELECT * FROM V_SPEDITIONEN where STATUS=''ANG'' and ((REF_LAGER is not null and REF_LAGER=:ref_lager) or (REF_LAGER is null and (REF_LOCATION is null or REF_LOCATION=:ref_loc))) and (REF_MAND is null or REF_MAND=:ref_mand)');
        query.SQl.Add ('order by REF_MAND nulls last, upper (NAME)');
        query.Parameters.ParamByName ('ref_lager').Value := fOldRefLager;
        query.Parameters.ParamByName ('ref_loc').Value   := LVSDatenModul.AktLocationRef;
        query.Parameters.ParamByName ('ref_mand').Value  := LVSDatenModul.AktMandantRef;

        ClearComboBoxObjects (SpedPrefComboBox);
        ClearComboBoxObjects (SpedDefaultComboBox);
        ClearComboBoxObjects (SpedRetComboBox);

        try
          query.Open;

          while not (query.Eof) do begin
            SpedPrefComboBox.AddItemIndex (query.FieldByName('NAME').AsString+'|'+query.FieldByName('BESCHREIBUNG').AsString, TComboBoxRef.Create (query.FieldByName('REF').AsInteger));
            SpedDefaultComboBox.AddItemIndex (query.FieldByName('NAME').AsString+'|'+query.FieldByName('BESCHREIBUNG').AsString, TComboBoxRef.Create (query.FieldByName('REF').AsInteger));
            SpedRetComboBox.AddItemIndex (query.FieldByName('NAME').AsString+'|'+query.FieldByName('BESCHREIBUNG').AsString, TComboBoxRef.Create (query.FieldByName('REF').AsInteger));

            query.Next;
          end;

          query.Close;
        except
        end;

        SpedPrefComboBox.Items.Insert(0, '');
        SpedPrefComboBox.ItemIndex := 0;

        SpedDefaultComboBox.Items.Insert(0, '');
        SpedDefaultComboBox.ItemIndex := 0;

        SpedRetComboBox.Items.Insert(0, '');
        SpedRetComboBox.ItemIndex := 0;
      finally
        query.Free;
      end;

      LoadLTCombobox(LTComboBox, 'WA', LVSDatenModul.AktLocationRef, fOldRefLager);
      LTComboBox.Items.Insert(0, '');
    end;

    if not Assigned (AufCfgQuery.FindField('OPT_DETERMINE_LT')) then
      DetermineLTCheckBox.Visible := false
    else
      DetermineLTCheckBox.Checked := AufCfgQuery.FieldByName ('OPT_DETERMINE_LT').AsString = '1';

    if not Assigned (AufCfgQuery.FindField('OPT_VOR_RESERVIERUNG')) then
      VorResCheckBox.Visible := false
    else
      VorResCheckBox.Checked := AufCfgQuery.FieldByName ('OPT_VOR_RESERVIERUNG').AsString = '1';

    if not Assigned (AufCfgQuery.FindField('OPT_TEIL_LIEFERUNG')) then
      PartDeliveryCheckBox.Visible := false
    else
      PartDeliveryCheckBox.Checked := AufCfgQuery.FieldByName ('OPT_TEIL_LIEFERUNG').AsString = '1';

    if not Assigned (AufCfgQuery.FindField('OPT_ONE_PACKAGE')) then
      OnePackageCheckBox.Visible := false
    else
      OnePackageCheckBox.Checked := AufCfgQuery.FieldByName ('OPT_ONE_PACKAGE').AsString = '1';

    if not Assigned (AufCfgQuery.FindField('OPT_DELIVERY_NOTE_BY_MAIL')) then
      DeliveryNoteMailCheckBox.Visible := false
    else
      DeliveryNoteMailCheckBox.Checked := AufCfgQuery.FieldByName ('OPT_DELIVERY_NOTE_BY_MAIL').AsString = '1';

    KonfAufCheckBox.Checked := AufCfgQuery.FieldByName ('OPT_PRODUCTION_ORDER').AsString = '1';
    KonfBestCheckBox.Checked := AufCfgQuery.FieldByName ('OPT_CREATE_PRODUCTION_WE').AsString = '1';

    if not Assigned (AufCfgQuery.FindField('DEFAULT_PRIO')) then begin
      PrioEdit.Visible := False;
      PrioUpDown.Visible := False;
      PrioUpDown.Position := 0;
      PrioEdit.Text := '';
    end else begin
      if not AufCfgQuery.FieldByName('DEFAULT_PRIO').IsNull then
        PrioUpDown.Position := AufCfgQuery.FieldByName('DEFAULT_PRIO').AsInteger
      else begin
        PrioUpDown.Position := 0;
        PrioEdit.Text := '';
      end;
    end;

    if not Assigned (AufCfgQuery.FindField('BBD_REMAINING')) then begin
      BBDRemainingEdit.Visible := False;
      BBDRemainingUpDown.Visible := False;
      BBDRemainingUpDown.Position := 0;
      BBDRemainingEdit.Text := '';
    end else begin
      if not AufCfgQuery.FieldByName('BBD_REMAINING').IsNull then
        BBDRemainingUpDown.Position := AufCfgQuery.FieldByName('BBD_REMAINING').AsInteger
      else begin
        BBDRemainingUpDown.Position := 0;
        BBDRemainingEdit.Text := '';
      end;
    end;

    if AufCfgQuery.FieldByName('REF_DEF_LT').IsNull then
      LTCombobox.ItemIndex := 0
    else
      LTCombobox.ItemIndex := FindComboboxRef (LTComboBox, AufCfgQuery.FieldByName('REF_DEF_LT').AsInteger);

    SpedRoleEdit.Text := AufCfgQuery.FieldByName('DEFAULT_SPED_NR').AsString;

    if not Assigned (AufCfgQuery.FindField('LT_PACKING_GROUP')) then
      PackTypeGroupComboBox.Enabled := false
    else if (AufCfgQuery.FieldByName('LT_PACKING_GROUP').IsNull) then
      PackTypeGroupComboBox.ItemIndex := 0
    else begin
      PackTypeGroupComboBox.ItemIndex := FindComboboxDBItem (PackTypeGroupComboBox, AufCfgQuery.FieldByName('LT_PACKING_GROUP').AsString);

      if (PackTypeGroupComboBox.ItemIndex = -1) then
        PackTypeGroupComboBox.ItemIndex := PackTypeGroupComboBox.Items.AddObject(AufCfgQuery.FieldByName('LT_PACKING_GROUP').AsString, TDBItemsDaten.Create ('LT_PACKING_GROUP', AufCfgQuery.FieldByName('LT_PACKING_GROUP').AsString, AufCfgQuery.FieldByName('LT_PACKING_GROUP').AsString, '', ''));
    end;

    if (SpedPrefComboBox.Items.Count > 0) then begin
      if AufCfgQuery.FieldByName('REF_PREDEF_SPED').IsNull then
        SpedPrefComboBox.ItemIndex := 0
      else
        SpedPrefComboBox.ItemIndex := FindComboboxRef (SpedPrefComboBox, AufCfgQuery.FieldByName('REF_PREDEF_SPED').AsInteger);
    end;

    if (SpedDefaultComboBox.Items.Count > 0) then begin
      if AufCfgQuery.FieldByName('REF_DEFAULT_SPED').IsNull then
        SpedDefaultComboBox.ItemIndex := 0
      else
        SpedDefaultComboBox.ItemIndex := FindComboboxRef (SpedDefaultComboBox, AufCfgQuery.FieldByName('REF_DEFAULT_SPED').AsInteger);
    end;

    if not Assigned (AufCfgQuery.FindField('REF_AUTO_RET_SPED')) then begin
      SpedRetComboBox.Visible := false;
      SpedRetLandEdit.Visible := false;
      RetPDFPathEdit.Visible := false;
      RetSendNrCheckBox.Visible := false;
    end else begin
      if (SpedRetComboBox.Items.Count = 0) then
        SpedRetComboBox.Enabled := false
      else if (SpedRetComboBox.Items.Count > 0) then begin
        if AufCfgQuery.FieldByName('REF_AUTO_RET_SPED').IsNull then
          SpedRetComboBox.ItemIndex := 0
        else
          SpedRetComboBox.ItemIndex := FindComboboxRef (SpedRetComboBox, AufCfgQuery.FieldByName('REF_AUTO_RET_SPED').AsInteger);
      end;

      if not Assigned (AufCfgQuery.FindField('AUTO_RET_LAND')) then
        SpedRetLandEdit.Visible := false
      else begin
        SpedRetLandEdit.Text := AufCfgQuery.FieldByName('AUTO_RET_LAND').AsString;
      end;

      if not Assigned (AufCfgQuery.FindField('FILE_DIR_PDF')) then
        RetPDFPathEdit.Visible := false
      else if (AnsiUpperCase(LVSDatenModul.AktUser) <> AnsiUpperCase(LVSDatenModul.Schema)) then begin
        RetPDFPathEdit.Enabled := false;
        RetPDFPathEdit.Text := AufCfgQuery.FieldByName('FILE_DIR_PDF').AsString;
      end else begin
        //Den Zielpfad kann nur der Schema-Owener eintragen
        RetPDFPathEdit.Enabled := true;
        RetPDFPathEdit.Text := AufCfgQuery.FieldByName('FILE_DIR_PDF').AsString;
      end;

      if not Assigned (AufCfgQuery.FindField('OPT_AUTO_RETOURE')) then
        RetSendNrCheckBox.Enabled := false
      else
        RetSendNrCheckBox.Checked := (AufCfgQuery.FieldByName('OPT_AUTO_RETOURE').AsString = '2');

      SpedRetComboBoxChange (Sender);
    end;

    DruckartEdit.Text := AufCfgQuery.FieldByName('DEFAULT_DRUCKART').AsString;

    if not Assigned (AufCfgQuery.FindField('OPT_PRINT_INVOICE')) then
      PrintInvoiceCheckBox.Visible := false
    else
      PrintInvoiceCheckBox.Checked := AufCfgQuery.FieldByName ('OPT_PRINT_INVOICE').AsString = '1';

    if not Assigned (AufCfgQuery.FindField('OPT_PROFORMA')) then
      PrintProformaCheckBox.Visible := false
    else
      PrintProformaCheckBox.Checked := AufCfgQuery.FieldByName ('OPT_PROFORMA').AsString = '1';

    if not Assigned (AufCfgQuery.FindField('OPT_PRINT_CMR')) then
      PrintCMRCheckBox.Visible := false
    else
      PrintCMRCheckBox.Checked := AufCfgQuery.FieldByName ('OPT_PRINT_CMR').AsString = '1';

    if not Assigned (AufCfgQuery.FindField('OPT_CHECK_FIRST_IMAGE')) then
      ShipLabelComboBox.Visible := false
    else if AufCfgQuery.FieldByName ('OPT_CHECK_FIRST_IMAGE').IsNull then
      ShipLabelComboBox.ItemIndex := 0
    else if TryStrToInt (AufCfgQuery.FieldByName ('OPT_CHECK_FIRST_IMAGE').AsString, intwert) then
      ShipLabelComboBox.ItemIndex := intwert
    else
      ShipLabelComboBox.ItemIndex := 0;

    Label5.Left        := SpedDefaultComboBox.Left;
    Label28.Left       := PackTypeGroupComboBox.Left;

    Label24.Visible     := SpedRetComboBox.Visible;
    Label24.Left        := SpedRetComboBox.Left;
    Label23.Visible     := SpedRetLandEdit.Visible;
    Label25.Visible     := RetPDFPathEdit.Visible;
    Label25.Left        := RetPDFPathEdit.Left;
    Label29.Visible     := ShipLabelComboBox.Visible;
    Label29.Left        := ShipLabelComboBox.Left;
    Label21.Visible     := BBDRemainingEdit.Visible;
    Label21.Left        := BBDRemainingEdit.Left;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TConfigAufAblaufForm.AufVersDataSourceDataChange(Sender: TObject; Field: TField);
var
  intwert : Integer;
  csvlist : TStringList;
  query   : TADOQuery;
begin
  ClearVersTabSheet;

  if (AufVersQuery.Active and (AufVersQuery.RecNo > 0)) then begin
    fVersRef := AufVersQuery.FieldByName ('REF').AsInteger;

    if (fOldRefLager <> AufVersQuery.FieldByName ('REF_LAGER').AsInteger) then begin
      fOldRefLager := AufVersQuery.FieldByName ('REF_LAGER').AsInteger;

      query    := TADOQuery.Create (Self);

      try
        query.LockType := ltReadOnly;
        query.Connection := LVSDatenModul.MainADOConnection;

        query.SQL.Clear;
        query.SQl.Add ('select * from V_SPEDITIONEN where STATUS=''ANG'' and ((REF_LAGER is not null and REF_LAGER=:ref_lager) or (REF_LAGER is null and (REF_LOCATION is null or REF_LOCATION=:ref_loc))) and (REF_MAND is null or REF_MAND=:ref_mand)');
        query.SQl.Add ('order by REF_MAND nulls last, upper (NAME)');
        query.Parameters.ParamByName ('ref_lager').Value := fOldRefLager;
        query.Parameters.ParamByName ('ref_loc').Value   := LVSDatenModul.AktLocationRef;
        query.Parameters.ParamByName ('ref_mand').Value  := AufVersQuery.FieldByName ('REF_MAND').AsInteger;

        ClearComboBoxObjects (VersSpedComboBox);

        try
          query.Open;

          while not (query.Eof) do begin
            VersSpedComboBox.AddItemIndex (query.FieldByName('NAME').AsString+'|'+query.FieldByName('BESCHREIBUNG').AsString, TComboBoxRef.Create (query.FieldByName('REF').AsInteger));

            query.Next;
          end;

          query.Close;
        except
        end;

        VersSpedComboBox.Items.Insert(0, '');
        VersSpedComboBox.ItemIndex := 0;
      finally
        query.Free;
      end;
    end;


    if AufVersQuery.FieldByName('LAND_ISO').IsNull then
      VersLandComboBox.ItemIndex := 0
    else begin
      VersLandComboBox.ItemIndex := FindComboboxLand (VersLandComboBox, AufVersQuery.FieldByName('LAND_ISO').AsString);
      if (VersLandComboBox.ItemIndex = -1) then VersLandComboBox.ItemIndex := VersLandComboBox.AddItemIndex (AufVersQuery.FieldByName('LAND_ISO').AsString, TDBItemsDaten.Create ('LAND_ISO', AufVersQuery.FieldByName('LAND_ISO').AsString, AufVersQuery.FieldByName('LAND_ISO').AsString, '', ''));
    end;

    IncotermEdit.Text := AufVersQuery.FieldByName('INCOTERM').AsString;
    VATEdit.Text      := AufVersQuery.FieldByName('VAT_ID').AsString;
    EORIEdit.Text     := AufVersQuery.FieldByName('EORI_NUMMER').AsString;
    ZollNrEdit.Text   := AufVersQuery.FieldByName('CUSTOM_NO').AsString;

    ChargesAccountEdit.Text := AufVersQuery.FieldByName('CHARGES_ACCOUNT').AsString;
    DutiesAccountEdit.Text  := AufVersQuery.FieldByName('DUTIES_ACCOUNT').AsString;
    SenderAccountEdit.Text  := AufVersQuery.FieldByName('SENDER_ACCOUNT').AsString;

    csvlist := TStringList.Create;

    try
      csvlist.Delimiter := ';';
      csvlist.StrictDelimiter := true;

      csvlist.DelimitedText := AufVersQuery.FieldByName ('FISKAL_ADDRESS').AsString;

      if (csvlist.Count > 0) then
        FiskalNameEdit.Text := csvlist [0];
      if (csvlist.Count > 1) then
        FiskalNameAddEdit.Text := csvlist [1];
      if (csvlist.Count > 2) then
        FiskalStreetEdit.Text := csvlist [2];
      if (csvlist.Count > 3) then
        FiskalHouseNoEdit.Text := csvlist [3];
      if (csvlist.Count > 4) then
        FiskalPLZ.Text := csvlist [4];
      if (csvlist.Count > 5) then
        FiskalOrt.Text := csvlist [5];
      if (csvlist.Count > 6) then
        FiskalLand.Text := csvlist [6];
      if (csvlist.Count > 7) then
        FiskalMailEdit.Text := csvlist [7];
      if (csvlist.Count > 8) then
        FiskalPhoneEdit.Text := csvlist [8];
    finally
      csvlist.Free;
    end;

    FiskalVATEdit.Text     := AufVersQuery.FieldByName('FISKAL_VAT_ID').AsString;
    FiskalEORIEdit.Text    := AufVersQuery.FieldByName('FISKAL_EORI_NUMMER').AsString;
    FiskalZollNrEdit.Text  := AufVersQuery.FieldByName('FISKAL_CUSTOM_NO').AsString;
    FiskalAccountEdit.Text := AufVersQuery.FieldByName('FISKAL_ACCOUNT').AsString;

    //Die Formulare
    DeliverNoteCheckBox.Checked := (AufVersQuery.FieldByName('OPT_PRINT_LS').AsString > '0');

    if AufVersQuery.FieldByName('OPT_PRINT_LS').IsNull or not TryStrToInt (AufVersQuery.FieldByName('OPT_PRINT_LS').AsString, intwert) or (intwert < 2) then
      DeliverNoteCopiesEdit.Text := ''
    else
      DeliverNoteCopiesUpDown.Position := intwert - 1;

    if AufVersQuery.FieldByName('LIEFERSCHEIN_FORMULAR').IsNull then
      DeliverNoteFormComboBox.ItemIndex := 0
    else begin
      DeliverNoteFormComboBox.ItemIndex := FindComboboxStr (DeliverNoteFormComboBox, AufVersQuery.FieldByName('LIEFERSCHEIN_FORMULAR').AsString);
      if (DeliverNoteFormComboBox.ItemIndex = -1) then
        DeliverNoteFormComboBox.ItemIndex := DeliverNoteFormComboBox.AddItemIndex (AufVersQuery.FieldByName('LIEFERSCHEIN_FORMULAR').AsString, TComboBoxStr.Create (AufVersQuery.FieldByName('LIEFERSCHEIN_FORMULAR').AsString));
    end;


    ProformaCheckBox.Checked := (AufVersQuery.FieldByName('OPT_PROFORMA').AsString > '0');

    if AufVersQuery.FieldByName('OPT_PROFORMA').IsNull or not TryStrToInt (AufVersQuery.FieldByName('OPT_PROFORMA').AsString, intwert) or (intwert < 2) then
      ProformaCopiesEdit.Text := ''
    else
      ProformaCopiesUpDown.Position := intwert - 1;

    if AufVersQuery.FieldByName('PROFORMA_FORMULAR').IsNull then
      ProformaFormComboBox.ItemIndex := 0
    else begin
      ProformaFormComboBox.ItemIndex := FindComboboxStr (ProformaFormComboBox, AufVersQuery.FieldByName('PROFORMA_FORMULAR').AsString);
      if (ProformaFormComboBox.ItemIndex = -1) then
        ProformaFormComboBox.ItemIndex := ProformaFormComboBox.AddItemIndex (AufVersQuery.FieldByName('PROFORMA_FORMULAR').AsString, TComboBoxStr.Create (AufVersQuery.FieldByName('LIEFERSCHEIN_FORMULAR').AsString));
    end;


    CN23CheckBox.Checked := (AufVersQuery.FieldByName('OPT_CN23').AsString > '0');

    if AufVersQuery.FieldByName('OPT_CN23').IsNull or not TryStrToInt (AufVersQuery.FieldByName('OPT_CN23').AsString, intwert) or (intwert < 2) then
      CN23CopiesEdit.Text := ''
    else
      CN23CopiesUpDown.Position := intwert - 1;

    if AufVersQuery.FieldByName('CN23_FORMULAR').IsNull then
      CN23FormComboBox.ItemIndex := 0
    else begin
      CN23FormComboBox.ItemIndex := FindComboboxStr (CN23FormComboBox, AufVersQuery.FieldByName('CN23_FORMULAR').AsString);
      if (CN23FormComboBox.ItemIndex = -1) then
        CN23FormComboBox.ItemIndex := CN23FormComboBox.AddItemIndex (AufVersQuery.FieldByName('CN23_FORMULAR').AsString, TComboBoxStr.Create (AufVersQuery.FieldByName('LIEFERSCHEIN_FORMULAR').AsString));
    end;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TConfigAufAblaufForm.AufVersTabSheetShow(Sender: TObject);

  procedure LoadFormular (ComboBox : TCustomComboBox; const FormularGroup : String);
  begin
    LoadPrintFormulare (ComboBox, FormularGroup);
    if (ComboBox.Items.Count = 0) then
      ComboBox.Enabled := false
    else begin
      ComboBox.Enabled := true;
      ComboBox.Items.Insert (0, '');
    end;
  end;

begin
  LoadLandCombobox (VersLandComboBox);

  LoadFormular (DeliverNoteFormComboBox, 'LIEFERSCHEIN');

  LoadFormular (ProformaFormComboBox, 'PROFORMA');

  LoadFormular (CN23FormComboBox, 'ZOLL_CN23');

  UpdateAufVersandQuery (Sender);
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TConfigAufAblaufForm.ConfigApplyButtonClick(Sender: TObject);
var
  res,
  prio    : Integer;
  opt,
  pdfpath,
  packstr : String;
begin
  res := 0;

  opt := '00000000000000';

  if not VorResCheckBox.Visible then
    opt [1] := '~'
  else if (VorResCheckBox.Checked) then
    opt [1] := '1';

  if not DetermineLTCheckBox.Visible then
    opt [2] := '~'
  else if (DetermineLTCheckBox.Checked) then
    opt [2] := '1';

  if not PartDeliveryCheckBox.Visible then
    opt [3] := '~'
  else if (PartDeliveryCheckBox.Checked) then
    opt [3] := '1';

  if not PrintInvoiceCheckBox.Visible then
    opt [4] := '~'
  else if (PrintInvoiceCheckBox.Checked) then
    opt [4] := '1';

  if not PrintProformaCheckBox.Visible then
    opt [5] := '~'
  else if (PrintProformaCheckBox.Checked) then
    opt [5] := '1';

  if not PrintCMRCheckBox.Visible then
    opt [6] := '~'
  else if (PrintCMRCheckBox.Checked) then
    opt [6] := '1';

  if not OnePackageCheckBox.Visible then
    opt [7] := '~'
  else if (OnePackageCheckBox.Checked) then
    opt [7] := '1';

  if not DeliveryNoteMailCheckBox.Visible then
    opt [8] := '~'
  else if (DeliveryNoteMailCheckBox.Checked) then
    opt [8] := '1';

  if not KonfAufCheckBox.Visible then
    opt [9] := '~'
  else if (KonfAufCheckBox.Checked) then
    opt [9] := '1';

  if not KonfBestCheckBox.Visible then
    opt [10] := '~'
  else if (KonfBestCheckBox.Checked) then
    opt [10] := '1';

  if (Length (PrioEdit.Text) = 0) then
    prio := -1
  else
    prio := PrioUpDown.Position;

  if not (PackTypeGroupComboBox.Visible and PackTypeGroupComboBox.Enabled) then
    packstr := ''
  else
    packstr := GetComboBoxDBItemWert (PackTypeGroupComboBox);

  if (fCfgRef > 0) then begin
    res := SetAuftragCfgData (fCfgRef, GetComboBoxRef(LTComboBox), GetComboBoxRef(SpedPrefComboBox), GetComboBoxRef(SpedDefaultComboBox), prio, SpedRoleEdit.Text, packstr);

    if (res = 0) then
      res := SetAuftragCfgOptions (fCfgRef, opt);

    if (res = 0) and ShipLabelComboBox.Visible then begin
      if (Length (BBDRemainingEdit.Text) = 0) then
        res := SetAuftragCfgFirstShipLabel (fCfgRef, -1)
      else
        res := SetAuftragCfgFirstShipLabel (fCfgRef, ShipLabelComboBox.ItemIndex);
    end;

    if (res = 0) and BBDRemainingEdit.Visible then
      res := SetAuftragCfgBBDRemaining (fCfgRef, BBDRemainingUpDown.Position);

    if (res = 0) and SpedRetComboBox.Visible then begin
      if (RetSendNrCheckBox.Visible) then begin
        if RetSendNrCheckBox.Checked then
          opt := '2'
        else if (Length (RetPDFPathEdit.Text) > 0) then
          opt := '1'
        else
          opt := '0'
      end;

      if (RetPDFPathEdit.Enabled) then
        pdfpath := RetPDFPathEdit.Text
      else
        pdfpath := '~';

      if (RetSendNrCheckBox.Visible) then
        res := SetAuftragCfgRetSpedLable (fCfgRef, GetComboBoxRef(SpedRetComboBox), SpedRetLandEdit.Text, pdfpath, opt)
      else
        res := SetAuftragCfgRetSpedLable (fCfgRef, GetComboBoxRef(SpedRetComboBox), SpedRetLandEdit.Text, pdfpath);
    end;
  end;

  if (res = 0) then
    AufCfgDBGrid.Reload (fCfgRef)
  else
    MessageDLG ('Fehler beim Ändern der Auftragesconfig' + #13 + #13 + LVSDatenModul.LastLVSErrorText, mtError, [mbOK], 0);
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 07.04.2024
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TConfigAufAblaufForm.ConfigTabSheetResize(Sender: TObject);
begin
   OptGroupBox.Width   := (ConfigDataGroupBox.ClientWidth - 16 - (OptGroupBox.Left * 2)) div 3;

   KonfGroupBox.Left   := OptGroupBox.Left + OptGroupBox.Width + 8;
   KonfGroupBox.Width  := OptGroupBox.Width;

   PrintGroupBox.Left  := KonfGroupBox.Left + KonfGroupBox.Width + 8;
   PrintGroupBox.Width := OptGroupBox.Width;

  Label5.Left         := SpedDefaultComboBox.Left;
  Label28.Left        := PackTypeGroupComboBox.Left;
  Label24.Left        := SpedRetComboBox.Left;
  Label25.Left        := RetPDFPathEdit.Left;
  Label29.Left        := ShipLabelComboBox.Left;
  Label21.Left        := BBDRemainingEdit.Left;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 06.12.2023
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TConfigAufAblaufForm.ConfigTabSheetShow(Sender: TObject);
begin
  fOldRefLager := -1;

  LoadComboxDBItems (PackTypeGroupComboBox, 'LT_TYPEN', 'LT_PACKING_GROUP');
  if (PackTypeGroupComboBox.Items.Count = 0) then
    PackTypeGroupComboBox.Enabled := false
  else
    PackTypeGroupComboBox.Items.Insert (0, '');

  ConfigTabSheetResize (Sender);

  UpdateAufConfigQuery (Sender);
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 06.12.2023
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TConfigAufAblaufForm.DefWALPCheckBoxClick(Sender: TObject);
begin
  SetWALPCheckBox.Enabled := not (DefWALPCheckBox.Enabled) or not DefWALPCheckBox.Checked;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 06.12.2023
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TConfigAufAblaufForm.EmptyCheckBoxClick(Sender: TObject);
begin
  AutoEmptyCheckBox.Enabled := EmptyCheckBox.Enabled and EmptyCheckBox.Checked;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TConfigAufAblaufForm.FormClose(Sender: TObject; var Action: TCloseAction);
begin
  AufAblaufQuery.Close;
  LVSConfigModul.SaveFormInfo (Self);
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TConfigAufAblaufForm.FormCreate(Sender: TObject);
begin
  if Assigned (DBGridUtils) then begin
    DBGridUtils.SetupDBGrids (Self);
    DBGridUtils.ConfigDBGrids (Self);
  end;

  fCfgRef := -1;
  fPlanRef := -1;

  LVSSprachModul.InitForm (Self);

  {$ifdef TranslateHelper}
    LVSSprachModul.SetNoTranslate (Self, NameEdit);
    LVSSprachModul.SetNoTranslate (Self, DescEdit);
    LVSSprachModul.SetNoTranslate (Self, SpedPrefComboBox);
    LVSSprachModul.SetNoTranslate (Self, SpedDefaultComboBox);
    LVSSprachModul.SetNoTranslate (Self, SpedRetComboBox);
    LVSSprachModul.SetNoTranslate (Self, WARelComboBox);
    LVSSprachModul.SetNoTranslate (Self, KommPlanGrpComboBox);
  {$endif}

  DialogPageControl.ActivePage := ConfigTabSheet;

  if not LVSDatenModul.ViewExits ('V_PCD_AUFTRAG_ART_VERSAND_CONFIG') then
    AufVersTabSheet.TabVisible := false
  else
    VersConfigPageControl.ActivePage := VersParamTabSheet;

  SubMandComboBox.Visible := LVSConfigModul.UseSubMandanten;

  LoadMandantCombobox (MandComboBox);
  MandComboBox.Items.Insert (0, rsAlle);
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TConfigAufAblaufForm.FormDestroy(Sender: TObject);
begin
  if Assigned (DBGridUtils) then
    DBGridUtils.StoreDBGrids (Self);

  ClearComboBoxObjects (LTComboBox);
  ClearComboBoxObjects (TAGroupComboBox);
  ClearComboBoxObjects (PackTypeGroupComboBox);
  ClearComboBoxObjects (KommArtComboBox);
  ClearComboBoxObjects (KommPlanCombobox);
  ClearComboBoxObjects (KommPlanGrpComboBox);
  ClearComboBoxObjects (KommPackComboBox);
  ClearComboBoxObjects (WAAblaufComboBox);
  ClearComboBoxObjects (WARelComboBox);
  ClearComboBoxObjects (WAPackplatzComboBox);
  ClearComboBoxObjects (WABufferComboBox);
  ClearComboBoxObjects (WACrossdockComboBox);
  ClearComboBoxObjects (WAUmpackComboBox);
  ClearComboBoxObjects (SpedPrefComboBox);
  ClearComboBoxObjects (SpedDefaultComboBox);
end;

procedure TConfigAufAblaufForm.FormResize(Sender: TObject);
begin
  MandComboBox.Width    := (ClientWidth - MandComboBox.Left * 2 - 16) div 2;
  SubMandComboBox.Left  := MandComboBox.Left + MandComboBox.Width + 16;
  SubMandComboBox.Width := MandComboBox.Width;
  SubMandLabel.Left := SubMandComboBox.Left;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TConfigAufAblaufForm.FormShow(Sender: TObject);
begin
  LVSConfigModul.RestoreFormInfo (Self);

  SubMandLabel.Visible := SubMandComboBox.Visible;

  if (LVSDatenModul.AktMandantRef <= 0) then
    MandComboBox.ItemIndex := 0
  else begin
    MandComboBox.ItemIndex := FindComboboxRef (MandComboBox, LVSDatenModul.AktMandantRef);
  end;

  MandComboBoxChange (Sender);
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TConfigAufAblaufForm.LHMCheckBoxClick(Sender: TObject);
begin
  AutoLHMCheckBox.Enabled := LHMCheckBox.Checked;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TConfigAufAblaufForm.LSPrintCheckBoxClick(Sender: TObject);
begin
  AutoLSPrintCheckBox.Enabled := LSPrintCheckBox.Checked;
  LSCopyEdit.Enabled := LSPrintCheckBox.Checked;
  LSCopyUpDown.Enabled := LSPrintCheckBox.Checked;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TConfigAufAblaufForm.MandComboBoxChange(Sender: TObject);
begin
  if not (SubMandComboBox.Visible) then
   SubMandComboBox.ItemIndex := -1
  else begin
    if (GetComboBoxRef (MandComboBox) <= 0) then begin
      ClearComboBoxObjects (SubMandComboBox);
      SubMandComboBox.Enabled := false;
      SubMandComboBox.ItemIndex := -1;
    end else begin
      LoadSubMandantCombobox (SubMandComboBox, LVSDatenModul.AktLocationRef, GetComboBoxRef (MandComboBox));

      if (SubMandComboBox.Items.Count = 0) then
        SubMandComboBox.Enabled := false
      else begin
        SubMandComboBox.Enabled := true;
        SubMandComboBox.Items.Insert (0, rsAlle);
        SubMandComboBox.ItemIndex := 0;
      end;
    end;
  end;

  SubMandComboBoxChange (Sender)
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 16.08.2025
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TConfigAufAblaufForm.VersNewMenuItemClick(Sender: TObject);
var
  selform : TSelectLagerMandantTraderForm;
begin
  selform := TSelectLagerMandantTraderForm.Create (Self);

  try
    if (selform.ShowModal = mrOk) then begin
    end;
  finally
    selform.Free;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TConfigAufAblaufForm.NVEInhaltPrintCheckBoxClick(Sender: TObject);
begin
  AutoNVEInhaltPrintCheckBox.Enabled := NVEInhaltPrintCheckBox.Checked;
  NVEinhaltLabelCheckBox.Enabled     := NVEInhaltPrintCheckBox.Checked;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TConfigAufAblaufForm.NVEPrintCheckBoxClick(Sender: TObject);
begin
  AutoNVEPrintCheckBox.Enabled := NVEPrintCheckBox.Checked;
  NVECopyEdit.Enabled := NVEPrintCheckBox.Checked;
  NVECopyUpDown.Enabled := NVEPrintCheckBox.Checked;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TConfigAufAblaufForm.PlanFullPALCheckBoxClick(Sender: TObject);
begin
  PALKommPlanBereichCheckBox.Enabled := PlanFullPALCheckBox.Checked;
  PALKommPlanZoneCheckBox.Enabled    := PlanFullPALCheckBox.Checked;
  PlanTeilPALCheckBox.Enabled        := PlanFullPALCheckBox.Checked;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TConfigAufAblaufForm.PlanTabSheetShow(Sender: TObject);
begin
  UpdateAufAblaufQuery (Sender);
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 10.08.2025
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TConfigAufAblaufForm.SpedRetComboBoxChange(Sender: TObject);
begin
  SpedRetLandEdit.Enabled := (GetComboBoxRef (SpedRetComboBox) > 0);
  RetPDFPathEdit.Enabled := SpedRetLandEdit.Enabled;
  RetSendNrCheckBox.Enabled := SpedRetLandEdit.Enabled;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 10.08.2025
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TConfigAufAblaufForm.SubMandComboBoxChange(Sender: TObject);
begin
  if (DialogPageControl.ActivePage = ConfigTabSheet) then
    UpdateAufConfigQuery (Sender)
  else if (DialogPageControl.ActivePage = PlanTabSheet) then
    UpdateAufAblaufQuery (Sender)
  else if (DialogPageControl.ActivePage = AufVersTabSheet) then
    UpdateAufVersandQuery (Sender);
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TConfigAufAblaufForm.PlanQuashButtonClick(Sender: TObject);
begin
  AufAblaufDataSourceDataChange (Sender, Nil);
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TConfigAufAblaufForm.ClearVersTabSheet;
begin
  IncotermEdit.Text := '';
  VATEdit.Text      := '';
  EORIEdit.Text     := '';
  ZollNrEdit.Text   := '';

  FiskalNameEdit.Text := '';
  FiskalNameAddEdit.Text := '';
  FiskalStreetEdit.Text := '';
  FiskalHouseNoEdit.Text := '';
  FiskalOrt.Text := '';
  FiskalPLZ.Text := '';
  FiskalLand.Text := '';
  FiskalMailEdit.Text := '';
  FiskalPhoneEdit.Text := '';
  FiskalVATEdit.Text    := '';
  FiskalEORIEdit.Text   := '';
  FiskalZollNrEdit.Text := '';

  DeliverNoteFormComboBox.ItemIndex := -1;
  DeliverNoteCheckBox.Checked       := False;
  DeliverNoteCopiesEdit.Enabled     := DeliverNoteCheckBox.Checked;
  DeliverNoteCopiesUpDown.Enabled   := DeliverNoteCheckBox.Checked;

  ProformaFormComboBox.ItemIndex    := -1;
  ProformaCheckBox.Checked          := False;
  ProformaCopiesEdit.Enabled        := ProformaCheckBox.Checked;
  ProformaCopiesUpDown.Enabled      := ProformaCheckBox.Checked;

  CN23FormComboBox.ItemIndex        := -1;
  CN23CheckBox.Checked              := False;
  CN23CopiesEdit.Enabled            := CN23CheckBox.Checked;
  CN23CopiesUpDown.Enabled          := CN23CheckBox.Checked;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function TConfigAufAblaufForm.UpdateAufAblaufQuery (Sender: TObject) : Integer;
begin
  fFirstFlag   := true;
  fOldRefLager := -1;

  AufAblaufQuery.Close;

  AufAblaufQuery.SQL.Clear;
  AufAblaufQuery.SQL.Add ('select * from V_PCD_AUFTRAG_ART_PLANUNG where STATUS=''ANG'' and ');

  if (SubMandComboBox.Visible and (GetComboBoxRef (SubMandComboBox) > 0)) then begin
    AufAblaufQuery.SQL.Add ('REF_SUB_MAND=:ref_sub_mand');
    AufAblaufQuery.Parameters.ParamByName('ref_sub_mand').Value := GetComboBoxRef (SubMandComboBox);
  end else if (GetComboBoxRef (MandComboBox) > 0) then begin
    AufAblaufQuery.SQL.Add ('REF_MAND in (select REF from V_PCD_MANDANT_LOCATION where REF_MASTER_MAND=:ref_mand)');
    AufAblaufQuery.Parameters.ParamByName('ref_mand').Value := GetComboBoxRef (MandComboBox);
  end else begin
    AufAblaufQuery.SQL.Add ('REF_MAND in (select REF from V_PCD_MANDANT_LOCATION where REF_LOCATION=:ref_loc_1)');
    AufAblaufQuery.Parameters.ParamByName('ref_loc_1').Value := LVSDatenModul.AktLocationRef;
  end;

  if (LVSDatenModul.AktLagerRef = -1) then begin
    AufAblaufQuery.SQL.Add ('and (REF_LAGER is null or REF_LAGER in (select REF_LAGER from V_LOCATION_REL_LAGER where REF_LOCATION=:ref_loc_2))');
    AufAblaufQuery.Parameters.ParamByName('ref_loc_2').Value := LVSDatenModul.AktLocationRef;
  end else begin
    AufAblaufQuery.SQL.Add ('and (REF_LAGER=:ref_lager)');
    AufAblaufQuery.Parameters.ParamByName('ref_lager').Value := LVSDatenModul.AktLagerRef;
  end;

  AufAblaufQuery.Open;

  AufAblaufDBGrid.SetColumnVisible('LAGER', Length(LVSDatenModul.AktLager) = 0);
  AufAblaufDBGrid.SetColumnVisible('MANDANT', Length(LVSDatenModul.AktMandant) = 0);

  AufAblaufDBGrid.SetColumnVisible('KOMM_ZONEN_PLANEN', false);
  AufAblaufDBGrid.SetColumnVisible('KOMM_LB_PLANEN', false);
  AufAblaufDBGrid.SetColumnVisible('PAL_ZONEN_PLANEN', false);
  AufAblaufDBGrid.SetColumnVisible('PAL_LB_PLANEN', false);
  AufAblaufDBGrid.SetColumnVisible('KOMM_LAUF_PLANUNG', false);

  AufAblaufDBGrid.SetColumnVisible('OPT_*', false);

  AufAblaufDBGrid.SetFocus;

  Result := 0;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 09.08.2025
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function TConfigAufAblaufForm.UpdateAufVersandQuery (Sender: TObject) : Integer;
begin
  AufVersQuery.Close;

  AufVersQuery.SQL.Clear;
  AufVersQuery.SQL.Add ('select * from V_PCD_AUFTRAG_ART_VERSAND_CONFIG where ');

  if (SubMandComboBox.Visible and (GetComboBoxRef (SubMandComboBox) > 0)) then begin
    AufVersQuery.SQL.Add ('REF_SUB_MAND=:ref_sub_mand');
    AufVersQuery.Parameters.ParamByName('ref_sub_mand').Value := GetComboBoxRef (SubMandComboBox);
  end else if (GetComboBoxRef (MandComboBox) > 0) then begin
    AufVersQuery.SQL.Add ('REF_MAND in (select REF from V_PCD_MANDANT_LOCATION where REF_MASTER_MAND=:ref_mand)');
    AufVersQuery.Parameters.ParamByName('ref_mand').Value := GetComboBoxRef (MandComboBox);
  end else begin
    AufVersQuery.SQL.Add ('REF_MAND in (select REF from V_PCD_MANDANT_LOCATION where REF_LOCATION=:ref_loc_1)');
    AufVersQuery.Parameters.ParamByName('ref_loc_1').Value := LVSDatenModul.AktLocationRef;
  end;

  if (LVSDatenModul.AktLagerRef = -1) then begin
    AufVersQuery.SQL.Add ('and (REF_LAGER is null or REF_LAGER in (select REF_LAGER from V_LOCATION_REL_LAGER where REF_LOCATION=:ref_loc_2))');
    AufVersQuery.Parameters.ParamByName('ref_loc_2').Value := LVSDatenModul.AktLocationRef;
  end else begin
    AufVersQuery.SQL.Add ('and (REF_LAGER is null or REF_LAGER=:ref_lager)');
    AufVersQuery.Parameters.ParamByName('ref_lager').Value := LVSDatenModul.AktLagerRef;
  end;

  AufVersQuery.Open;

  AufVersDBGrid.SetColumnVisible('LAGER', Length(LVSDatenModul.AktLager) = 0);
  AufVersDBGrid.SetColumnVisible('MANDANT', Length(LVSDatenModul.AktMandant) = 0);

  AufVersDBGrid.SetColumnVisible('OPT_*', false);
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function TConfigAufAblaufForm.UpdateAufConfigQuery (Sender: TObject) : Integer;
begin
  AufCfgQuery.Close;

  AufCfgQuery.SQL.Clear;
  AufCfgQuery.SQL.Add ('select * from V_PCD_AUFTRAG_ART_CONFIG where ');

  if (SubMandComboBox.Visible and (GetComboBoxRef (SubMandComboBox) > 0)) then begin
    AufCfgQuery.SQL.Add ('REF_SUB_MAND=:ref_sub_mand');
    AufCfgQuery.Parameters.ParamByName('ref_sub_mand').Value := GetComboBoxRef (SubMandComboBox);
  end else if (GetComboBoxRef (MandComboBox) > 0) then begin
    AufCfgQuery.SQL.Add ('REF_MAND in (select REF from V_PCD_MANDANT_LOCATION where REF_MASTER_MAND=:ref_mand)');
    AufCfgQuery.Parameters.ParamByName('ref_mand').Value := GetComboBoxRef (MandComboBox);
  end else begin
    AufCfgQuery.SQL.Add ('REF_MAND in (select REF from V_PCD_MANDANT_LOCATION where REF_LOCATION=:ref_loc_1)');
    AufCfgQuery.Parameters.ParamByName('ref_loc_1').Value := LVSDatenModul.AktLocationRef;
  end;

  if (LVSDatenModul.AktLagerRef = -1) then begin
    AufCfgQuery.SQL.Add ('and (REF_LAGER is null or REF_LAGER in (select REF_LAGER from V_LOCATION_REL_LAGER where REF_LOCATION=:ref_loc_2))');
    AufCfgQuery.Parameters.ParamByName('ref_loc_2').Value := LVSDatenModul.AktLocationRef;
  end else begin
    AufCfgQuery.SQL.Add ('and (REF_LAGER=:ref_lager)');
    AufCfgQuery.Parameters.ParamByName('ref_lager').Value := LVSDatenModul.AktLagerRef;
  end;

  AufCfgQuery.Open;

  AufCfgDBGrid.SetColumnVisible('LAGER', Length(LVSDatenModul.AktLager) = 0);
  AufCfgDBGrid.SetColumnVisible('MANDANT', Length(LVSDatenModul.AktMandant) = 0);

  AufCfgDBGrid.SetColumnVisible('OPT_*', false);

  AufCfgDBGrid.SetFocus;

  Result := 0;
end;

procedure TConfigAufAblaufForm.WAComboBoxChange(Sender: TObject);
begin
  //DefWALPCheckBox.Enabled := (WAPackplatzComboBox.Visible and (WAPackplatzComboBox.ItemIndex > 0)) or (WARelComboBox.Visible and (WARelComboBox.ItemIndex > 0));
end;

end.
