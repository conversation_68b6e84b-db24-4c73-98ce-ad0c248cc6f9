object EditMandantForm: TEditMandantForm
  Left = 428
  Top = 300
  BorderStyle = bsDialog
  Caption = 'EditMandantForm'
  ClientHeight = 671
  ClientWidth = 924
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'MS Sans Serif'
  Font.Style = []
  KeyPreview = True
  Position = poOwnerFormCenter
  OnCloseQuery = FormCloseQuery
  OnCreate = FormCreate
  OnDestroy = FormDestroy
  OnShow = FormShow
  DesignSize = (
    924
    671)
  TextHeight = 13
  object OkButton: TButton
    Left = 762
    Top = 639
    Width = 73
    Height = 24
    Margins.Left = 5
    Margins.Top = 5
    Margins.Right = 5
    Margins.Bottom = 5
    Anchors = [akRight, akBottom]
    Caption = #220'bernehmen'
    Default = True
    ModalResult = 1
    TabOrder = 0
  end
  object AbortButton: TButton
    Left = 842
    Top = 639
    Width = 73
    Height = 24
    Margins.Left = 5
    Margins.Top = 5
    Margins.Right = 5
    Margins.Bottom = 5
    Anchors = [akRight, akBottom]
    Cancel = True
    Caption = '&Abbrechen'
    ModalResult = 3
    TabOrder = 1
  end
  object PageControl1: TPageControl
    Left = 8
    Top = 8
    Width = 908
    Height = 625
    Margins.Left = 5
    Margins.Top = 5
    Margins.Right = 5
    Margins.Bottom = 5
    ActivePage = ConfigTabSheet
    Anchors = [akLeft, akTop, akRight, akBottom]
    TabOrder = 2
    object StammTabSheet: TTabSheet
      Margins.Left = 5
      Margins.Top = 5
      Margins.Right = 5
      Margins.Bottom = 5
      Caption = 'Stammdaten'
      DesignSize = (
        900
        597)
      object Label1: TLabel
        Left = 8
        Top = 8
        Width = 73
        Height = 13
        Margins.Left = 5
        Margins.Top = 5
        Margins.Right = 5
        Margins.Bottom = 5
        Caption = 'Mandant-Name'
      end
      object Label2: TLabel
        Left = 8
        Top = 55
        Width = 65
        Height = 13
        Margins.Left = 5
        Margins.Top = 5
        Margins.Right = 5
        Margins.Bottom = 5
        Caption = 'Beschreibung'
      end
      object Label3: TLabel
        Left = 8
        Top = 101
        Width = 125
        Height = 13
        Margins.Left = 5
        Margins.Top = 5
        Margins.Right = 5
        Margins.Bottom = 5
        Caption = 'GLN (ILN) des Mandanten'
      end
      object Label5: TLabel
        Left = 8
        Top = 296
        Width = 62
        Height = 13
        Margins.Left = 5
        Margins.Top = 5
        Margins.Right = 5
        Margins.Bottom = 5
        Caption = 'Adresszusatz'
      end
      object Label6: TLabel
        Left = 8
        Top = 337
        Width = 108
        Height = 13
        Margins.Left = 5
        Margins.Top = 5
        Margins.Right = 5
        Margins.Bottom = 5
        Caption = 'Strasse / Hausnummer'
      end
      object Label7: TLabel
        Left = 8
        Top = 378
        Width = 53
        Height = 13
        Margins.Left = 5
        Margins.Top = 5
        Margins.Right = 5
        Margins.Bottom = 5
        Caption = 'Postleitzahl'
      end
      object Label8: TLabel
        Left = 78
        Top = 379
        Width = 14
        Height = 13
        Margins.Left = 5
        Margins.Top = 5
        Margins.Right = 5
        Margins.Bottom = 5
        Caption = 'Ort'
      end
      object Bevel2: TBevel
        Left = 8
        Top = 288
        Width = 889
        Height = 2
        Margins.Left = 5
        Margins.Top = 5
        Margins.Right = 5
        Margins.Bottom = 5
        Anchors = [akLeft, akTop, akRight]
        Shape = bsTopLine
      end
      object Label9: TLabel
        Left = 810
        Top = 378
        Width = 24
        Height = 13
        Margins.Left = 5
        Margins.Top = 5
        Margins.Right = 5
        Margins.Bottom = 5
        Anchors = [akTop, akRight]
        Caption = 'Land'
      end
      object Label16: TLabel
        Left = 630
        Top = 101
        Width = 36
        Height = 13
        Margins.Left = 5
        Margins.Top = 5
        Margins.Right = 5
        Margins.Bottom = 5
        Anchors = [akTop, akRight]
        Caption = 'ERP-ID'
        ExplicitLeft = 636
      end
      object Label17: TLabel
        Left = 779
        Top = 101
        Width = 43
        Height = 13
        Margins.Left = 5
        Margins.Top = 5
        Margins.Right = 5
        Margins.Bottom = 5
        Anchors = [akTop, akRight]
        Caption = 'Daten-ID'
        ExplicitLeft = 785
      end
      object Label18: TLabel
        Left = 632
        Top = 8
        Width = 82
        Height = 13
        Margins.Left = 5
        Margins.Top = 5
        Margins.Right = 5
        Margins.Bottom = 5
        Anchors = [akTop, akRight]
        Caption = 'Kurzbezeichnung'
        ExplicitLeft = 641
      end
      object NameEdit: TEdit
        Left = 8
        Top = 29
        Width = 602
        Height = 21
        Margins.Left = 5
        Margins.Top = 5
        Margins.Right = 5
        Margins.Bottom = 5
        Anchors = [akLeft, akTop, akRight]
        MaxLength = 32
        TabOrder = 0
        Text = 'NameEdit'
      end
      object BeschreibungEdit: TEdit
        Left = 8
        Top = 70
        Width = 888
        Height = 21
        Margins.Left = 5
        Margins.Top = 5
        Margins.Right = 5
        Margins.Bottom = 5
        Anchors = [akLeft, akTop, akRight]
        MaxLength = 128
        TabOrder = 2
        Text = 'BeschreibungEdit'
      end
      object ILNEdit: TEdit
        Left = 8
        Top = 117
        Width = 289
        Height = 21
        Margins.Left = 5
        Margins.Top = 5
        Margins.Right = 5
        Margins.Bottom = 5
        Anchors = [akLeft, akTop, akRight]
        MaxLength = 16
        TabOrder = 3
        Text = 'ILNEdit'
      end
      object AdrEdit: TEdit
        Left = 8
        Top = 311
        Width = 888
        Height = 21
        Margins.Left = 5
        Margins.Top = 5
        Margins.Right = 5
        Margins.Bottom = 5
        Anchors = [akLeft, akTop, akRight]
        MaxLength = 64
        TabOrder = 7
        Text = 'AdrEdit'
      end
      object RoadEdit: TEdit
        Left = 8
        Top = 352
        Width = 888
        Height = 21
        Margins.Left = 5
        Margins.Top = 5
        Margins.Right = 5
        Margins.Bottom = 5
        Anchors = [akLeft, akTop, akRight]
        MaxLength = 64
        TabOrder = 8
        Text = 'Edit1'
      end
      object PLZEdit: TEdit
        Left = 8
        Top = 393
        Width = 64
        Height = 21
        Margins.Left = 5
        Margins.Top = 5
        Margins.Right = 5
        Margins.Bottom = 5
        MaxLength = 8
        TabOrder = 9
        Text = 'PLZEdit'
      end
      object OrtEdit: TEdit
        Left = 78
        Top = 393
        Width = 722
        Height = 21
        Margins.Left = 5
        Margins.Top = 5
        Margins.Right = 5
        Margins.Bottom = 5
        Anchors = [akLeft, akTop, akRight]
        MaxLength = 64
        TabOrder = 10
        Text = 'OrtEdit'
      end
      object LandEdit: TEdit
        Left = 807
        Top = 393
        Width = 89
        Height = 21
        Margins.Left = 5
        Margins.Top = 5
        Margins.Right = 5
        Margins.Bottom = 5
        Anchors = [akTop, akRight]
        MaxLength = 16
        TabOrder = 11
        Text = 'LandEdit'
      end
      object ERPIDEdit: TEdit
        Left = 630
        Top = 117
        Width = 117
        Height = 21
        Margins.Left = 5
        Margins.Top = 5
        Margins.Right = 5
        Margins.Bottom = 5
        Anchors = [akTop, akRight]
        MaxLength = 32
        TabOrder = 4
        Text = 'ERPIDEdit'
      end
      object DXFEdit: TEdit
        Left = 779
        Top = 117
        Width = 117
        Height = 21
        Margins.Left = 5
        Margins.Top = 5
        Margins.Right = 5
        Margins.Bottom = 5
        Anchors = [akTop, akRight]
        MaxLength = 32
        TabOrder = 5
        Text = 'DXFEdit'
      end
      object ShortNameEdit: TEdit
        Left = 630
        Top = 29
        Width = 117
        Height = 21
        Margins.Left = 5
        Margins.Top = 5
        Margins.Right = 5
        Margins.Bottom = 5
        Anchors = [akTop, akRight]
        MaxLength = 16
        TabOrder = 1
        Text = 'ShortNameEdit'
      end
      object PageControl2: TPageControl
        Left = 8
        Top = 429
        Width = 888
        Height = 165
        ActivePage = AdrTabSheet
        Anchors = [akLeft, akRight, akBottom]
        TabOrder = 12
        object AdrTabSheet: TTabSheet
          Caption = 'Adresse in der Niederlassung'
          ImageIndex = 1
          inline MandantAdressFrame: TMandAdrFrame
            Left = 0
            Top = 0
            Width = 880
            Height = 137
            Align = alClient
            TabOrder = 0
            ExplicitWidth = 880
            ExplicitHeight = 137
            inherited Label26: TLabel
              Width = 28
              Height = 13
              ExplicitWidth = 28
              ExplicitHeight = 13
            end
            inherited Label27: TLabel
              Width = 108
              Height = 13
              ExplicitWidth = 108
              ExplicitHeight = 13
            end
            inherited Label28: TLabel
              Width = 53
              Height = 13
              ExplicitWidth = 53
              ExplicitHeight = 13
            end
            inherited Label29: TLabel
              Width = 14
              Height = 13
              ExplicitWidth = 14
              ExplicitHeight = 13
            end
            inherited Label30: TLabel
              Left = 790
              Width = 24
              Height = 13
              ExplicitLeft = 793
              ExplicitWidth = 24
              ExplicitHeight = 13
            end
            inherited Label1: TLabel
              Width = 62
              Height = 13
              ExplicitWidth = 62
              ExplicitHeight = 13
            end
            inherited Label2: TLabel
              Width = 78
              Height = 13
              ExplicitWidth = 78
              ExplicitHeight = 13
            end
            inherited Label3: TLabel
              Width = 36
              Height = 13
              ExplicitWidth = 36
              ExplicitHeight = 13
            end
            inherited Label4: TLabel
              Width = 19
              Height = 13
              ExplicitWidth = 19
              ExplicitHeight = 13
            end
            inherited NameEdit: TEdit
              Height = 21
              ExplicitHeight = 21
            end
            inherited RoadEdit: TEdit
              Height = 21
              ExplicitHeight = 21
            end
            inherited PLZEdit: TEdit
              Height = 21
              ExplicitHeight = 21
            end
            inherited OrtEdit: TEdit
              Width = 305
              Height = 21
              ExplicitWidth = 305
              ExplicitHeight = 21
            end
            inherited LandEdit: TEdit
              Left = 790
              Height = 21
              ExplicitLeft = 790
              ExplicitHeight = 21
            end
            inherited NameAddEdit: TEdit
              Width = 398
              Height = 21
              ExplicitWidth = 398
              ExplicitHeight = 21
            end
            inherited ContactEdit: TEdit
              Width = 286
              Height = 21
              Anchors = [akLeft, akTop, akRight]
              ExplicitWidth = 286
              ExplicitHeight = 21
            end
            inherited PhoneEdit: TEdit
              Width = 141
              Height = 21
              Anchors = [akLeft, akTop, akRight]
              ExplicitWidth = 141
              ExplicitHeight = 21
            end
            inherited MailEdit: TEdit
              Width = 271
              Height = 21
              ExplicitWidth = 271
              ExplicitHeight = 21
            end
          end
        end
        object AbsTabSheet: TTabSheet
          Caption = 'Absender in der Niederlassung'
          inline MandantAbsenderFrame: TMandAdrFrame
            Left = 0
            Top = 0
            Width = 880
            Height = 137
            Align = alClient
            TabOrder = 0
            ExplicitWidth = 880
            ExplicitHeight = 137
            inherited Label26: TLabel
              Width = 28
              Height = 13
              ExplicitWidth = 28
              ExplicitHeight = 13
            end
            inherited Label27: TLabel
              Width = 108
              Height = 13
              ExplicitWidth = 108
              ExplicitHeight = 13
            end
            inherited Label28: TLabel
              Width = 53
              Height = 13
              ExplicitWidth = 53
              ExplicitHeight = 13
            end
            inherited Label29: TLabel
              Width = 14
              Height = 13
              ExplicitWidth = 14
              ExplicitHeight = 13
            end
            inherited Label30: TLabel
              Left = 790
              Width = 24
              Height = 13
              ExplicitLeft = 793
              ExplicitWidth = 24
              ExplicitHeight = 13
            end
            inherited Label1: TLabel
              Width = 62
              Height = 13
              ExplicitWidth = 62
              ExplicitHeight = 13
            end
            inherited Label2: TLabel
              Width = 78
              Height = 13
              ExplicitWidth = 78
              ExplicitHeight = 13
            end
            inherited Label3: TLabel
              Width = 36
              Height = 13
              ExplicitWidth = 36
              ExplicitHeight = 13
            end
            inherited Label4: TLabel
              Width = 19
              Height = 13
              ExplicitWidth = 19
              ExplicitHeight = 13
            end
            inherited NameEdit: TEdit
              Height = 21
              ExplicitHeight = 21
            end
            inherited RoadEdit: TEdit
              Height = 21
              ExplicitHeight = 21
            end
            inherited PLZEdit: TEdit
              Height = 21
              ExplicitHeight = 21
            end
            inherited OrtEdit: TEdit
              Width = 305
              Height = 21
              ExplicitWidth = 305
              ExplicitHeight = 21
            end
            inherited LandEdit: TEdit
              Left = 790
              Height = 21
              ExplicitLeft = 790
              ExplicitHeight = 21
            end
            inherited NameAddEdit: TEdit
              Width = 398
              Height = 21
              ExplicitWidth = 398
              ExplicitHeight = 21
            end
            inherited ContactEdit: TEdit
              Width = 286
              Height = 21
              Anchors = [akLeft, akTop, akRight]
              ExplicitWidth = 286
              ExplicitHeight = 21
            end
            inherited PhoneEdit: TEdit
              Width = 141
              Height = 21
              Anchors = [akLeft, akTop, akRight]
              ExplicitWidth = 141
              ExplicitHeight = 21
            end
            inherited MailEdit: TEdit
              Width = 262
              Height = 21
              ExplicitWidth = 262
              ExplicitHeight = 21
            end
          end
        end
      end
      object BesMandGroupBox: TGroupBox
        Left = 8
        Top = 146
        Width = 888
        Height = 134
        Caption = 'Alternativer Warenbestands-Mandant'
        TabOrder = 6
        object BesMandantCheckListBox: TCheckListBox
          Left = 12
          Top = 22
          Width = 500
          Height = 100
          ItemHeight = 13
          TabOrder = 0
        end
        object BesMandOnlyCheckBox: TCheckBox
          Left = 552
          Top = 24
          Width = 297
          Height = 17
          Caption = 'Nur aktive anzeigen'
          Checked = True
          State = cbChecked
          TabOrder = 1
          OnClick = BesMandOnlyCheckBoxClick
        end
      end
    end
    object ConfigTabSheet: TTabSheet
      Margins.Left = 5
      Margins.Top = 5
      Margins.Right = 5
      Margins.Bottom = 5
      Caption = 'Einstellungen'
      ImageIndex = 1
      DesignSize = (
        900
        597)
      object GroupBox1: TGroupBox
        Left = 5
        Top = 3
        Width = 896
        Height = 227
        Margins.Left = 5
        Margins.Top = 5
        Margins.Right = 5
        Margins.Bottom = 5
        Anchors = [akLeft, akTop, akRight]
        Caption = 'Angabe von Gr'#252'nden bei'
        TabOrder = 0
        DesignSize = (
          896
          227)
        object BesChangeGrundCheckBox: TCheckBox
          Left = 8
          Top = 40
          Width = 880
          Height = 16
          Margins.Left = 5
          Margins.Top = 5
          Margins.Right = 5
          Margins.Bottom = 5
          Anchors = [akLeft, akTop, akRight]
          Caption = 
            'Es muss bei einer Bestands'#228'nderungen immer einen Grund angegeben' +
            ' werden'
          TabOrder = 1
        end
        object BesDelGrundCheckBox: TCheckBox
          Left = 8
          Top = 58
          Width = 880
          Height = 16
          Margins.Left = 5
          Margins.Top = 5
          Margins.Right = 5
          Margins.Bottom = 5
          Anchors = [akLeft, akTop, akRight]
          Caption = 
            'Es muss beim L'#246'schen eines Bestandes immer einen Grund angegeben' +
            ' werden'
          TabOrder = 2
        end
        object BesCreateGrundCheckBox: TCheckBox
          Left = 8
          Top = 22
          Width = 880
          Height = 16
          Margins.Left = 5
          Margins.Top = 5
          Margins.Right = 5
          Margins.Bottom = 5
          Anchors = [akLeft, akTop, akRight]
          Caption = 
            'Es muss bei der manuellen Anlage es Bestandes immer einen Grund ' +
            'angegeben werden'
          TabOrder = 0
        end
        object BesLockGrundCheckBox: TCheckBox
          Left = 8
          Top = 76
          Width = 880
          Height = 16
          Margins.Left = 5
          Margins.Top = 5
          Margins.Right = 5
          Margins.Bottom = 5
          Anchors = [akLeft, akTop, akRight]
          Caption = 
            'Es muss bei einer Bestandssperren immer ein Sperr-ID angegeben w' +
            'erden'
          TabOrder = 3
        end
        object BesUnlockGrundCheckBox: TCheckBox
          Left = 8
          Top = 112
          Width = 880
          Height = 16
          Margins.Left = 5
          Margins.Top = 5
          Margins.Right = 5
          Margins.Bottom = 5
          Anchors = [akLeft, akTop, akRight]
          Caption = 
            'Es muss beim Entsperren eines Bestandes immer einen Grund angege' +
            'ben werden'
          TabOrder = 5
        end
        object BesQSGrundCheckBox: TCheckBox
          Left = 8
          Top = 130
          Width = 880
          Height = 16
          Margins.Left = 5
          Margins.Top = 5
          Margins.Right = 5
          Margins.Bottom = 5
          Anchors = [akLeft, akTop, akRight]
          Caption = 
            'Es muss beim Anlegen einer QS-Pr'#252'fung immer einen Grund angegebe' +
            'n werden'
          TabOrder = 6
        end
        object BesINVGrundCheckBox: TCheckBox
          Left = 8
          Top = 148
          Width = 880
          Height = 16
          Margins.Left = 5
          Margins.Top = 5
          Margins.Right = 5
          Margins.Bottom = 5
          Anchors = [akLeft, akTop, akRight]
          Caption = 
            'Es muss beim Anlegen einer Check-Inventur immer einen Grund ange' +
            'geben werden'
          TabOrder = 7
        end
        object AufDeleteGrundCheckBox: TCheckBox
          Left = 8
          Top = 166
          Width = 880
          Height = 16
          Margins.Left = 5
          Margins.Top = 5
          Margins.Right = 5
          Margins.Bottom = 5
          Anchors = [akLeft, akTop, akRight]
          Caption = 
            'Es muss beim L'#246'schen eines Auftrages immer einen Grund angegeben' +
            ' werden'
          TabOrder = 8
        end
        object AufStornoGrundCheckBox: TCheckBox
          Left = 8
          Top = 1184
          Width = 880
          Height = 16
          Margins.Left = 5
          Margins.Top = 5
          Margins.Right = 5
          Margins.Bottom = 5
          Anchors = [akLeft, akTop, akRight]
          Caption = 
            'Es muss beim Stornieren eines Auftrages immer einen Grund angege' +
            'ben werden'
          TabOrder = 9
        end
        object BesLockIDCheckBox: TCheckBox
          Left = 8
          Top = 94
          Width = 880
          Height = 16
          Margins.Left = 5
          Margins.Top = 5
          Margins.Right = 5
          Margins.Bottom = 5
          Anchors = [akLeft, akTop, akRight]
          Caption = 
            'Es muss bei einer Bestandssperren immer ein Sperr-ID angegeben w' +
            'erden'
          TabOrder = 4
        end
      end
      object OptionPageControl: TPageControl
        Left = 3
        Top = 240
        Width = 896
        Height = 352
        Margins.Left = 5
        Margins.Top = 5
        Margins.Right = 5
        Margins.Bottom = 5
        ActivePage = PackTabSheet
        Anchors = [akLeft, akTop, akRight, akBottom]
        TabOrder = 1
        object MandDatenTabSheet: TTabSheet
          Margins.Left = 5
          Margins.Top = 5
          Margins.Right = 5
          Margins.Bottom = 5
          Caption = 'Stammdaten'
          ImageIndex = 13
          DesignSize = (
            888
            324)
          object GroupBox3: TGroupBox
            Left = 5
            Top = 239
            Width = 883
            Height = 76
            Margins.Left = 5
            Margins.Top = 5
            Margins.Right = 5
            Margins.Bottom = 5
            Anchors = [akLeft, akRight, akBottom]
            Caption = 'Zahlungsinfos'
            TabOrder = 0
            object Label35: TLabel
              Left = 8
              Top = 51
              Width = 25
              Height = 13
              Margins.Left = 5
              Margins.Top = 5
              Margins.Right = 5
              Margins.Bottom = 5
              Caption = 'IBAN'
            end
            object Label36: TLabel
              Left = 339
              Top = 51
              Width = 17
              Height = 13
              Margins.Left = 5
              Margins.Top = 5
              Margins.Right = 5
              Margins.Bottom = 5
              Caption = 'BIC'
            end
            object Label37: TLabel
              Left = 8
              Top = 20
              Width = 25
              Height = 13
              Margins.Left = 5
              Margins.Top = 5
              Margins.Right = 5
              Margins.Bottom = 5
              Caption = 'Bank'
            end
            object StammIBANEdit: TEdit
              Left = 86
              Top = 48
              Width = 231
              Height = 21
              Margins.Left = 5
              Margins.Top = 5
              Margins.Right = 5
              Margins.Bottom = 5
              MaxLength = 32
              TabOrder = 1
              Text = 'StammIBANEdit'
            end
            object StammBICEdit: TEdit
              Left = 367
              Top = 48
              Width = 180
              Height = 21
              Margins.Left = 5
              Margins.Top = 5
              Margins.Right = 5
              Margins.Bottom = 5
              MaxLength = 16
              TabOrder = 2
              Text = 'StammBICEdit'
            end
            object StammBankEdit: TEdit
              Left = 86
              Top = 17
              Width = 231
              Height = 21
              Margins.Left = 5
              Margins.Top = 5
              Margins.Right = 5
              Margins.Bottom = 5
              TabOrder = 0
              Text = 'StammBankEdit'
            end
          end
          object GroupBox4: TGroupBox
            Left = 5
            Top = 5
            Width = 883
            Height = 155
            Margins.Left = 5
            Margins.Top = 5
            Margins.Right = 5
            Margins.Bottom = 5
            Anchors = [akLeft, akTop, akRight]
            Caption = 'Impressum'
            TabOrder = 1
            object Label31: TLabel
              Left = 12
              Top = 21
              Width = 75
              Height = 13
              Margins.Left = 5
              Margins.Top = 5
              Margins.Right = 5
              Margins.Bottom = 5
              Caption = 'Gesch'#228'ftsf'#252'hrer'
            end
            object Label32: TLabel
              Left = 12
              Top = 48
              Width = 22
              Height = 13
              Margins.Left = 5
              Margins.Top = 5
              Margins.Right = 5
              Margins.Bottom = 5
              Caption = 'URL'
            end
            object Label34: TLabel
              Left = 12
              Top = 79
              Width = 43
              Height = 13
              Margins.Left = 5
              Margins.Top = 5
              Margins.Right = 5
              Margins.Bottom = 5
              Caption = 'Steuernr.'
            end
            object Label38: TLabel
              Left = 314
              Top = 20
              Width = 78
              Height = 13
              Margins.Left = 5
              Margins.Top = 5
              Margins.Right = 5
              Margins.Bottom = 5
              Alignment = taRightJustify
              Caption = 'Ansprechpartner'
            end
            object Label39: TLabel
              Left = 564
              Top = 21
              Width = 60
              Height = 13
              Margins.Left = 5
              Margins.Top = 5
              Margins.Right = 5
              Margins.Bottom = 5
              Alignment = taRightJustify
              Caption = 'Mail-Adresse'
            end
            object Label40: TLabel
              Left = 358
              Top = 77
              Width = 35
              Height = 13
              Margins.Left = 5
              Margins.Top = 5
              Margins.Right = 5
              Margins.Bottom = 5
              Caption = 'VAT-ID'
            end
            object Label41: TLabel
              Left = 564
              Top = 79
              Width = 62
              Height = 13
              Margins.Left = 5
              Margins.Top = 5
              Margins.Right = 5
              Margins.Bottom = 5
              Caption = 'HR-Angaben'
            end
            object Label42: TLabel
              Left = 600
              Top = 47
              Width = 24
              Height = 13
              Margins.Left = 5
              Margins.Top = 5
              Margins.Right = 5
              Margins.Bottom = 5
              Alignment = taRightJustify
              Caption = 'Logo'
            end
            object Label33: TLabel
              Left = 358
              Top = 109
              Width = 26
              Height = 13
              Margins.Left = 5
              Margins.Top = 5
              Margins.Right = 5
              Margins.Bottom = 5
              Caption = 'EORI'
            end
            object Label26: TLabel
              Left = 12
              Top = 109
              Width = 44
              Height = 13
              Margins.Left = 5
              Margins.Top = 5
              Margins.Right = 5
              Margins.Bottom = 5
              Caption = 'W'#228'hrung'
            end
            object StammGFEdit: TEdit
              Left = 98
              Top = 18
              Width = 192
              Height = 21
              Margins.Left = 5
              Margins.Top = 5
              Margins.Right = 5
              Margins.Bottom = 5
              MaxLength = 64
              TabOrder = 0
              Text = 'StammGFEdit'
            end
            object StammURLEdit: TEdit
              Left = 98
              Top = 49
              Width = 449
              Height = 21
              Margins.Left = 5
              Margins.Top = 5
              Margins.Right = 5
              Margins.Bottom = 5
              MaxLength = 64
              TabOrder = 3
              Text = 'StammURLEdit'
            end
            object StammTaxNrEdit: TEdit
              Left = 98
              Top = 76
              Width = 192
              Height = 21
              Margins.Left = 5
              Margins.Top = 5
              Margins.Right = 5
              Margins.Bottom = 5
              MaxLength = 64
              TabOrder = 6
              Text = 'StammTaxNrEdit'
            end
            object StammContactEdit: TEdit
              Left = 402
              Top = 18
              Width = 145
              Height = 21
              Margins.Left = 5
              Margins.Top = 5
              Margins.Right = 5
              Margins.Bottom = 5
              MaxLength = 64
              TabOrder = 1
              Text = 'StammContactEdit'
            end
            object StammMailEdit: TEdit
              Left = 632
              Top = 18
              Width = 234
              Height = 21
              Margins.Left = 5
              Margins.Top = 5
              Margins.Right = 5
              Margins.Bottom = 5
              MaxLength = 64
              TabOrder = 2
              Text = 'StammMailEdit'
            end
            object StammVATEdit: TEdit
              Left = 402
              Top = 76
              Width = 145
              Height = 21
              Margins.Left = 5
              Margins.Top = 5
              Margins.Right = 5
              Margins.Bottom = 5
              MaxLength = 64
              TabOrder = 7
              Text = 'StammVATEdit'
            end
            object StammHREdit: TEdit
              Left = 632
              Top = 76
              Width = 235
              Height = 21
              Margins.Left = 5
              Margins.Top = 5
              Margins.Right = 5
              Margins.Bottom = 5
              MaxLength = 64
              TabOrder = 9
              Text = 'StammHREdit'
            end
            object StammLogoEdit: TEdit
              Left = 632
              Top = 44
              Width = 177
              Height = 21
              Margins.Left = 5
              Margins.Top = 5
              Margins.Right = 5
              Margins.Bottom = 5
              MaxLength = 64
              ReadOnly = True
              TabOrder = 4
              Text = 'StammLogoEdit'
            end
            object StammLogoButton: TButton
              Left = 815
              Top = 42
              Width = 51
              Height = 24
              Margins.Left = 5
              Margins.Top = 5
              Margins.Right = 5
              Margins.Bottom = 5
              Caption = '...'
              TabOrder = 5
              OnClick = StammLogoButtonClick
            end
            object StammEORIEdit: TEdit
              Left = 402
              Top = 107
              Width = 145
              Height = 21
              Margins.Left = 5
              Margins.Top = 5
              Margins.Right = 5
              Margins.Bottom = 5
              MaxLength = 32
              TabOrder = 10
              Text = 'StammEORIEdit'
            end
            object CurrencyComboBox: TComboBoxPro
              Left = 98
              Top = 107
              Width = 192
              Height = 22
              Style = csOwnerDrawFixed
              ColWidth = 48
              ColumeCount = 3
              ItemIndex = 0
              TabOrder = 8
              Items.Strings = (
                ''
                'EUR||Euro'
                'SFr||Schweizer Franken')
            end
          end
        end
        object ArtikelTabSheet: TTabSheet
          Margins.Left = 5
          Margins.Top = 5
          Margins.Right = 5
          Margins.Bottom = 5
          Caption = 'Artikel'
          ImageIndex = 6
          object Label10: TLabel
            Left = 8
            Top = 44
            Width = 116
            Height = 13
            Margins.Left = 5
            Margins.Top = 5
            Margins.Right = 5
            Margins.Bottom = 5
            Caption = 'Suffix f'#252'r Artikelnummern'
          end
          object Label11: TLabel
            Left = 8
            Top = 93
            Width = 66
            Height = 13
            Margins.Left = 5
            Margins.Top = 5
            Margins.Right = 5
            Margins.Bottom = 5
            Caption = 'Erste Nummer'
          end
          object Label13: TLabel
            Left = 101
            Top = 93
            Width = 68
            Height = 13
            Margins.Left = 5
            Margins.Top = 5
            Margins.Right = 5
            Margins.Bottom = 5
            Caption = 'Letze Nummer'
          end
          object Label14: TLabel
            Left = 197
            Top = 93
            Width = 62
            Height = 13
            Margins.Left = 5
            Margins.Top = 5
            Margins.Right = 5
            Margins.Bottom = 5
            Caption = 'Gesamtl'#228'nge'
          end
          object Label15: TLabel
            Left = 156
            Top = 44
            Width = 82
            Height = 13
            Margins.Left = 5
            Margins.Top = 5
            Margins.Right = 5
            Margins.Bottom = 5
            Caption = 'N'#228'chste Nummer'
          end
          object Label4: TLabel
            Left = 8
            Top = 237
            Width = 85
            Height = 13
            Caption = 'Prefix f'#252'r Artikelnr.'
          end
          object ArtikelStartEdit: TEdit
            Left = 8
            Top = 109
            Width = 79
            Height = 21
            Margins.Left = 5
            Margins.Top = 5
            Margins.Right = 5
            Margins.Bottom = 5
            MaxLength = 5
            TabOrder = 3
            Text = 'ArtikelStartEdit'
            OnKeyPress = ArtikelNrEditKeyPress
          end
          object ArtikelEndEdit: TEdit
            Left = 101
            Top = 109
            Width = 80
            Height = 21
            Margins.Left = 5
            Margins.Top = 5
            Margins.Right = 5
            Margins.Bottom = 5
            MaxLength = 5
            TabOrder = 4
            Text = 'ArtikelEndEdit'
          end
          object ArtikelSuffixEdit: TEdit
            Left = 8
            Top = 60
            Width = 118
            Height = 21
            Margins.Left = 5
            Margins.Top = 5
            Margins.Right = 5
            Margins.Bottom = 5
            MaxLength = 16
            TabOrder = 1
            Text = 'ArtikelSuffixEdit'
          end
          object ArtikelLenEdit: TEdit
            Left = 197
            Top = 109
            Width = 54
            Height = 21
            Margins.Left = 5
            Margins.Top = 5
            Margins.Right = 5
            Margins.Bottom = 5
            MaxLength = 2
            TabOrder = 5
            Text = 'ArtikelLenEdit'
          end
          object ArtikelNextEdit: TEdit
            Left = 156
            Top = 60
            Width = 95
            Height = 21
            Margins.Left = 5
            Margins.Top = 5
            Margins.Right = 5
            Margins.Bottom = 5
            MaxLength = 5
            TabOrder = 2
            Text = 'ArtikelNextEdit'
          end
          object UseInternalArtikelNrCheckBox: TCheckBox
            Left = 8
            Top = 12
            Width = 290
            Height = 17
            Margins.Left = 5
            Margins.Top = 5
            Margins.Right = 5
            Margins.Bottom = 5
            Caption = 'Interne Arikelnummer erzeugen'
            TabOrder = 0
          end
          object IFCStammdatenUpdateCheckBox: TCheckBox
            Left = 8
            Top = 164
            Width = 668
            Height = 17
            Margins.Left = 5
            Margins.Top = 5
            Margins.Right = 5
            Margins.Bottom = 5
            Caption = 'Lagerstammdaten nicht per Schnittstelle aktualisierbar'
            Checked = True
            State = cbChecked
            TabOrder = 6
          end
          object ArContenIntCheckBox: TCheckBox
            Left = 8
            Top = 202
            Width = 668
            Height = 16
            Margins.Left = 5
            Margins.Top = 5
            Margins.Right = 5
            Margins.Bottom = 5
            Caption = 'Inhaltsmengen mit Nenner/Z'#228'hler angeben'
            TabOrder = 7
          end
          object ArtikelPrefixEdit: TEdit
            Left = 8
            Top = 256
            Width = 121
            Height = 21
            MaxLength = 16
            TabOrder = 8
            Text = 'ArtikelPrefixEdit'
          end
        end
        object BestandTabSheet: TTabSheet
          Margins.Left = 5
          Margins.Top = 5
          Margins.Right = 5
          Margins.Bottom = 5
          Caption = 'Bestandsverwaltung'
          ImageIndex = 7
          object UniqueChargenCheckBox: TCheckBox
            Left = 8
            Top = 12
            Width = 274
            Height = 17
            Margins.Left = 5
            Margins.Top = 5
            Margins.Right = 5
            Margins.Bottom = 5
            Caption = 'Eindeutige Chargen '#252'ber alle L'#228'ger'
            TabOrder = 0
          end
          object BestandBelegCheckBox: TCheckBox
            Left = 8
            Top = 52
            Width = 498
            Height = 16
            Margins.Left = 5
            Margins.Top = 5
            Margins.Right = 5
            Margins.Bottom = 5
            Caption = 'F'#252'r alle manuellen '#196'nderungen einen Beleg erzeugen'
            TabOrder = 1
          end
        end
        object BestTabSheet: TTabSheet
          Margins.Left = 5
          Margins.Top = 5
          Margins.Right = 5
          Margins.Bottom = 5
          Caption = 'Warenerwartung'
          ImageIndex = 9
          object BestNrUniqueCheckBox: TCheckBox
            Left = 8
            Top = 51
            Width = 473
            Height = 17
            Margins.Left = 5
            Margins.Top = 5
            Margins.Right = 5
            Margins.Bottom = 5
            Caption = 'Bestellnummer muss eindeutig sein'
            TabOrder = 0
          end
          object BestEnabledCheckBox: TCheckBox
            Left = 8
            Top = 12
            Width = 473
            Height = 17
            Margins.Left = 5
            Margins.Top = 5
            Margins.Right = 5
            Margins.Bottom = 5
            Caption = 'Das Anlegen von Warenerwartungen ist zul'#228'ssig'
            TabOrder = 1
          end
        end
        object AufTabSheet: TTabSheet
          Margins.Left = 5
          Margins.Top = 5
          Margins.Right = 5
          Margins.Bottom = 5
          Caption = 'Auftr'#228'ge'
          ImageIndex = 10
          object Label45: TLabel
            Left = 8
            Top = 148
            Width = 113
            Height = 13
            Caption = 'Verpackungsart-Gruppe'
          end
          object AufEnabledCheckBox: TCheckBox
            Left = 8
            Top = 12
            Width = 353
            Height = 17
            Margins.Left = 5
            Margins.Top = 5
            Margins.Right = 5
            Margins.Bottom = 5
            Caption = 'Das Anlegen von Auftr'#228'gen ist zul'#228'ssig'
            TabOrder = 0
          end
          object AufEmpfSelectCheckBox: TCheckBox
            Left = 8
            Top = 33
            Width = 353
            Height = 16
            Margins.Left = 5
            Margins.Top = 5
            Margins.Right = 5
            Margins.Bottom = 5
            Caption = 'Auswahl der Warenempf'#228'nger immer anzeigen'
            TabOrder = 1
            Visible = False
          end
          object AufVorResCheckBox: TCheckBox
            Left = 8
            Top = 54
            Width = 353
            Height = 16
            Margins.Left = 5
            Margins.Top = 5
            Margins.Right = 5
            Margins.Bottom = 5
            Caption = 'Bestands-Vorreservierung f'#252'r jeden Auftrag aktivieren'
            TabOrder = 2
            Visible = False
          end
          object AufPrioGroupBox: TGroupBox
            Left = 8
            Top = 213
            Width = 279
            Height = 100
            Margins.Left = 5
            Margins.Top = 5
            Margins.Right = 5
            Margins.Bottom = 5
            Caption = 'Prios '#252'ber die Schnittstelle'
            TabOrder = 9
            Visible = False
            object Label44: TLabel
              Left = 16
              Top = 46
              Width = 107
              Height = 13
              Margins.Left = 5
              Margins.Top = 5
              Margins.Right = 5
              Margins.Bottom = 5
              Caption = 'H'#246'chste zul'#228'ssige Prio'
            end
            object IFCPrioCheckBox: TCheckBox
              Left = 16
              Top = 23
              Width = 196
              Height = 17
              Margins.Left = 5
              Margins.Top = 5
              Margins.Right = 5
              Margins.Bottom = 5
              Caption = 'Prios werden '#252'bernommen'
              TabOrder = 0
              Visible = False
            end
            object MaxPrioEdit: TEdit
              Left = 16
              Top = 61
              Width = 118
              Height = 21
              Margins.Left = 5
              Margins.Top = 5
              Margins.Right = 5
              Margins.Bottom = 5
              TabOrder = 1
              Text = 'MaxPrioEdit'
            end
          end
          object PackTypeGroupComboBox: TComboBoxPro
            Left = 8
            Top = 165
            Width = 279
            Height = 22
            Style = csOwnerDrawFixed
            TabOrder = 8
          end
          object AufCheckAdrCheckBox: TCheckBox
            Left = 384
            Top = 9
            Width = 393
            Height = 17
            Caption = 'Automatisch Adresspr'#252'fung'
            TabOrder = 4
            OnClick = AufCheckAdrCheckBoxClick
          end
          object AufAdrCorrCityCheckBox: TCheckBox
            Left = 400
            Top = 46
            Width = 393
            Height = 17
            Caption = 'Automatisch Korrektur des Ortes'
            TabOrder = 6
          end
          object AufAdrCorrStrCheckBox: TCheckBox
            Left = 400
            Top = 65
            Width = 393
            Height = 17
            Caption = 'Automatisch Korrektur Strasse'
            TabOrder = 7
          end
          object AufAdrCorrCheckBox: TCheckBox
            Left = 400
            Top = 27
            Width = 393
            Height = 17
            Caption = 'Automatisch Korrektur von Schreibweisen'
            TabOrder = 5
            OnClick = AufAdrCorrCheckBoxClick
          end
          object LieferAdrCheckBox: TCheckBox
            Left = 8
            Top = 88
            Width = 289
            Height = 17
            Caption = 'Lieferanschrift immer '#252'bernehmen'
            TabOrder = 3
          end
        end
        object WETabSheet: TTabSheet
          Margins.Left = 5
          Margins.Top = 5
          Margins.Right = 5
          Margins.Bottom = 5
          Caption = 'Wareneingang'
          ImageIndex = 3
          DesignSize = (
            888
            324)
          object Label46: TLabel
            Left = 528
            Top = 235
            Width = 204
            Height = 13
            Caption = 'Lieferdatum (maximale Tage in der Zukunft)'
          end
          object WEPicCheckBox: TCheckBox
            Left = 8
            Top = 10
            Width = 422
            Height = 17
            Margins.Left = 5
            Margins.Top = 5
            Margins.Right = 5
            Margins.Bottom = 5
            Caption = 'Artikelbilder im WE'
            TabOrder = 0
          end
          object WELastMHDCheckBox: TCheckBox
            Left = 8
            Top = 27
            Width = 422
            Height = 17
            Margins.Left = 5
            Margins.Top = 5
            Margins.Right = 5
            Margins.Bottom = 5
            Caption = 
              'Pr'#252'fen, ob MHD geringer ist, als das bei der letzten Warenannahm' +
              'e '
            TabOrder = 2
          end
          object WEQSGrundCheckBox: TCheckBox
            Left = 8
            Top = 44
            Width = 422
            Height = 17
            Margins.Left = 5
            Margins.Top = 5
            Margins.Right = 5
            Margins.Bottom = 5
            Caption = 'QS-Auswahl bei der WE-Erfassung'
            TabOrder = 4
          end
          object WEArEinheitOptCheckBox: TCheckBox
            Left = 8
            Top = 61
            Width = 422
            Height = 17
            Margins.Left = 5
            Margins.Top = 5
            Margins.Right = 5
            Margins.Bottom = 5
            Caption = 'Sperrgut bzw. Big Item im WE festlegen'
            TabOrder = 5
          end
          object WEBedarfCheckBox: TCheckBox
            Left = 3
            Top = 254
            Width = 157
            Height = 17
            Margins.Left = 5
            Margins.Top = 5
            Margins.Right = 5
            Margins.Bottom = 5
            Caption = 'Bedarfsmenge anzeigen'
            TabOrder = 18
          end
          object WEKommBestandCheckBox: TCheckBox
            Left = 179
            Top = 254
            Width = 158
            Height = 17
            Margins.Left = 5
            Margins.Top = 5
            Margins.Right = 5
            Margins.Bottom = 5
            Caption = 'Kommbestand anzeigen'
            TabOrder = 19
          end
          object WENachsBestandCheckBox: TCheckBox
            Left = 355
            Top = 254
            Width = 157
            Height = 17
            Margins.Left = 5
            Margins.Top = 5
            Margins.Right = 5
            Margins.Bottom = 5
            Caption = 'Nachschubbestand anzeigen'
            TabOrder = 20
          end
          object WELeerProPosCheckBox: TCheckBox
            Left = 8
            Top = 78
            Width = 422
            Height = 16
            Margins.Left = 5
            Margins.Top = 5
            Margins.Right = 5
            Margins.Bottom = 5
            Caption = 'Leergut pro WE-Position erfassen'
            TabOrder = 6
          end
          object WEArtikelDatenCheckBox: TCheckBox
            Left = 8
            Top = 175
            Width = 617
            Height = 17
            Margins.Left = 5
            Margins.Top = 5
            Margins.Right = 5
            Margins.Bottom = 5
            Caption = 'Wenn notwenig  beim WE die Artikelstammdaten abfragen'
            TabOrder = 10
          end
          object WEArAbmessungCheckBox: TCheckBox
            Left = 23
            Top = 198
            Width = 147
            Height = 16
            Margins.Left = 5
            Margins.Top = 5
            Margins.Right = 5
            Margins.Bottom = 5
            Caption = 'Abmessung und Gewicht'
            TabOrder = 11
          end
          object WEArAttrCheckBox: TCheckBox
            Left = 343
            Top = 198
            Width = 146
            Height = 16
            Margins.Left = 5
            Margins.Top = 5
            Margins.Right = 5
            Margins.Bottom = 5
            Caption = 'Atrribute'
            TabOrder = 13
          end
          object WEArBarcodeCheckBox: TCheckBox
            Left = 503
            Top = 198
            Width = 146
            Height = 16
            Margins.Left = 5
            Margins.Top = 5
            Margins.Right = 5
            Margins.Bottom = 5
            Caption = 'EAN und Barcode'
            TabOrder = 14
          end
          object WEArShipperCheckBox: TCheckBox
            Left = 660
            Top = 198
            Width = 147
            Height = 16
            Margins.Left = 5
            Margins.Top = 5
            Margins.Right = 5
            Margins.Bottom = 5
            Caption = 'Versender'
            TabOrder = 15
          end
          object WEArPalDatenCheckBox: TCheckBox
            Left = 179
            Top = 198
            Width = 147
            Height = 16
            Margins.Left = 5
            Margins.Top = 5
            Margins.Right = 5
            Margins.Bottom = 5
            Caption = 'Palettendaten'
            TabOrder = 12
          end
          object WEArGefahrstoffeCheckBox: TCheckBox
            Left = 23
            Top = 219
            Width = 147
            Height = 17
            Margins.Left = 5
            Margins.Top = 5
            Margins.Right = 5
            Margins.Bottom = 5
            Caption = 'Gefahrstoffe'
            TabOrder = 16
          end
          object WEArKlasseCheckBox: TCheckBox
            Left = 179
            Top = 220
            Width = 147
            Height = 17
            Margins.Left = 5
            Margins.Top = 5
            Margins.Right = 5
            Margins.Bottom = 5
            Caption = 'Artikel-Klasse'
            TabOrder = 17
          end
          object WEMehrMengeCheckBox: TCheckBox
            Left = 8
            Top = 112
            Width = 422
            Height = 17
            Margins.Left = 5
            Margins.Top = 5
            Margins.Right = 5
            Margins.Bottom = 5
            Caption = 'Mehrmengen im WE erlaubt'
            TabOrder = 7
          end
          object WEBeschaffCheckBox: TCheckBox
            Left = 452
            Top = 10
            Width = 423
            Height = 17
            Margins.Left = 5
            Margins.Top = 5
            Margins.Right = 5
            Margins.Bottom = 5
            Caption = 'Nur Beschaffungsartikel annehmen'
            TabOrder = 1
          end
          object WEBBDPastCheckBox: TCheckBox
            Left = 452
            Top = 29
            Width = 423
            Height = 17
            Margins.Left = 5
            Margins.Top = 5
            Margins.Right = 5
            Margins.Bottom = 5
            Caption = 'MHDs aus der Vergangenheit annehmen'
            TabOrder = 3
          end
          object WEDutyGroupBox: TGroupBox
            Left = 452
            Top = 60
            Width = 419
            Height = 130
            Anchors = [akLeft, akTop, akRight]
            Caption = 'Pflichtangaben im WE'
            TabOrder = 9
            object WESuppDutyCheckBox: TCheckBox
              Left = 14
              Top = 58
              Width = 250
              Height = 17
              Margins.Left = 5
              Margins.Top = 5
              Margins.Right = 5
              Margins.Bottom = 5
              Caption = 'Lieferant ist Pflicht'
              TabOrder = 2
            end
            object WEKFZDutyCheckBox: TCheckBox
              Left = 14
              Top = 39
              Width = 250
              Height = 17
              Margins.Left = 5
              Margins.Top = 5
              Margins.Right = 5
              Margins.Bottom = 5
              Caption = 'Fahrzeugkennzeichen ist Pflicht'
              TabOrder = 1
            end
            object WELSNoDutyCheckBox: TCheckBox
              Left = 14
              Top = 20
              Width = 250
              Height = 17
              Margins.Left = 5
              Margins.Top = 5
              Margins.Right = 5
              Margins.Bottom = 5
              Caption = 'Lieferscheinnummer ist Pflicht'
              TabOrder = 0
            end
            object WEPackTypeDutyCheckBox: TCheckBox
              Left = 14
              Top = 77
              Width = 250
              Height = 17
              Margins.Left = 5
              Margins.Top = 5
              Margins.Right = 5
              Margins.Bottom = 5
              Caption = 'Verpackungsart ist Pflicht'
              TabOrder = 3
            end
            object WEAnzPackStCheckBox: TCheckBox
              Left = 14
              Top = 96
              Width = 250
              Height = 17
              Margins.Left = 5
              Margins.Top = 5
              Margins.Right = 5
              Margins.Bottom = 5
              Caption = 'Anzahl Packst'#252'cke ist Pflicht'
              TabOrder = 4
            end
          end
          object WECategoryCheckBox: TCheckBox
            Left = 8
            Top = 130
            Width = 422
            Height = 17
            Margins.Left = 5
            Margins.Top = 5
            Margins.Right = 5
            Margins.Bottom = 5
            Caption = 'Bestandskategorie im WE festlegen'
            TabOrder = 8
          end
          object LSDatumMaxNumberBox: TNumberBox
            Left = 528
            Top = 254
            Width = 113
            Height = 21
            TabOrder = 21
          end
          object WEUseDefaultLTCheckBox: TCheckBox
            Left = 8
            Top = 95
            Width = 422
            Height = 17
            Margins.Left = 5
            Margins.Top = 5
            Margins.Right = 5
            Margins.Bottom = 5
            Caption = 'Standard-LT aus Stammdaten automatisch '#252'bernehmen'
            TabOrder = 22
          end
        end
        object LiefRetTabSheet: TTabSheet
          Margins.Left = 5
          Margins.Top = 5
          Margins.Right = 5
          Margins.Bottom = 5
          Caption = 'R'#252'cksendungen'
          ImageIndex = 2
          DesignSize = (
            888
            324)
          object Label21: TLabel
            Left = 263
            Top = 129
            Width = 140
            Height = 13
            Margins.Left = 5
            Margins.Top = 5
            Margins.Right = 5
            Margins.Bottom = 5
            Caption = 'Beim Erfassen der Positionen:'
          end
          object Label22: TLabel
            Left = 8
            Top = 129
            Width = 127
            Height = 13
            Margins.Left = 5
            Margins.Top = 5
            Margins.Right = 5
            Margins.Bottom = 5
            Caption = 'Beim Anlegen der Retoure:'
          end
          object LiefRetBesCheckBox: TCheckBox
            Left = 8
            Top = 8
            Width = 350
            Height = 17
            Margins.Left = 5
            Margins.Top = 5
            Margins.Right = 5
            Margins.Bottom = 5
            Anchors = [akLeft, akTop, akRight]
            Caption = 'Lieferantenretouren mit Bestandsanpassungen'
            TabOrder = 0
          end
          object LiefRetWECheckBox: TCheckBox
            Left = 8
            Top = 29
            Width = 350
            Height = 17
            Margins.Left = 5
            Margins.Top = 5
            Margins.Right = 5
            Margins.Bottom = 5
            Anchors = [akLeft, akTop, akRight]
            Caption = 'WE-Kontrolle bei Lieferantenretouren'
            TabOrder = 1
          end
          object RetQSGrundCheckBox: TCheckBox
            Left = 265
            Top = 211
            Width = 257
            Height = 17
            Margins.Left = 5
            Margins.Top = 5
            Margins.Right = 5
            Margins.Bottom = 5
            Caption = 'QS-Auswahl bei Retouren'
            TabOrder = 10
          end
          object RetCatAuswahlCheckBox: TCheckBox
            Left = 265
            Top = 191
            Width = 257
            Height = 17
            Margins.Left = 5
            Margins.Top = 5
            Margins.Right = 5
            Margins.Bottom = 5
            Caption = 'Kategorieauswahl bei Retouren'
            TabOrder = 9
          end
          object RetSelZustandCheckBox: TCheckBox
            Left = 265
            Top = 172
            Width = 257
            Height = 17
            Margins.Left = 5
            Margins.Top = 5
            Margins.Right = 5
            Margins.Bottom = 5
            Caption = 'Zustandsauswahl bei Retouren'
            TabOrder = 8
          end
          object RetSelGrundCheckBox: TCheckBox
            Left = 265
            Top = 152
            Width = 257
            Height = 17
            Margins.Left = 5
            Margins.Top = 5
            Margins.Right = 5
            Margins.Bottom = 5
            Caption = 'Auswahl des Retourengrundes'
            TabOrder = 7
          end
          object RetProcessPreGradingCheckBox: TCheckBox
            Left = 8
            Top = 175
            Width = 240
            Height = 16
            Margins.Left = 5
            Margins.Top = 5
            Margins.Right = 5
            Margins.Bottom = 5
            Caption = 'Vorqualifizierung auswerten'
            TabOrder = 6
          end
          object RetAutoPrintCheckBox: TCheckBox
            Left = 8
            Top = 51
            Width = 350
            Height = 17
            Margins.Left = 5
            Margins.Top = 5
            Margins.Right = 5
            Margins.Bottom = 5
            Anchors = [akLeft, akTop, akRight]
            Caption = 'Automatischer Druck des Retourenbeleges'
            TabOrder = 2
          end
          object RetPrintLableCheckBox: TCheckBox
            Left = 8
            Top = 72
            Width = 349
            Height = 17
            Margins.Left = 5
            Margins.Top = 5
            Margins.Right = 5
            Margins.Bottom = 5
            Caption = 'Retourenlabel ausdrucken'
            TabOrder = 3
          end
          object RetGrundPflichtCheckBox: TCheckBox
            Left = 8
            Top = 152
            Width = 240
            Height = 17
            Margins.Left = 5
            Margins.Top = 5
            Margins.Right = 5
            Margins.Bottom = 5
            AllowGrayed = True
            Caption = 'Retourengrunde muss ausgew'#228'hlt werden'
            TabOrder = 4
          end
          object RetAvisAutoBuchtCheckBox: TCheckBox
            Left = 8
            Top = 197
            Width = 240
            Height = 17
            Margins.Left = 5
            Margins.Top = 5
            Margins.Right = 5
            Margins.Bottom = 5
            Caption = 'Retourenavise nach Anlage durchbuchen'
            TabOrder = 5
          end
          object RetZustandCommentCheckBox: TCheckBox
            Left = 265
            Top = 230
            Width = 257
            Height = 17
            Margins.Left = 5
            Margins.Top = 5
            Margins.Right = 5
            Margins.Bottom = 5
            Caption = 'Zustands-Beschreibung'
            TabOrder = 11
          end
          object ReturnHintMemo: TMemo
            Left = 450
            Top = 8
            Width = 440
            Height = 82
            Margins.Left = 5
            Margins.Top = 5
            Margins.Right = 5
            Margins.Bottom = 5
            Anchors = [akLeft, akTop, akRight]
            Lines.Strings = (
              'ReturnHintMemo')
            TabOrder = 12
          end
          object RetVASCheckBox: TCheckBox
            Left = 8
            Top = 94
            Width = 349
            Height = 17
            Margins.Left = 5
            Margins.Top = 5
            Margins.Right = 5
            Margins.Bottom = 5
            Caption = 'Value Added Services m'#252'ssen erfasst werden'
            TabOrder = 13
          end
        end
        object PackTabSheet: TTabSheet
          Margins.Left = 5
          Margins.Top = 5
          Margins.Right = 5
          Margins.Bottom = 5
          Caption = 'Verpacken'
          ImageIndex = 5
          object VerteilPicCheckBox: TCheckBox
            Left = 8
            Top = 12
            Width = 422
            Height = 17
            Margins.Left = 5
            Margins.Top = 5
            Margins.Right = 5
            Margins.Bottom = 5
            Caption = 'Artikelbilder im Verteilen'
            TabOrder = 0
          end
          object VerpacklPicCheckBox: TCheckBox
            Left = 8
            Top = 35
            Width = 422
            Height = 17
            Margins.Left = 5
            Margins.Top = 5
            Margins.Right = 5
            Margins.Bottom = 5
            Caption = 'Artikelbilder beim Verpacken'
            TabOrder = 1
          end
          object WALHMCheckBox: TCheckBox
            Left = 8
            Top = 138
            Width = 422
            Height = 16
            Margins.Left = 5
            Margins.Top = 5
            Margins.Right = 5
            Margins.Bottom = 5
            Caption = 'Ladehilfsmittel erfassen'
            TabOrder = 4
          end
          object WALHMAutoCheckBox: TCheckBox
            Left = 23
            Top = 157
            Width = 407
            Height = 17
            Margins.Left = 5
            Margins.Top = 5
            Margins.Right = 5
            Margins.Bottom = 5
            Caption = 'Automatisch bei Abschluss'
            TabOrder = 5
          end
          object VerpackungCheckBox: TCheckBox
            Left = 8
            Top = 76
            Width = 422
            Height = 16
            Margins.Left = 5
            Margins.Top = 5
            Margins.Right = 5
            Margins.Bottom = 5
            Caption = 'Verpackungen erfassen'
            TabOrder = 2
          end
          object VerpackLTVorschlagCheckBox: TCheckBox
            Left = 23
            Top = 96
            Width = 422
            Height = 16
            Margins.Left = 5
            Margins.Top = 5
            Margins.Right = 5
            Margins.Bottom = 5
            Caption = 'Verpacksvorschl'#228'ge automatisch '#252'bernehmen'
            TabOrder = 3
          end
        end
        object VersandTabSheet: TTabSheet
          Margins.Left = 5
          Margins.Top = 5
          Margins.Right = 5
          Margins.Bottom = 5
          Caption = 'Versand'
          ImageIndex = 12
          DesignSize = (
            888
            324)
          object RetourenLabelCheckBox: TCheckBox
            Left = 11
            Top = 73
            Width = 322
            Height = 17
            Margins.Left = 5
            Margins.Top = 5
            Margins.Right = 5
            Margins.Bottom = 5
            Caption = 'Retouren-Lable erzeugen (F'#252'r alle Versender)'
            TabOrder = 2
          end
          object RetourenScheinCheckBox: TCheckBox
            Left = 11
            Top = 44
            Width = 322
            Height = 16
            Margins.Left = 5
            Margins.Top = 5
            Margins.Right = 5
            Margins.Bottom = 5
            Caption = 'Retourenschein drucken'
            TabOrder = 1
          end
          object LieferscheinCheckBox: TCheckBox
            Left = 11
            Top = 13
            Width = 322
            Height = 16
            Margins.Left = 5
            Margins.Top = 5
            Margins.Right = 5
            Margins.Bottom = 5
            Caption = 'Lieferscheinschein drucken'
            TabOrder = 0
          end
          object ZollGroupBox: TGroupBox
            Left = 11
            Top = 99
            Width = 879
            Height = 103
            Margins.Left = 5
            Margins.Top = 5
            Margins.Right = 5
            Margins.Bottom = 5
            Anchors = [akLeft, akTop, akRight]
            Caption = 'Export'
            TabOrder = 3
            object Label43: TLabel
              Left = 13
              Top = 51
              Width = 99
              Height = 13
              Margins.Left = 5
              Margins.Top = 5
              Margins.Right = 5
              Margins.Bottom = 5
              Caption = 'Basis Zolltraifnummer'
            end
            object BaseTaricNrEdit: TEdit
              Left = 13
              Top = 66
              Width = 118
              Height = 21
              Margins.Left = 5
              Margins.Top = 5
              Margins.Right = 5
              Margins.Bottom = 5
              TabOrder = 1
              Text = 'BaseTaricNrEdit'
            end
            object AutoZollAnmeldungCheckBox: TCheckBox
              Left = 13
              Top = 23
              Width = 285
              Height = 17
              Margins.Left = 5
              Margins.Top = 5
              Margins.Right = 5
              Margins.Bottom = 5
              Caption = 'Zolldaten elektronisch anmelden (DHL, GLS usw)'
              TabOrder = 0
            end
          end
        end
        object InvTabSheet: TTabSheet
          Margins.Left = 5
          Margins.Top = 5
          Margins.Right = 5
          Margins.Bottom = 5
          Caption = 'Inventur'
          ImageIndex = 1
          DesignSize = (
            888
            324)
          object InvEinzelScanCheckBox: TCheckBox
            Left = 5
            Top = 8
            Width = 403
            Height = 17
            Margins.Left = 5
            Margins.Top = 5
            Margins.Right = 5
            Margins.Bottom = 5
            Caption = 'Artikel einzeln scannen und z'#228'hlen'
            TabOrder = 0
          end
          object InvBesAbgleichOldMHDCheckBox: TCheckBox
            Left = 5
            Top = 58
            Width = 403
            Height = 16
            Margins.Left = 5
            Margins.Top = 5
            Margins.Right = 5
            Margins.Bottom = 5
            Caption = 'Bestandsabgleich auch f'#252'r abgelaufene Ware'
            TabOrder = 2
          end
          object InvAssignPosCheckBox: TCheckBox
            Left = 5
            Top = 33
            Width = 403
            Height = 17
            Margins.Left = 5
            Margins.Top = 5
            Margins.Right = 5
            Margins.Bottom = 5
            Caption = 'Von Platz zu Platz f'#252'hren'
            TabOrder = 1
          end
          object InvBesAbgleichANGCheckBox: TCheckBox
            Left = 5
            Top = 83
            Width = 403
            Height = 17
            Margins.Left = 5
            Margins.Top = 5
            Margins.Right = 5
            Margins.Bottom = 5
            Caption = 'Bestandsabgleich auch f'#252'r noch nicht gemeldete Ware '
            TabOrder = 3
          end
          object InvOptionGroupBox: TGroupBox
            Left = 8
            Top = 190
            Width = 337
            Height = 128
            Anchors = [akLeft, akBottom]
            Caption = 'Standard Optionen f'#252'r Inventuren'
            TabOrder = 4
            object InvOptSingleScanCheckBox: TCheckBox
              Left = 16
              Top = 40
              Width = 225
              Height = 17
              Caption = 'Artikel einzeln scannen'
              TabOrder = 1
            end
            object InvOptGoLPCheckBox: TCheckBox
              Left = 16
              Top = 56
              Width = 177
              Height = 17
              Caption = 'Von Platz zu Platz f'#252'hren'
              TabOrder = 2
            end
            object InvSuggestArtikelCheckBox: TCheckBox
              Left = 16
              Top = 72
              Width = 401
              Height = 17
              Caption = 'Erster Artikel vorschlagen'
              TabOrder = 3
            end
            object InvOptNurMengeCheckBox: TCheckBox
              Left = 16
              Top = 18
              Width = 321
              Height = 17
              Caption = 'Nur Mengen erfassen, Kein MHD oder Charge'
              TabOrder = 0
            end
            object InvSuggestLEBesCheckBox: TCheckBox
              Left = 16
              Top = 88
              Width = 401
              Height = 17
              Caption = 'Einzene Bestand auf HU oder LE vorschlagen'
              TabOrder = 4
            end
          end
          object InvChkOptionGroupBox: TGroupBox
            Left = 368
            Top = 190
            Width = 337
            Height = 128
            Anchors = [akLeft, akBottom]
            Caption = 'Standard Optionen f'#252'r Check Inventuren'
            TabOrder = 5
            object InvChkOptSingleScanCheckBox: TCheckBox
              Left = 16
              Top = 56
              Width = 225
              Height = 17
              Caption = 'Artikel einzeln scannen'
              TabOrder = 2
            end
            object InvChkOptGoLPCheckBox: TCheckBox
              Left = 16
              Top = 72
              Width = 177
              Height = 17
              Caption = 'Von Platz zu Platz f'#252'hren'
              TabOrder = 3
            end
            object InvChkSuggestArtikelCheckBox: TCheckBox
              Left = 16
              Top = 88
              Width = 401
              Height = 17
              Caption = 'Erster Artikel vorschlagen'
              TabOrder = 4
            end
            object InvChkOptNurMengeCheckBox: TCheckBox
              Left = 16
              Top = 18
              Width = 321
              Height = 17
              Caption = 'Nur Mengen erfassen, Kein MHD oder Charge'
              TabOrder = 0
            end
            object InvChkSuggestLEBesCheckBox: TCheckBox
              Left = 16
              Top = 104
              Width = 401
              Height = 17
              Caption = 'Einzene Bestand auf HU oder LE vorschlagen'
              TabOrder = 5
            end
            object InvChkAreaCheckBox: TCheckBox
              Left = 16
              Top = 34
              Width = 233
              Height = 17
              Caption = 'Checkinventuren pro Bereich'
              TabOrder = 1
            end
          end
        end
        object AvisTabSheet: TTabSheet
          Margins.Left = 5
          Margins.Top = 5
          Margins.Right = 5
          Margins.Bottom = 5
          Caption = 'Avisierung'
          ImageIndex = 4
          TabVisible = False
          DesignSize = (
            888
            324)
          object GroupBox2: TGroupBox
            Left = 4
            Top = 10
            Width = 886
            Height = 113
            Margins.Left = 5
            Margins.Top = 5
            Margins.Right = 5
            Margins.Bottom = 5
            Anchors = [akLeft, akTop, akRight]
            Caption = 'Avisierung nach Lieferabschluss'
            TabOrder = 0
            DesignSize = (
              886
              113)
            object Label12: TLabel
              Left = 8
              Top = 23
              Width = 82
              Height = 13
              Margins.Left = 5
              Margins.Top = 5
              Margins.Right = 5
              Margins.Bottom = 5
              Caption = 'E-Mail Adresse(n)'
            end
            object AVISMailLSCheckBox: TCheckBox
              Left = 8
              Top = 73
              Width = 167
              Height = 17
              Margins.Left = 5
              Margins.Top = 5
              Margins.Right = 5
              Margins.Bottom = 5
              Caption = 'Lieferscheine per Mail senden'
              TabOrder = 2
            end
            object AvisMailEdit: TEdit
              Left = 8
              Top = 39
              Width = 868
              Height = 21
              Margins.Left = 5
              Margins.Top = 5
              Margins.Right = 5
              Margins.Bottom = 5
              Anchors = [akLeft, akTop, akRight]
              MaxLength = 256
              TabOrder = 0
              Text = 'AvisMailEdit'
            end
            object AVISMailAvisCheckBox: TCheckBox
              Left = 250
              Top = 73
              Width = 167
              Height = 17
              Margins.Left = 5
              Margins.Top = 5
              Margins.Right = 5
              Margins.Bottom = 5
              Caption = 'Lieferavis per Mail senden'
              TabOrder = 1
              Visible = False
            end
          end
        end
        object InterfaceTabSheet: TTabSheet
          Margins.Left = 5
          Margins.Top = 5
          Margins.Right = 5
          Margins.Bottom = 5
          Caption = 'Schnittstelle'
          ImageIndex = 8
          object IFCAbgleichAufEmpfCheckBox: TCheckBox
            Left = 8
            Top = 16
            Width = 498
            Height = 17
            Margins.Left = 5
            Margins.Top = 5
            Margins.Right = 5
            Margins.Bottom = 5
            Caption = 'Auftr'#228'ge mit ge'#228'nderten Warenempf'#228'ngerdaten abgleichen'
            TabOrder = 0
          end
        end
        object QSTabSheet: TTabSheet
          Margins.Left = 5
          Margins.Top = 5
          Margins.Right = 5
          Margins.Bottom = 5
          Caption = 'Qualit'#228'tskontrolle'
          object Label19: TLabel
            Left = 3
            Top = 13
            Width = 45
            Height = 13
            Margins.Left = 5
            Margins.Top = 5
            Margins.Right = 5
            Margins.Bottom = 5
            Caption = 'Annahme'
          end
          object Label20: TLabel
            Left = 3
            Top = 39
            Width = 38
            Height = 13
            Margins.Left = 5
            Margins.Top = 5
            Margins.Right = 5
            Margins.Bottom = 5
            Caption = 'Retoure'
          end
          object QSWEComboBox: TComboBoxPro
            Left = 62
            Top = 10
            Width = 141
            Height = 29
            Margins.Left = 5
            Margins.Top = 5
            Margins.Right = 5
            Margins.Bottom = 5
            Style = csOwnerDrawFixed
            ItemHeight = 23
            TabOrder = 0
          end
          object QSRETComboBox: TComboBoxPro
            Left = 62
            Top = 36
            Width = 141
            Height = 29
            Margins.Left = 5
            Margins.Top = 5
            Margins.Right = 5
            Margins.Bottom = 5
            Style = csOwnerDrawFixed
            ItemHeight = 23
            TabOrder = 1
          end
        end
        object TexteTabSheet: TTabSheet
          Margins.Left = 5
          Margins.Top = 5
          Margins.Right = 5
          Margins.Bottom = 5
          Caption = 'Texte'
          ImageIndex = 11
          DesignSize = (
            888
            324)
          object Label23: TLabel
            Left = 8
            Top = 12
            Width = 145
            Height = 13
            Margins.Left = 5
            Margins.Top = 5
            Margins.Right = 5
            Margins.Bottom = 5
            Caption = 'BIO-Kontrollnr. des Mandanten'
          end
          object Label24: TLabel
            Left = 8
            Top = 70
            Width = 278
            Height = 13
            Margins.Left = 5
            Margins.Top = 5
            Margins.Right = 5
            Margins.Bottom = 5
            Caption = 'Standart Pick-Hinweis, wenn nichts anderes angegeben ist'
          end
          object Label25: TLabel
            Left = 8
            Top = 113
            Width = 282
            Height = 13
            Margins.Left = 5
            Margins.Top = 5
            Margins.Right = 5
            Margins.Bottom = 5
            Caption = 'Standart Pack-Hinweis, wenn nichts anderes angegeben ist'
          end
          object BioKontrollNrEdit: TEdit
            Left = 8
            Top = 31
            Width = 118
            Height = 21
            Margins.Left = 5
            Margins.Top = 5
            Margins.Right = 5
            Margins.Bottom = 5
            MaxLength = 32
            TabOrder = 0
            Text = 'BioKontrollNrEdit'
          end
          object StdPickHintEdit: TEdit
            Left = 8
            Top = 87
            Width = 871
            Height = 21
            Margins.Left = 5
            Margins.Top = 5
            Margins.Right = 5
            Margins.Bottom = 5
            Anchors = [akLeft, akTop, akRight]
            MaxLength = 128
            TabOrder = 1
            Text = 'StdPickHintEdit'
          end
          object StdPackHintEdit: TEdit
            Left = 8
            Top = 129
            Width = 871
            Height = 21
            Margins.Left = 5
            Margins.Top = 5
            Margins.Right = 5
            Margins.Bottom = 5
            Anchors = [akLeft, akTop, akRight]
            MaxLength = 128
            TabOrder = 2
            Text = 'StdPackHintEdit'
          end
        end
      end
    end
  end
end
