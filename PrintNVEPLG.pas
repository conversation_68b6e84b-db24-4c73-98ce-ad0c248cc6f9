unit PrintNVEPLG;

interface

uses
  Windows, Messages, SysUtils, Variants, Classes, Graphics, Controls, Forms,
  Dialogs, StdCtrls, ComboBoxPro, ExtCtrls, DB, ADODB, PrinterUtils;

type
  TPrintNVEForm = class(TForm)
    Panel2: TPanel;
    Label8: TLabel;
    PrinterComboBox: TComboBoxPro;
    PrintButton: TButton;
    CloseButton: TButton;
    Label1: TLabel;
    NVENrLabel: TLabel;
    ADOQuery1: TADOQuery;
    Label2: TLabel;
    NVEAnzLabel: TLabel;
    Label3: TLabel;
    NVESpeditionLabel: TLabel;
    PreviewCheckBox: TCheckBox;
    procedure PrintButtonClick(Sender: TObject);
    procedure FormShow(Sender: TObject);
    procedure FormCreate(Sender: TObject);
    procedure PrinterComboBoxChange(Sender: TObject);
  private
    PortArray  :  array [0..255] of TPrinterPorts;
    fMandant   : Integer;
    fLager     : Integer;
    fPrinted   : Boolean;
  public
    RefNVE,
    RefWA,
    RefKomm,
    RefKommPos,
    RefMand,
    RefLager     : Integer;
    LabelArt     : String;
    OptMaster    : Boolean;

    NVEListCount : Integer;
    NVEList      : array [0..1023] of integer;

    property Printed : boolean read fPrinted;

    function GetSelectedPrinter : String; overload;
    function GetSelectedPrinterInfo : TPrinterPorts; overload;

    procedure Prepare (const DefPrinterName, Format : String; const RefMand, RefLager : Integer);
  end;

implementation

uses DatenModul, LVSGlobalDaten, LVSDatenInterface, PrintModul, FrontendUtils, ResourceText, LablePrinterUtils,
     SprachModul, VerlaufDLG;

{$R *.dfm}

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 02.05.2017
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function TPrintNVEForm.GetSelectedPrinter : String;
var
  prtstr : String;
begin
  if (PrinterComboBox.ItemIndex <= Low (PortArray)) or (PrinterComboBox.ItemIndex > High (PortArray)) then
    prtstr := ''
  else if (Length (PortArray [PrinterComboBox.ItemIndex].Name) = 0) then
    prtstr := ''
  else if (Length (PortArray [PrinterComboBox.ItemIndex].PrtTyp) = 0) then
    prtstr := PortArray [PrinterComboBox.ItemIndex].Name
  else
    prtstr := PortArray [PrinterComboBox.ItemIndex].Name + ';' + PortArray [PrinterComboBox.ItemIndex].PrtTyp;

  Result := prtstr;
end;

//******************************************************************************
//* Function Name: GetSelectedPrinterInfo
//* Author       : Stefan Graf
//* Datum        : 13.09.2022
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function TPrintNVEForm.GetSelectedPrinterInfo : TPrinterPorts;
begin
  if (PrinterComboBox.ItemIndex <= Low (PortArray)) or (PrinterComboBox.ItemIndex > High (PortArray)) then
    Result.Clear
  else if (Length (PortArray [PrinterComboBox.ItemIndex].Name) = 0) then
    Result.Clear
  else
    Result := PortArray [PrinterComboBox.ItemIndex]
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 21.03.2018
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TPrintNVEForm.FormCreate(Sender: TObject);
begin
  RefNVE := -1;
  RefWA := -1;
  RefKomm := -1;
  RefKommPos := -1;
  NVEListCount := 0;
  OptMaster := False;

  fPrinted := false;

  LVSSprachModul.InitForm (Self);

  {$ifdef TranslateHelper}
    LVSSprachModul.SetNoTranslate (Self, PrinterComboBox);
    LVSSprachModul.SetNoTranslate (Self, NVENrLabel);
    LVSSprachModul.SetNoTranslate (Self, NVESpeditionLabel);
    LVSSprachModul.SetNoTranslate (Self, NVEAnzLabel);
  {$endif}
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//* Datum        : 06.07.2015
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TPrintNVEForm.FormShow(Sender: TObject);
begin
  Label2.Visible := NVEAnzLabel.Visible;
  Label3.Visible := NVESpeditionLabel.Visible;

  PrinterComboBoxChange (Sender);
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TPrintNVEForm.Prepare (const DefPrinterName, Format : String; const RefMand, RefLager : Integer);
begin
  fMandant := RefMand;
  fLager   := RefLager;

  PrintModule.LoadPrinterCombobox (fLager, Format, PrinterComboBox, PortArray, DefPrinterName, True);

  (*
  if (PrinterComboBox.Items.Count = 0) Then begin
    PrinterComboBox.ItemIndex := -1;
    PrinterComboBox.Text := '';
  end else begin
    PrinterComboBox.ItemIndex := PrinterComboBox.IndexOf (DefPrinterName, 0, True);
    if (PrinterComboBox.ItemIndex = -1) then
      PrinterComboBox.ItemIndex := 0;
  end;
  *)
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TPrintNVEForm.PrintButtonClick(Sender: TObject);
var
  i,
  res      : Integer;
  fname,
  errtxt,
  sendnr,
  lblart,
  sendurl,
  liststr  : String;
  verlauf  : TVerlaufForm;
begin
  res := 0;
  verlauf := nil;

  if (PrinterComboBox.ItemIndex < 0) or (PrinterComboBox.ItemIndex > High (PortArray)) then
    MessageDLG (FormatMessageText (1131, []), mtError, [mbOk], 0)
  else if (PortArray [PrinterComboBox.ItemIndex].PrtTyp = 'LASER') then begin
    if (RefNVE > -1) then begin
      if ((LabelArt = 'AMAZONE_PRIME') or (LabelArt = 'AMAZON_PRIME')) then begin
        ADOQuery1.SQL.Clear;
        ADOQuery1.SQL.Add ('select auf.AUFTRAG_NR from V_NVE nve, V_AUFTRAG_01 auf where auf.REF=nve.REF_AUF_KOPF and nve.REF=:ref');
        ADOQuery1.Parameters.ParamByName('ref').Value := RefNVE;

        try
          ADOQuery1.Open;

          fname := DatenPath + 'Prime\' + ADOQuery1.FieldByName ('AUFTRAG_NR').AsString+'.png';
          res := PrintGraphiLabel (PortArray [PrinterComboBox.ItemIndex], fname, sendnr, sendurl, errtxt);

          if (res = 0) then begin
            res := SetNVEGedruckt (RefNVE);

            if (res = 0) and (Length (sendnr) > 0) then
              res := SetNVESendungsNrURL (RefNVE, 'AMAZONE_PRIME', 'AMAZONE_PRIME', sendnr, sendurl);
          end;

          ADOQuery1.Close;
        except
          res := -9;
          errtxt := '';
        end;
      end else begin
        ADOQuery1.SQL.Clear;
        ADOQuery1.SQL.Add ('select'
                          +'   nve.REF,sped.LABEL_ART,nve.OPT_MASTER_NVE,'
                          +' (select TEXT from'
                          +'     (select'
                          +'       inf.TEXT'
                          +'      from'
                          +'        V_WARENEMPF_REL_KOMM rel'
                          +'        left outer join V_INF_WERTE inf on (inf.TABELLE=''NVE_LABEL_ART'' and inf.SPALTE=''NVE_LABEL_ART'' and inf.WERT=rel.NVE_LABEL_ART)'
                          +'      where'
                          +'        rel.REF_WARENEMPF=auf.REF_WARENEMPF'
                          +'        and rel.REF_LOCATION=:ref_loc'
                          +'      order by'
                          +'        case when rel.REF_LAGER=nve.REF_LAGER then 0 else 9 end asc'
                          +'     )'
                          +'    where rownum=1'
                          +'   ) as NVE_LABEL_ART,'
                          +'   auf.FORMULAR_KENNZEICHEN'
                          +' from'
                          +'   V_NVE_01 nve'
                          +'   left outer join V_SPEDITIONEN sped on (sped.REF=nve.REF_SPED)'
                          +'   left outer join V_AUFTRAG auf on (auf.REF=nve.REF_AUF_KOPF)'
                          +'   left outer join V_WARENEMPF emp on (emp.REF=auf.REF_WARENEMPF)'
                          +' where'
                          +'   nve.REF=:ref'
                          );
        ADOQuery1.Parameters.ParamByName('ref').Value := RefNVE;
        ADOQuery1.Parameters.ParamByName ('ref_loc').Value := LVSDatenModul.AktLocationRef;

        if PreviewCheckBox.Checked then
          res := PrintModule.PreparePreview;

        try
          ADOQuery1.Open;

          if (OptMaster) then
            res := PrintModule.PrintReport('', PortArray [PrinterComboBox.ItemIndex].Port, '', -1, RefMand, RefLager, '', 'LASER-NVE-MASTER', ADOQuery1.FieldByName('FORMULAR_KENNZEICHEN').AsString, ['REF:' + IntToStr (RefNVE)], errtxt, PreviewCheckBox.Checked)
          else if (not ADOQuery1.Fields [1].IsNull and (ADOQuery1.Fields [1].AsString <> 'NVE')) then begin
            res := BuildNVEVersandinfos (RefNVE, ADOQuery1.Fields [1].AsString);

            if (res <> 0) then
              errtxt := LVSDatenModul.LastLVSErrorText
            else if not ADOQuery1.Fields [3].IsNull then
              res := PrintModule.PrintReport('', PortArray [PrinterComboBox.ItemIndex].Port, '', -1, RefMand, RefLager, '', 'LASER-'+ADOQuery1.Fields [3].AsString, ADOQuery1.FieldByName('FORMULAR_KENNZEICHEN').AsString, ['REF:' + IntToStr (RefNVE)], errtxt, PreviewCheckBox.Checked)
            else
              res := PrintModule.PrintReport('', PortArray [PrinterComboBox.ItemIndex].Port, '', -1, RefMand, RefLager, '', 'LASER-'+ADOQuery1.Fields [1].AsString, ADOQuery1.FieldByName('FORMULAR_KENNZEICHEN').AsString, ['REF:' + IntToStr (RefNVE)], errtxt, PreviewCheckBox.Checked)
          end else if not ADOQuery1.Fields [3].IsNull then
            res := PrintModule.PrintReport('', PortArray [PrinterComboBox.ItemIndex].Port, '', -1, RefMand, RefLager, '', 'LASER-'+ADOQuery1.Fields [3].AsString, ADOQuery1.FieldByName('FORMULAR_KENNZEICHEN').AsString, ['REF:' + IntToStr (RefNVE)], errtxt, PreviewCheckBox.Checked)
          else if CheckMischNVE (RefNVE) then
            res := PrintModule.PrintReport('', PortArray [PrinterComboBox.ItemIndex].Port, '', -1, RefMand, RefLager, '', 'LASER-NVE-MISCH', ADOQuery1.FieldByName('FORMULAR_KENNZEICHEN').AsString, ['REF:' + IntToStr (RefNVE)], errtxt, PreviewCheckBox.Checked)
          else
            res := PrintModule.PrintReport('', PortArray [PrinterComboBox.ItemIndex].Port, '', -1, RefMand, RefLager, '', 'LASER-NVE', ADOQuery1.FieldByName('FORMULAR_KENNZEICHEN').AsString, ['REF:' + IntToStr (RefNVE)], errtxt, PreviewCheckBox.Checked);

          ADOQuery1.Close;
        except
          res := -9;
          errtxt := '';
        end;

        if (res = 0) Then begin
          fPrinted := true;

          if PreviewCheckBox.Checked then
            PrintModule.BeginPreview
        end else begin
          if PreviewCheckBox.Checked then
            PrintModule.StopPreview;
        end;
      end;
    end else if (RefWA > 0) then begin
      res := 0;

      if PreviewCheckBox.Checked then
        res := PrintModule.PreparePreview;

      ADOQuery1.SQL.Clear;
      ADOQuery1.SQL.Add ('select'
                        +'   nve.REF,sped.LABEL_ART,nve.OPT_MASTER_NVE,'
                        +' (select TEXT from'
                        +'     (select'
                        +'       inf.TEXT'
                        +'      from'
                        +'        V_WARENEMPF_REL_KOMM rel'
                        +'        left outer join V_INF_WERTE inf on (inf.TABELLE=''NVE_LABEL_ART'' and inf.SPALTE=''NVE_LABEL_ART'' and inf.WERT=rel.NVE_LABEL_ART)'
                        +'      where'
                        +'        rel.REF_WARENEMPF=auf.REF_WARENEMPF'
                        +'        and rel.REF_LOCATION=:ref_loc'
                        +'      order by'
                        +'        case when rel.REF_LAGER=nve.REF_LAGER then 0 else 9 end asc'
                        +'     )'
                        +'    where rownum=1'
                        +'   ) as NVE_LABEL_ART,'
                        +'   auf.FORMULAR_KENNZEICHEN'
                        +' from'
                        +'   V_NVE_01 nve'
                        +'   left outer join V_SPEDITIONEN sped on (sped.REF=nve.REF_SPED)'
                        +'   left outer join V_AUFTRAG auf on (auf.REF=nve.REF_AUF_KOPF)'
                        +'   left outer join V_WARENEMPF emp on (emp.REF=auf.REF_WARENEMPF)'
                        +' where'
                        +'   not (nve.STATUS in (''ANG'',''DEL'')) and nve.REF_WA=:ref_wa'
                        +' order by'
                        +'   nve.PACKAGE_NR nulls last'
                        );
      ADOQuery1.Parameters.ParamByName('ref_wa').Value := RefWA;
      ADOQuery1.Parameters.ParamByName ('ref_loc').Value := LVSDatenModul.AktLocationRef;

      try
        ADOQuery1.Open;

        while not (ADOQuery1.Eof) and (res = 0) do begin
          if (ADOQuery1.Fields [1].AsString = '1') then
            res := PrintModule.PrintReport('', PortArray [PrinterComboBox.ItemIndex].Port, '', -1, RefMand, RefLager, '', 'LASER-NVE-MASTER', '', ['REF:' + ADOQuery1.Fields [0].AsString], errtxt, False)
          else if (not ADOQuery1.Fields [1].IsNull and (ADOQuery1.Fields [1].AsString <> 'NVE')) then begin
            res := BuildNVEVersandinfos (ADOQuery1.Fields [0].AsInteger, ADOQuery1.Fields [1].AsString);

            if (res <> 0) then
              errtxt := LVSDatenModul.LastLVSErrorText
            else if not ADOQuery1.Fields [3].IsNull then
              res := PrintModule.PrintReport('', PortArray [PrinterComboBox.ItemIndex].Port, '', -1, RefMand, RefLager, '', 'LASER-'+ADOQuery1.Fields [3].AsString, ADOQuery1.FieldByName('FORMULAR_KENNZEICHEN').AsString, ['REF:' + ADOQuery1.Fields [0].AsString], errtxt, PreviewCheckBox.Checked)
            else
              res := PrintModule.PrintReport('', PortArray [PrinterComboBox.ItemIndex].Port, '', -1, RefMand, RefLager, '', 'LASER-'+ADOQuery1.Fields [1].AsString, ADOQuery1.FieldByName('FORMULAR_KENNZEICHEN').AsString, ['REF:' + ADOQuery1.Fields [0].AsString], errtxt, PreviewCheckBox.Checked)
          end else if not ADOQuery1.Fields [3].IsNull then
            res := PrintModule.PrintReport('', PortArray [PrinterComboBox.ItemIndex].Port, '', -1, RefMand, RefLager, '', 'LASER-'+ADOQuery1.Fields [3].AsString, ADOQuery1.FieldByName('FORMULAR_KENNZEICHEN').AsString, ['REF:' + ADOQuery1.Fields [0].AsString], errtxt, PreviewCheckBox.Checked)
          else if CheckMischNVE (ADOQuery1.Fields [0].AsInteger) then
            res := PrintModule.PrintReport('', PortArray [PrinterComboBox.ItemIndex].Port, '', -1, RefMand, RefLager, '', 'LASER-NVE-MISCH', ADOQuery1.FieldByName('FORMULAR_KENNZEICHEN').AsString, ['REF:' + ADOQuery1.Fields [0].AsString], errtxt, PreviewCheckBox.Checked)
          else
            res := PrintModule.PrintReport('', PortArray [PrinterComboBox.ItemIndex].Port, '', -1, RefMand, RefLager, '', 'LASER-NVE', ADOQuery1.FieldByName('FORMULAR_KENNZEICHEN').AsString, ['REF:' + ADOQuery1.Fields [0].AsString], errtxt, PreviewCheckBox.Checked);

          ADOQuery1.Next;
        end;

        ADOQuery1.Close;
      except
        res := -9;
        errtxt := '';
      end;

      if (res = 0) Then begin
        fPrinted := true;

        if PreviewCheckBox.Checked then
          PrintModule.BeginPreview
      end else begin
        if PreviewCheckBox.Checked then
          PrintModule.StopPreview;
      end;
    end else if (NVEListCount > 0) then begin
      res := 0;

      liststr := '';

      for i:=0 to NVEListCount - 1 do begin
        if (i > 0) then liststr := liststr + ',';

        liststr := liststr + IntToStr (NVEList [i]);
      end;


      verlauf := TVerlaufForm.Create (Self);

      try
        verlauf.Caption := 'NVEs drucken';
        verlauf.Label1.Visible := True;
        verlauf.Label1.Caption := 'NVE drucken';
        verlauf.ProgressBar1.Position := 0;

        if PreviewCheckBox.Checked then
          res := PrintModule.PreparePreview;

        ADOQuery1.SQL.Clear;
        ADOQuery1.SQL.Add ( 'select'
                           +' nve.REF, sped.LABEL_ART, nve.OPT_MASTER_NVE,'
                           +' (select TEXT from'
                           +'     (select'
                           +'       inf.TEXT'
                           +'      from'
                           +'        V_WARENEMPF_REL_KOMM rel'
                           +'        left outer join V_INF_WERTE inf on (inf.TABELLE=''NVE_LABEL_ART'' and inf.SPALTE=''NVE_LABEL_ART'' and inf.WERT=rel.NVE_LABEL_ART)'
                           +'      where'
                           +'        rel.REF_WARENEMPF=auf.REF_WARENEMPF'
                           +'        and rel.REF_LOCATION=:ref_loc'
                           +'      order by'
                           +'        case when rel.REF_LAGER=nve.REF_LAGER then 0 else 9 end asc'
                           +'     )'
                           +'    where rownum=1'
                           +'   ) as NVE_LABEL_ART'
                           +'   ,(select max (count (*)) from V_NVE_INHALT where REF_NVE=nve.REF group by ARTIKEL_NR, MHD, CHARGE)'
                           +'   ,auf.FORMULAR_KENNZEICHEN'
                           +' from'
                           +'   V_NVE_01 nve'
                           +'   left outer join V_SPEDITIONEN sped on (sped.REF=nve.REF_SPED)'
                           +'   left outer join V_AUFTRAG auf on (auf.REF=nve.REF_AUF_KOPF)'
                           +'   left outer join V_WARENEMPF emp on (emp.REF=auf.REF_WARENEMPF)'
                           +' where'
                           +'   not (nve.STATUS in (''ANG'',''DEL'')) and nve.REF in ('+liststr+')'
                           +' order by'
                           +'   nve.REF_AUF_KOPF, nve.PACKAGE_NR'
                          );
        ADOQuery1.Parameters.ParamByName ('ref_loc').Value := LVSDatenModul.AktLocationRef;

        try
          ADOQuery1.Open;

          verlauf.ProgressBar1.Position := 0;
          verlauf.ProgressBar1.Max := ADOQuery1.RecordCount;
          verlauf.BeginShowModal;

          while not (ADOQuery1.Eof) and (res = 0) and not (verlauf.AbortFlag) do begin
            if (ADOQuery1.Fields [2].AsString = '1') then
              res := PrintModule.PrintReport('', PortArray [PrinterComboBox.ItemIndex].Port, '', -1, RefMand, RefLager, '', 'LASER-NVE-MASTER', '', ['REF:' + ADOQuery1.Fields [0].AsString], errtxt, False)
            else if (not ADOQuery1.Fields [1].IsNull and (ADOQuery1.Fields [1].AsString <> 'NVE')) then begin
              res := BuildNVEVersandinfos (ADOQuery1.Fields [0].AsInteger, ADOQuery1.Fields [1].AsString);

              if (res <> 0) then
                errtxt := LVSDatenModul.LastLVSErrorText
              else if not ADOQuery1.Fields [3].IsNull then
                res := PrintModule.PrintReport('', PortArray [PrinterComboBox.ItemIndex].Port, '', -1, RefMand, RefLager, '', 'LASER-'+ADOQuery1.Fields [3].AsString, ADOQuery1.FieldByName('FORMULAR_KENNZEICHEN').AsString, ['REF:' + ADOQuery1.Fields [0].AsString], errtxt, PreviewCheckBox.Checked)
              else
                res := PrintModule.PrintReport('', PortArray [PrinterComboBox.ItemIndex].Port, '', -1, RefMand, RefLager, '', 'LASER-'+ADOQuery1.Fields [1].AsString, ADOQuery1.FieldByName('FORMULAR_KENNZEICHEN').AsString, ['REF:' + ADOQuery1.Fields [0].AsString], errtxt, PreviewCheckBox.Checked);
            end else if not ADOQuery1.Fields [3].IsNull then
              res := PrintModule.PrintReport('', PortArray [PrinterComboBox.ItemIndex].Port, '', -1, RefMand, RefLager, '', 'LASER-'+ADOQuery1.Fields [3].AsString, ADOQuery1.FieldByName('FORMULAR_KENNZEICHEN').AsString, ['REF:' + ADOQuery1.Fields [0].AsString], errtxt, PreviewCheckBox.Checked)
            else if (ADOQuery1.Fields [4].AsInteger > 1) then
              res := PrintModule.PrintReport('', PortArray [PrinterComboBox.ItemIndex].Port, '', -1, RefMand, RefLager, '', 'LASER-NVE-MISCH', ADOQuery1.FieldByName('FORMULAR_KENNZEICHEN').AsString, ['REF:' + ADOQuery1.Fields [0].AsString], errtxt, PreviewCheckBox.Checked)
            else
              res := PrintModule.PrintReport('', PortArray [PrinterComboBox.ItemIndex].Port, '', -1, RefMand, RefLager, '', 'LASER-NVE', ADOQuery1.FieldByName('FORMULAR_KENNZEICHEN').AsString, ['REF:' + ADOQuery1.Fields [0].AsString], errtxt, PreviewCheckBox.Checked);

            verlauf.ProgressBar1.StepIt;
            verlauf.UpdateModal;

            ADOQuery1.Next;
          end;

          ADOQuery1.Close;
        except
          res := -9;
          errtxt := '';
        end;
      finally
        verlauf.EndShowModal;
        verlauf.Release;
      end;

      if (res = 0) Then begin
        fPrinted := true;

        if PreviewCheckBox.Checked then
          PrintModule.BeginPreview
      end else begin
        if PreviewCheckBox.Checked then
          PrintModule.StopPreview;
      end;
    end else if (RefKomm > 0) then begin
      res := 0;

      if PreviewCheckBox.Checked then
        res := PrintModule.PreparePreview;

      ADOQuery1.SQL.Clear;
      ADOQuery1.SQL.Add ('select REF from V_KOMM_POS where REF_KOMM_KOPF=:ref_komm');
      ADOQuery1.Parameters.ParamByName('ref_komm').Value := RefKomm;

      try
        ADOQuery1.Open;

        while not (ADOQuery1.Eof) and (res = 0) do begin
          res := PrintModule.PrintReport('', PortArray [PrinterComboBox.ItemIndex].Port, '', -1, RefMand, RefLager, '', 'LASER-NVE-KOMM', '', ['REF:' + ADOQuery1.Fields [0].AsString], errtxt, False);

          ADOQuery1.Next;
        end;

        ADOQuery1.Close;
      except
        res := -9;
        errtxt := '';
      end;

      if (res = 0) Then begin
        if PreviewCheckBox.Checked then
          PrintModule.BeginPreview
      end else begin
        if PreviewCheckBox.Checked then
          PrintModule.StopPreview;
      end;
    end else if (RefKommPos > 0) then begin
      if PreviewCheckBox.Checked then
        res := PrintModule.PreparePreview;

      res := PrintModule.PrintReport('', PortArray [PrinterComboBox.ItemIndex].Port, '', -1, RefMand, RefLager, '', 'LASER-NVE-KOMM', '', ['REF:' + IntToStr (RefKommPos)], errtxt, False);

      if (res = 0) Then begin
        fPrinted := true;

        if PreviewCheckBox.Checked then
          PrintModule.BeginPreview
      end else begin
        if PreviewCheckBox.Checked then
          PrintModule.StopPreview;
      end;
    end;

    if (res <> 0) then
      MessageDLG(FormatMessageText (1539, [errtxt]), mtError, [mbOK], 0)
    else if (RefKomm > 0) or (RefWA > 0) then
      Close;
  end else begin
    if (RefNVE > 0) then begin
      if (Length (LabelArt) = 0) or (LabelArt = 'NVE') then
        res := CreateNVEPrintJob (RefNVE, RefMand, RefLager, -1, PortArray [PrinterComboBox.ItemIndex].Name)
      else if (copy (LabelArt, 1, 4) = 'NVE_') then
        res := CreateNVEPrintJob (RefNVE, -1, PortArray [PrinterComboBox.ItemIndex].Name, LabelArt)
      else if (LabelArt = 'AMAZONE_PRIME') then begin
        ADOQuery1.SQL.Clear;
        ADOQuery1.SQL.Add ('select auf.AUFTRAG_NR from V_NVE nve, V_AUFTRAG_01 auf where auf.REF=nve.REF_AUF_KOPF and nve.REF=:ref');
        ADOQuery1.Parameters.ParamByName('ref').Value := RefNVE;

        try
          ADOQuery1.Open;

          fname := DatenPath + 'Prime\' + ADOQuery1.FieldByName ('AUFTRAG_NR').AsString+'.png';
          res := PrintGraphiLabel (PortArray [PrinterComboBox.ItemIndex], fname, sendnr, sendurl, errtxt);

          if (res = 0) then begin
            res := SetNVEGedruckt (RefNVE);

            if (res = 0) and (Length (sendnr) > 0) then
              res := SetNVESendungsNrURL (RefNVE, 'AMAZONE_PRIME', 'AMAZONE_PRIME', sendnr, sendurl);
          end;

          ADOQuery1.Close;
        except
          res := -9;
          errtxt := '';
        end;
      end else if (copy (LabelArt,1,4) = 'KEP_') then
        res := CreateVersandPrintJob (RefNVE, '', -1, PortArray [PrinterComboBox.ItemIndex].Name)
      else if (copy (LabelArt,1,5) = 'SPED_') then
        res := CreateNVELabelPrintJob (RefNVE, -1, PortArray [PrinterComboBox.ItemIndex].Name)
      else
        res := CreateAdrPrintJob (RefNVE, -1, PortArray [PrinterComboBox.ItemIndex].Name, LabelArt);

      if (res <> 0) then
        errtxt := LVSDatenModul.LastLVSErrorText
      else
        fPrinted := true;
    end else if (RefWA > 0) then begin
      res := 0;

      ADOQuery1.SQL.Clear;
      ADOQuery1.SQL.Add ('select nve.REF, sped.LABEL_ART from V_NVE nve left outer join V_SPEDITIONEN sped on (sped.REF=nve.REF_SPED) where not (nve.STATUS in (''ANG'',''DEL'')) and nve.REF_WA=:ref_wa');
      ADOQuery1.Parameters.ParamByName('ref_wa').Value := RefWA;

      try
        ADOQuery1.Open;

        while not (ADOQuery1.Eof) and (res = 0) do begin
          lblart := ADOQuery1.Fields [1].AsString;

          if (Length (lblart) = 0) or (lblart = 'NVE') then
            res := CreateNVEPrintJob (ADOQuery1.Fields [0].AsInteger, RefMand, RefLager, -1, PortArray [PrinterComboBox.ItemIndex].Name)
          else if (copy (lblart, 1, 4) = 'NVE_') then
            res := CreateNVEPrintJob (ADOQuery1.Fields [0].AsInteger, -1, PortArray [PrinterComboBox.ItemIndex].Name, lblart)
          else if (copy (LabelArt,1,4) = 'KEP_') then
            res := CreateVersandPrintJob (ADOQuery1.Fields [0].AsInteger, '', -1, PortArray [PrinterComboBox.ItemIndex].Name)
          else if (copy (lblart,1,5) = 'SPED_') then
            res := CreateNVELabelPrintJob (ADOQuery1.Fields [0].AsInteger, -1, PortArray [PrinterComboBox.ItemIndex].Name)
          else
            res := CreateAdrPrintJob (ADOQuery1.Fields [0].AsInteger, -1, PortArray [PrinterComboBox.ItemIndex].Name, lblart);

          if (res <> 0) then
            errtxt := LVSDatenModul.LastLVSErrorText;

          ADOQuery1.Next;
        end;

        ADOQuery1.Close;
      except
        res := -9;
        LVSDatenModul.LastLVSErrorText := '';
      end;

      if (res = 0) then
        fPrinted := true;
    end else if (NVEListCount > 0) then begin
      res := 0;

      liststr := '';

      for i:=0 to NVEListCount - 1 do begin
        if (i > 0) then liststr := liststr + ',';

        liststr := liststr + IntToStr (NVEList [i]);
      end;

      ADOQuery1.SQL.Clear;
      ADOQuery1.SQL.Add ('select nve.REF, sped.LABEL_ART from V_NVE nve left outer join V_SPEDITIONEN sped on (sped.REF=nve.REF_SPED) where not (nve.STATUS in (''ANG'',''DEL'')) and nve.REF in ('+liststr+')');

      try
        ADOQuery1.Open;

        while not (ADOQuery1.Eof) and (res = 0) do begin
          lblart := ADOQuery1.Fields [1].AsString;

          if (Length (lblart) = 0) or (lblart = 'NVE') then
            res := CreateNVEPrintJob (ADOQuery1.Fields [0].AsInteger, RefMand, RefLager, -1, PortArray [PrinterComboBox.ItemIndex].Name)
          else if (copy (lblart, 1, 4) = 'NVE_') then
            res := CreateNVEPrintJob (ADOQuery1.Fields [0].AsInteger, -1, PortArray [PrinterComboBox.ItemIndex].Name, lblart)
          else if (copy (LabelArt,1,4) = 'KEP_') then
            res := CreateVersandPrintJob (ADOQuery1.Fields [0].AsInteger, '', -1, PortArray [PrinterComboBox.ItemIndex].Name)
          else if (copy (lblart,1,5) = 'SPED_') then
            res := CreateNVELabelPrintJob (ADOQuery1.Fields [0].AsInteger, -1, PortArray [PrinterComboBox.ItemIndex].Name)
          else
            res := CreateAdrPrintJob (ADOQuery1.Fields [0].AsInteger, -1, PortArray [PrinterComboBox.ItemIndex].Name, lblart);

          if (res <> 0) then
            errtxt := LVSDatenModul.LastLVSErrorText;

          ADOQuery1.Next;
        end;

        ADOQuery1.Close;
      except
        res := -9;
        LVSDatenModul.LastLVSErrorText := '';
      end;

      if (res = 0) then
        fPrinted := true;
    end else if (RefKomm > 0) then begin
      res := CreateKommNVELabelPrintJob (RefKomm, -1, PortArray [PrinterComboBox.ItemIndex].Name);

      if (res <> 0) then
        errtxt := LVSDatenModul.LastLVSErrorText
      else
        fPrinted := true;
    end else if (RefKommPos > 0) then begin
      res := CreateKommPosNVELabelPrintJob (RefKommPos, -1, PortArray [PrinterComboBox.ItemIndex].Name);

      if (res <> 0) then
        errtxt := LVSDatenModul.LastLVSErrorText
      else
        fPrinted := true;
    end;

    if (res <> 0) then
      MessageDLG(FormatMessageText (1539, [errtxt]), mtError, [mbOK], 0)
    else if (RefKomm > 0) or (RefKommPos > 0) or (RefWA > 0) then
      Close;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TPrintNVEForm.PrinterComboBoxChange(Sender: TObject);
begin
  if (PrinterComboBox.ItemIndex >= 0) then
    PreviewCheckBox.Visible := (PortArray [PrinterComboBox.ItemIndex].PrtTyp = 'LASER')
end;

end.
