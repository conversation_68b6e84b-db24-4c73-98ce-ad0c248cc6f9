﻿unit SelectSpedTourForm;

interface

uses
  Winapi.Windows, Winapi.Messages, System.SysUtils, System.Variants, System.Classes, Vcl.Graphics,
  Vcl.Controls, Vcl.Forms, Vcl.Dialogs, Vcl.ExtCtrls, Data.DB, Vcl.StdCtrls, Vcl.Grids, Vcl.DBGrids, SMDBGrid, DBGridPro, MemDS,
  DBAccess, Ora, OraSmart;

type
  TForm1 = class(TForm)
    Panel1: TPanel;
    TourDBGrid: TDBGridPro;
    NewButton: TButton;
    OkButton: TButton;
    Button3: TButton;
    TourDataSource: TOraDataSource;
    TourQuery: TSmartQuery;
    procedure FormShow(Sender: TObject);
    procedure NewButtonClick(Sender: TObject);
    procedure FormCloseQuery(Sender: TObject; var CanClose: Boolean);
  private
    fRefLager : Integer;
    fRefTour  : Integer;
  public
    property RefLager : Integer read fRefLager write fRefLager;
    property RefTour : Integer read fRefTour write fRefTour;
  end;

var
  Form1: TForm1;

implementation

{$R *.dfm}

uses
  DatenModul;

procedure TForm1.NewButtonClick(Sender: TObject);
begin
  fRefTour := -1;
  ModalResult := mrOk;
  Close;
end;

procedure TForm1.FormCloseQuery(Sender: TObject; var CanClose: Boolean);
begin
  if (ModalResult <> mrOk) then
    CanClose := true
  else begin
    if (TourQuery.RecordCount > 0) then
      fRefTour := TourQuery.FieldByName ('REF').AsInteger;
  end;
end;

procedure TForm1.FormShow(Sender: TObject);
begin
  TourQuery.Session := LVSDatenModul.OraMainSession;

  TourQuery.SQL.Add ('select * from V_SPED_TOUR where STATUS=''ANG'' and REF_LAGER=:ref_lager');
  TourQuery.Params [0].Value := fRefLager;

  TourQuery.Open;
end;

end.
