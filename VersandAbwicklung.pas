﻿{$WARN UNIT_PLATFORM OFF}
{$WARN SYMBOL_PLATFORM OFF}
 unit VersandAbwicklung;

 {$i compilers.inc}

interface

uses
  Classes, PrinterUtils, LogFile;

procedure ResetVersandSystem;

function VersandPapiereNachdruck  (const RefAuftrag : Integer; const Preview, AufPrint, PackPrint, LSPrint, NVEPrint : Boolean; var ErrorMsg : String) : Integer;

function StartCreateVersandInfos  (AOwner: TComponent; const RefNVE : Integer; const PrtInfo : TPrinterPorts; var VersandApp : String; var ResponsFlag : Boolean; var ErrorText : String) : Integer;
function StartCreateRetoureLabel  (AOwner: TComponent; const RefAuftrag : Integer; const PrtInfo : TPrinterPorts; var VersandApp, LabelFormat : String; var LabelStream : TMemoryStream; var ResponsFlag : Boolean; var ErrorText : String) : Integer;
function StartLoadVersandInfos    (AOwner: TComponent; const RefNVE : Integer; var ErrorText : String) : Integer;


function FinishCreateVersandInfos (AOwner: TComponent; const RefNVE : Integer; const VersandApp, Sped : String; var TrackingNr, ErrorText : String) : Integer;
function FinishLoadVersandInfos   (AOwner: TComponent; const RefNVE : Integer; const Sped : String; var ErrorText : String) : Integer;
function DeleteVersandInfos       (AOwner: TComponent; const RefNVE : Integer; const Sped : String; var ErrorText : String) : Integer;

function PrintVersandFehlerLabel  (AOwner: TComponent; const RefNVE : Integer; const PrtInfo : TPrinterPorts; const ErrorMsg : String; var ErrorText : String) : Integer;

function CloseVersandDFUE         (AOwner: TComponent; const RefSped : Integer; const DruckerPort, DruckerStation : String; var ErrorText : String) : Integer;
function CloseWorkDay             (AOwner: TComponent; const RefSped : Integer; const RefAbschluss : Integer; var ErrorText : String) : Integer;

function ImportVersandInfos       (const RefSped : Integer; const VersandApp : String; var ErrorText : StrinG) : integer;

function GetDHLESSendungsNr : Integer;

implementation

uses
  {$ifdef Trace}
    Trace,
  {$endif}

  DB, VCLUtilitys, Variants, StringUtils,
  Math, Windows, SysUtils, ADODB, Win32Utils, ConfigModul, DatenModul, LVSGlobalDaten, LVSDatenInterface,
  Forms, Controls, InfoWinForm, ErrorTracking, PrintModul, FrontendUtils, LablePrinterUtils,
  DMSDatenInterface, PDFPreviewDLG, ResourceText, LVSConst, uLkJSON, SendHTTPRequest, DateUtils,
  EncdDecd, xml_base, xml_parser, xml_utils, Timers, LVSDruckInterface, Ora, OraSmart, CreateVersandLabel,
  ExportoVersand, ShippingUtils;

var
  infowin   : TInfoWin;

  refauf    : Integer;
  loadfname : String;

procedure ResetVersandSystem;
begin
  CreateVersandLabel.ResetVersandSystem;
end;

//****************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//****************************************************************************
//* Description
//****************************************************************************
//* Return Value :
//****************************************************************************
function VersandPapiereNachdruck (const RefAuftrag : Integer; const Preview, AufPrint, PackPrint, LSPrint, NVEPrint : Boolean; var ErrorMsg : String) : Integer;
var
  res,
  dmsref,
  kopie        : Integer;
  fname,
  stat,
  comstr,
  prttype,
  vers_prt     : String;
  errtxt       : String;
  fexp         : boolean;
  query        : TSmartQuery;
  nvequery     : TSmartQuery;
  dmsquery     : TSmartQuery;
  cfgquery     : TSmartQuery;
  reprint      : Boolean;
  docidx,
  prtref       : Integer;
  prtdaten     : TPrinterPorts;
  oldflag      : Boolean;
  optnvein     : String;
  shipinfo     : TShippingInfos;
begin
  res := 0;

  dmsref := -1;
  reprint := False;

  ErrorMsg := '';

  query    := LVSDatenModul.CreateSmartQuery (Nil, 'VersandPapiereNachdruck_query');
  cfgquery := LVSDatenModul.CreateSmartQuery (Nil, 'VersandPapiereNachdruck_cfg');

  try
    Screen.Cursor := crHourGlass;

    query.SQL.Clear;
    query.SQL.Add ( 'select a.REF,nvl (a.REF_SUB_MAND,a.REF_MAND) as REF_DOCU_MAND,a.REF_LAGER,a.REF_LIEFLAGER,a.AUFTRAG_NR,a.DRUCKART,a.SPRACHE,av.VERSAND_ART'
                   +',nvl (versplan.OPT_RETOUREN_SCHEIN, aufplan.OPT_RETOUREN_SCHEIN) as OPT_RETOUREN_SCHEIN'
                   +',av.OPT_PRINT_LS as AUF_OPT_PRINT_LS, nvl (av.OPT_PRINT_LS, aufplan.OPT_PRINT_LS) as OPT_PRINT_LS'
                   +',aufplan.OPT_DMS_PDF_LS'
                   +',sped.DFUE_ART,a.FORMULAR_KENNZEICHEN,nvl (adr.LAND_ISO,adr.LAND) as LAND_ISO,adr.PLZ,a.STATUS,mand.CONFIG_OPT as MAND_CONFIG_OPT,rech.RECHNUNGS_NR'
                   +',aufplan.OPT_PRINT_NVE_INHALT,aufplan.OPT_AUTO_PRINT_NVE_INHALT,aufplan.OPT_MDE_NVE_INHALT_PRINT,aufplan.OPT_AUTO_PRINT_LS'
                   +',(select count (*) from V_NVE where STATUS in (''ANG'',''WA'') and REF_AUF_KOPF=a.REF) as COUNT_OPEN_NVE,nvl (prod.OPT_NACHNAHME,av.OPT_NACHNAHME) as OPT_NACHNAHME,versplan.OPT_NACHNAHME_BELEG'
                   +',aufcfg.OPT_PRINT_INVOICE,av.OPT_RETOUREN_LABEL,sped.DFUE_KENNZEICHEN,mand.IBAN,nvl (rech.BRUTTO_BETRAG_CALC,rech.BRUTTO_BETRAG) as BRUTTO_BETRAG, a.REF_AUF_VERS_CFG, a.REF_ART_CONFIG'
                   +',lager.LAND as LAGER_LAND_ISO'
                   +' from V_AUFTRAG_LS a'
                   +'     inner join V_MANDANT mand on (mand.REF=nvl (a.REF_SUB_MAND, a.REF_MAND))'
                   +'     inner join V_LAGER lager on (lager.REF=a.REF_LAGER)'
                   +'     inner join V_AUFTRAG_VERSAND av on (av.REF_AUF_KOPF=a.REF)'
                   +'     inner join V_AUFTRAG_ADR adr on (adr.REF=a.REF_LIEFER_ADR)'
                   +'     inner join V_AUFTRAG_ART_CONFIG aufcfg on (aufcfg.REF=a.REF_ART_CONFIG)'
                   +'     left outer join V_AUFTRAG_ART_PLANUNG aufplan on (aufplan.REF=a.REF_ART_PLANUNG)'
                   +'     left outer join V_AUFTRAG_ART_VERSAND_CONFIG versplan on (versplan.REF=a.REF_AUF_VERS_CFG)'
                   +'     left outer join V_AUFTRAG_RECHNUNG rech on (rech.REF_AUF_KOPF=a.REF)'
                   +'     left outer join V_SPEDITIONEN sped on (sped.REF=av.REF_SPED)'
                   +'     left outer join V_SPED_PRODUKTE prod on (prod.REF=av.REF_SPED_PRODUKT)'
                   +'where a.REF=:RefAuf');
    query.Params.ParamByName('RefAuf').Value := RefAuftrag;

    try
      query.Open;

      PrintLogging ('REF='+query.FieldByName ('REF').AsString+
                    ', AufPrint='+BoolToStr (AufPrint)+', PackPrint='+BoolToStr (PackPrint)+', LSPrint='+BoolToStr (LSPrint)+', NVEPrint='+BoolToStr (NVEPrint)+
                    ', LAND_ISO='+query.FieldByName ('LAND_ISO').AsString+
                    ', AUF_OPT_PRINT_LS='+query.FieldByName ('AUF_OPT_PRINT_LS').AsString+
                    ', OPT_PRINT_LS='+query.FieldByName ('OPT_PRINT_LS').AsString+
                    ', OPT_AUTO_PRINT_LS='+query.FieldByName ('OPT_AUTO_PRINT_LS').AsString
                   );

      if (query.FieldByName ('LAND_ISO').IsNull or not (query.FieldByName ('LAND_ISO').AsString = 'DE')) then begin
        fexp := (query.FieldByName ('LAGER_LAND_ISO').IsNull and (query.FieldByName ('LAND_ISO').AsString <> 'DE'))
                  or
                (query.FieldByName ('LAND_ISO').AsString <> query.FieldByName ('LAGER_LAND_ISO').AsString);
      end;


      stat := query.FieldByName ('STATUS').AsString;

      GetOrderShippingInfos (RefAuftrag, shipinfo);

      PrintLogging ('DeliveryForm='+shipinfo.DeliveryForm+', ProformaForm='+shipinfo.ProformaForm+
                    ', OptPrintDelivery='+shipinfo.OptPrintDelivery+
                    ', OptAutoPrintDelivery='+BoolToStr (shipinfo.OptAutoPrintDelivery)+
                    ', OptPrintInvoice='+shipinfo.OptPrintInvoice+
                    ', OptPrintProforma='+shipinfo.OptPrintProforma
                   );

      if AufPrint then begin
        dmsref := -1;

        try
          //Rechnung bei Bar-Zahlern
          if ((Pos ('BAR', query.FieldByName ('DRUCKART').AsString) > 0) or (Pos ('RECHNUNG', query.FieldByName ('DRUCKART').AsString) > 0)) then begin
            if (Pos ('BAR', query.FieldByName ('DRUCKART').AsString) > 0) then begin
              res := FindWaWiProforma (query.FieldByName ('REF_DOCU_MAND').AsInteger,
                                       query.FieldByName ('AUFTRAG_NR').AsString,
                                       comstr, fname, dmsref);
            end else begin
              res := FindWaWiRechnung (query.FieldByName ('REF_DOCU_MAND').AsInteger,
                                       query.FieldByName ('AUFTRAG_NR').AsString,
                                       comstr, fname, dmsref);
            end;

            if (dmsref = -1) then begin
              //Wenn die Rechnung nicht da ist, wird eben nur der LS gedruckt
              if (Pos ('PRT-INVOICE_OR_DELIVERY', query.FieldByName ('DRUCKART').AsString) > 0) then
              else begin
                res := -15;

                if (Pos ('BAR', query.FieldByName ('DRUCKART').AsString) > 0) then
                  ErrorMsg := 'Die Proformarechnung für den Barverkaufsauftrag ' + query.FieldByName ('AUFTRAG_NR').AsString + ' liegt noch nicht vor'
                else
                  ErrorMsg := 'Die Rechnung für den Auftrag ' + query.FieldByName ('AUFTRAG_NR').AsString + ' liegt noch nicht vor';
              end;
            end;
          end else  if (query.FieldByName('OPT_PRINT_LS').AsString > '0') then begin
            res := FindWaWiLieferschein (query.FieldByName ('REF_DOCU_MAND').AsInteger,
                                         query.FieldByName ('AUFTRAG_NR').AsString,
                                         comstr, fname, dmsref);
          end;

          if (res = 0) then begin
            if (dmsref <> -1) then begin
              if Preview then begin
                PrintModule.AddPDFPreview (comstr, fname, 1, PrintModule.RELaserPrinter.BinNr, RefAuftrag);
              end else begin
                if (PrintModule.RELaserPrinter.BinNr = -1) then
                  res := PrintPDFDokument (PrintModule.RELaserPrinter.Port, fname, 1, '')
                else
                  res := PrintPDFDokument (PrintModule.RELaserPrinter.Port, fname, 1, '#'+IntToStr (PrintModule.RELaserPrinter.BinNr));
              end;

              if (res <> 0) then begin
                if (Length (ErrorMsg) > 0) then ErrorMsg := ErrorMsg + #13;
                ErrorMsg := FormatMessageText (1593, [errtxt]);
              end else if (Copy (query.FieldByName('MAND_CONFIG_OPT').AsString, cMandRetourenSchein, 1) > '0') then
                res := PrintModule.PrintReport ('Retourenschein für: ' + query.FieldByName ('AUFTRAG_NR').AsString, PrintModule.StdLaserPrinter.Port, '', -1, query.FieldByName('REF_DOCU_MAND').AsInteger, query.FieldByName('REF_LIEFLAGER').AsInteger, query.FieldByName('SPRACHE').AsString, shipinfo.ReturnForm, query.FieldByName('FORMULAR_KENNZEICHEN').AsString, ['REF:' + IntToStr (RefAuftrag)], errtxt, Preview, -1, PrintModule.StdLaserPrinter.BinNr)
              else if (Copy (query.FieldByName('OPT_RETOUREN_LABEL').AsString, 1, 1) = '1') or (Copy (query.FieldByName('OPT_RETOUREN_SCHEIN').AsString, 2, 1) = '1') then begin
                res := PrintModule.PrintReport ('Retourenschein für: ' + query.FieldByName ('AUFTRAG_NR').AsString, PrintModule.StdLaserPrinter.Port, '', -1, query.FieldByName('REF_DOCU_MAND').AsInteger, query.FieldByName('REF_LIEFLAGER').AsInteger, query.FieldByName('SPRACHE').AsString, shipinfo.ReturnForm, query.FieldByName('FORMULAR_KENNZEICHEN').AsString, ['REF:' + IntToStr (RefAuftrag)], errtxt, Preview, -1, PrintModule.StdLaserPrinter.BinNr);

                if (res <> 0) then begin
                  if (Length (ErrorMsg) > 0) then ErrorMsg := ErrorMsg + #13;
                  ErrorMsg := ErrorMsg + FormatMessageText (1594, [errtxt]);
                end;
              end;
            end;
          end;
        finally
        end;
      end else begin
        //Nur wenn alle NVEs des Auftrags abgeschlosse sind, wird der Lieferschein gedruckt

        if not PackPrint and not ((stat = 'WA') or (stat = 'ABG') or (stat = 'FIN') or (stat = 'TRA')) then begin
          res := -10;
          ErrorMsg := FormatMessageText (1055, []);
        end else if not PackPrint and (query.FieldByName ('COUNT_OPEN_NVE').AsInteger > 0) then begin
          res := -11;
          ErrorMsg := FormatMessageText (1056, []);
        end else if LSPrint then begin
          dmsref := -1;

          try
            //Rechnung bei Bar-Zahlern
            if ((Pos ('BAR', query.FieldByName ('DRUCKART').AsString) > 0) or (Pos ('RECHNUNG', query.FieldByName ('DRUCKART').AsString) > 0)) then begin
              if (Pos ('BAR', query.FieldByName ('DRUCKART').AsString) > 0) then begin
                res := FindWaWiProforma (query.FieldByName ('REF_DOCU_MAND').AsInteger,
                                         query.FieldByName ('AUFTRAG_NR').AsString,
                                         comstr, fname, dmsref);
              end else begin
                res := FindWaWiRechnung (query.FieldByName ('REF_DOCU_MAND').AsInteger,
                                         query.FieldByName ('AUFTRAG_NR').AsString,
                                         comstr, fname, dmsref);
              end;

              if (dmsref = -1) then begin
                //Wenn die Rechnung nicht da ist, wird eben nur der LS gedruckt
                if (Pos ('PRT-INVOICE_OR_DELIVERY', query.FieldByName ('DRUCKART').AsString) > 0) then
                else begin
                  res := -15;

                  if (Pos ('BAR', query.FieldByName ('DRUCKART').AsString) > 0) then
                    ErrorMsg := 'Die Proformarechnung für den Barverkaufsauftrag ' + query.FieldByName ('AUFTRAG_NR').AsString + ' liegt noch nicht vor'
                  else
                    ErrorMsg := 'Die Rechnung für den Auftrag ' + query.FieldByName ('AUFTRAG_NR').AsString + ' liegt noch nicht vor';
                end;
              end;
            end else if (Pos ('PRT-DELIVERY', query.FieldByName ('DRUCKART').AsString) > 0) then begin
              if (query.FieldByName('OPT_PRINT_LS').AsString > '0') then begin
                res := FindWaWiLieferschein (query.FieldByName ('REF_DOCU_MAND').AsInteger,
                                             query.FieldByName ('AUFTRAG_NR').AsString,
                                             comstr, fname, dmsref);
              end;
            end;

            if (res = 0) then begin
              if (dmsref <> -1) then begin
                if Preview then begin
                  PrintModule.AddPDFPreview (comstr, fname, 1, PrintModule.RELaserPrinter.BinNr, RefAuftrag);
                end else begin
                  if (PrintModule.RELaserPrinter.BinNr = -1) then
                    res := PrintPDFDokument (PrintModule.RELaserPrinter.Port, fname, 1, '')
                  else
                    res := PrintPDFDokument (PrintModule.RELaserPrinter.Port, fname, 1, '#'+IntToStr (PrintModule.RELaserPrinter.BinNr));
                end;

                if (res <> 0) then begin
                  if (Length (ErrorMsg) > 0) then ErrorMsg := ErrorMsg + #13;
                  ErrorMsg := FormatMessageText (1593, [errtxt]);
                end else if (Copy (query.FieldByName('MAND_CONFIG_OPT').AsString, cMandRetourenSchein, 1) > '0') then
                  res := PrintModule.PrintReport ('Retourenschein für: ' + query.FieldByName ('AUFTRAG_NR').AsString, PrintModule.StdLaserPrinter.Port, '', -1, query.FieldByName('REF_DOCU_MAND').AsInteger, query.FieldByName('REF_LIEFLAGER').AsInteger, query.FieldByName('SPRACHE').AsString, shipinfo.ReturnForm, query.FieldByName('FORMULAR_KENNZEICHEN').AsString, ['REF:' + IntToStr (RefAuftrag)], errtxt, Preview, -1, PrintModule.StdLaserPrinter.BinNr)
                else if (Copy (query.FieldByName('OPT_RETOUREN_LABEL').AsString, 1, 1) = '1') or (Copy (query.FieldByName('OPT_RETOUREN_SCHEIN').AsString, 2, 1) = '1') then begin
                  res := PrintModule.PrintReport ('Retourenschein für: ' + query.FieldByName ('AUFTRAG_NR').AsString, PrintModule.StdLaserPrinter.Port, '', -1, query.FieldByName('REF_DOCU_MAND').AsInteger, query.FieldByName('REF_LIEFLAGER').AsInteger, query.FieldByName('SPRACHE').AsString, shipinfo.ReturnForm, query.FieldByName('FORMULAR_KENNZEICHEN').AsString, ['REF:' + IntToStr (RefAuftrag)], errtxt, Preview, -1, PrintModule.StdLaserPrinter.BinNr);

                  if (res <> 0) then begin
                    if (Length (ErrorMsg) > 0) then ErrorMsg := ErrorMsg + #13;
                    ErrorMsg := ErrorMsg + FormatMessageText (1594, [errtxt]);
                  end;
                end;
              end else if (Pos ('PRT-INVOICE_OR_DELIVERY', query.FieldByName ('DRUCKART').AsString) > 0) then begin
                res := PrintModule.PrintReport ('Lieferschein für: ' + query.FieldByName ('AUFTRAG_NR').AsString, PrintModule.LSLaserPrinter.Port, '', RefAuftrag, query.FieldByName('REF_DOCU_MAND').AsInteger, query.FieldByName('REF_LIEFLAGER').AsInteger, query.FieldByName('SPRACHE').AsString, shipinfo.DeliveryForm, query.FieldByName('FORMULAR_KENNZEICHEN').AsString, ['REF:' + IntToStr (RefAuftrag)], errtxt, Preview, -1, PrintModule.LSLaserPrinter.BinNr);

                if (res <> 0) then begin
                  if (Length (ErrorMsg) > 0) then ErrorMsg := ErrorMsg + #13;
                  ErrorMsg := ErrorMsg + FormatMessageText (1595, [errtxt]);
                end else if (Copy (query.FieldByName('OPT_RETOUREN_SCHEIN').AsString, 2, 1) = '1') then begin
                  res := PrintModule.PrintReport ('Retourenschein für: ' + query.FieldByName ('AUFTRAG_NR').AsString, PrintModule.StdLaserPrinter.Port, '', -1, query.FieldByName('REF_DOCU_MAND').AsInteger, query.FieldByName('REF_LIEFLAGER').AsInteger, query.FieldByName('SPRACHE').AsString, shipinfo.ReturnForm, query.FieldByName('FORMULAR_KENNZEICHEN').AsString, ['REF:' + IntToStr (RefAuftrag)], errtxt, Preview, -1, PrintModule.StdLaserPrinter.BinNr);

                  if (res <> 0) then begin
                    if (Length (ErrorMsg) > 0) then ErrorMsg := ErrorMsg + #13;
                    ErrorMsg := ErrorMsg + FormatMessageText (1594, [errtxt]);
                  end;
                end;
              end else if (Pos ('PRT-INVOICE', query.FieldByName ('DRUCKART').AsString) > 0) then begin
                res := CalcAuftragRechnung (RefAuftrag);

                if (res <> 0) then begin
                  if (Length (ErrorMsg) > 0) then ErrorMsg := ErrorMsg + #13;
                  ErrorMsg := ErrorMsg + 'Fehler beim Neuberechen des Rechnungsbetrages' + #13 + #13 + errtxt
                end else begin
                  res := PrintModule.PrintReport ('Rechnung für: ' + query.FieldByName ('AUFTRAG_NR').AsString, PrintModule.RELaserPrinter.Port, '', RefAuftrag, query.FieldByName('REF_DOCU_MAND').AsInteger, query.FieldByName('REF_LIEFLAGER').AsInteger, query.FieldByName('SPRACHE').AsString, shipinfo.InvoiceForm, query.FieldByName('FORMULAR_KENNZEICHEN').AsString, ['REF:' + IntToStr (RefAuftrag)], errtxt, Preview, -1, PrintModule.RELaserPrinter.BinNr);

                  if (res <> 0) then begin
                    if (Length (ErrorMsg) > 0) then ErrorMsg := ErrorMsg + #13;
                    ErrorMsg := ErrorMsg + FormatMessageText (1593, [errtxt])
                  end else if (Copy (query.FieldByName('OPT_RETOUREN_SCHEIN').AsString, 2, 1) = '1') then begin
                    res := PrintModule.PrintReport ('Retourenschein für: ' + query.FieldByName ('AUFTRAG_NR').AsString, PrintModule.StdLaserPrinter.Port, '', -1, query.FieldByName('REF_DOCU_MAND').AsInteger, query.FieldByName('REF_LIEFLAGER').AsInteger, query.FieldByName('SPRACHE').AsString, shipinfo.ReturnForm, query.FieldByName('FORMULAR_KENNZEICHEN').AsString, ['REF:' + IntToStr (RefAuftrag)], errtxt, Preview, -1, PrintModule.StdLaserPrinter.BinNr);

                    if (res <> 0) then begin
                      if (Length (ErrorMsg) > 0) then ErrorMsg := ErrorMsg + #13;
                      ErrorMsg := ErrorMsg + FormatMessageText (1594, [errtxt]);
                    end;
                  end;
                end;
              end else begin
                if (Copy (query.FieldByName('MAND_CONFIG_OPT').AsString, cMandLieferSchein, 1) > '0') or (shipinfo.OptPrintDelivery > '0') then begin
                  if Preview or query.FieldByName('OPT_DMS_PDF_LS').IsNull or (query.FieldByName('OPT_DMS_PDF_LS').AsString = '0') or (query.FieldByName('OPT_DMS_PDF_LS').AsString = '1') then begin
                    //Nur wenn auch automatisch beim Packen gedruckt werden soll
                    if not PackPrint or shipinfo.OptAutoPrintDelivery then begin
                      //Anzahl der Kopien bestimmen
                      if (shipinfo.OptPrintDelivery = '1') then
                        kopie := -1
                      else if TryStrToInt (shipinfo.OptPrintDelivery, kopie) then
                        kopie := kopie - 1
                      else
                        kopie := -1;

                      res := FindLieferschein (query.FieldByName ('REF').AsInteger, comstr, fname, dmsref);

                      if (dmsref > 0) then begin
                        if Preview then begin
                          if (Length (comstr) = 0)  then
                            comstr := 'Lieferschein für: ' + query.FieldByName ('AUFTRAG_NR').AsString;

                          PrintModule.AddPDFPreview (comstr, fname, 1, PrintModule.LSLaserPrinter.BinNr, RefAuftrag);                          
                        end else begin
                          if (PrintModule.LSLaserPrinter.BinNr = -1) then
                            res := PrintPDFDokument (PrintModule.LSLaserPrinter.Port, fname, kopie, '')
                          else
                            res := PrintPDFDokument (PrintModule.LSLaserPrinter.Port, fname, kopie, '#'+IntToStr (PrintModule.LSLaserPrinter.BinNr));
                        end;
                      end else begin
                        res := PrintModule.PrintReport ('Lieferschein für: ' + query.FieldByName ('AUFTRAG_NR').AsString, PrintModule.LSLaserPrinter.Port, '', RefAuftrag, query.FieldByName('REF_DOCU_MAND').AsInteger, query.FieldByName('REF_LIEFLAGER').AsInteger, query.FieldByName('SPRACHE').AsString, shipinfo.DeliveryForm, query.FieldByName('FORMULAR_KENNZEICHEN').AsString, ['REF:' + IntToStr (RefAuftrag)], errtxt, Preview, kopie, PrintModule.LSLaserPrinter.BinNr);
                      end;
                    end;
                  end;

                  if (res <> 0) then begin
                    if (Length (ErrorMsg) > 0) then ErrorMsg := ErrorMsg + #13;
                    ErrorMsg := ErrorMsg + FormatMessageText (1595, [errtxt]);
                  end;
                end;

                if (res = 0) then begin
                  //Prüfen, ob eine Rechnung gedruckt werden soll (OPT_PRINT_INVOICE > 0)
                  if (shipinfo.OptPrintInvoice > '0') then begin
                    //Anzahl der Kopien bestimmen
                    if TryStrToInt (shipinfo.OptPrintInvoice, kopie) then
                      kopie := kopie - 1
                    else kopie := 0;

                    res := PrintModule.PrintReport ('Rechnung für Auftrag: '+query.FieldByName ('AUFTRAG_NR').AsString, PrintModule.RELaserPrinter.Port, '', -1, query.FieldByName('REF_DOCU_MAND').AsInteger, query.FieldByName('REF_LIEFLAGER').AsInteger, query.FieldByName('SPRACHE').AsString, shipinfo.InvoiceForm, query.FieldByName ('FORMULAR_KENNZEICHEN').AsString, ['REF:' + query.FieldByName('REF').AsString], errtxt, Preview, kopie, PrintModule.RELaserPrinter.BinNr);

                    if (res = 0) then
                      reprint := true
                    else begin
                      if (Length (ErrorMsg) > 0) then ErrorMsg := ErrorMsg + #13;
                      ErrorMsg := ErrorMsg + FormatMessageText (1593, [errtxt]);
                    end;
                  end;
                end;

                if (res = 0) then begin
                  if (Copy (query.FieldByName('MAND_CONFIG_OPT').AsString, cMandRetourenSchein, 1) > '0') then
                    res := PrintModule.PrintReport ('Retourenschein für: ' + query.FieldByName ('AUFTRAG_NR').AsString, PrintModule.StdLaserPrinter.Port, '', -1, query.FieldByName('REF_DOCU_MAND').AsInteger, query.FieldByName('REF_LIEFLAGER').AsInteger, query.FieldByName('SPRACHE').AsString, shipinfo.ReturnForm, query.FieldByName('FORMULAR_KENNZEICHEN').AsString, ['REF:' + IntToStr (RefAuftrag)], errtxt, Preview, -1, PrintModule.StdLaserPrinter.BinNr)
                  else if (Copy (query.FieldByName('OPT_RETOUREN_SCHEIN').AsString, 2, 1) = '1') then
                    res := PrintModule.PrintReport ('Retourenschein für: ' + query.FieldByName ('AUFTRAG_NR').AsString, PrintModule.StdLaserPrinter.Port, '', -1, query.FieldByName('REF_DOCU_MAND').AsInteger, query.FieldByName('REF_LIEFLAGER').AsInteger, query.FieldByName('SPRACHE').AsString, shipinfo.ReturnForm, query.FieldByName('FORMULAR_KENNZEICHEN').AsString, ['REF:' + IntToStr (RefAuftrag)], errtxt, Preview, -1, PrintModule.StdLaserPrinter.BinNr);

                  if (res <> 0) then begin
                    if (Length (ErrorMsg) > 0) then ErrorMsg := ErrorMsg + #13;
                    ErrorMsg := ErrorMsg + FormatMessageText (1594, [errtxt])
                  end else if (shipinfo.OptNVEContent > '0') then begin
                    if (LSPrint or (PackPrint and (query.FieldByName('OPT_AUTO_PRINT_NVE_INHALT').AsString = '0'))) then begin
                      //Die Paketinhaltsliste drucken, nur wenn die nicht schon automatisch beim Verpacken gedruckt wurden
                      if (shipinfo.OptNVEContent > '0') and (shipinfo.OptNVEContent <= '9') then
                        prttype := 'LASER'
                      else if (shipinfo.OptNVEContent >= 'B') and (shipinfo.OptNVEContent <= 'J') then
                        prttype := 'LABEL'
                      else
                        prttype := shipinfo.OptNVEContent;

                      if (Length (prttype) > 0) then begin
                        nvequery := LVSDatenModul.CreateSmartQuery (Nil, 'VersandPapiereNachdruck_nve');

                        try
                          nvequery.Close;

                          nvequery.SQL.Clear;
                          nvequery.SQL.Add ('select nve.REF as REF_NVE, nve.NVE_NR from V_NVE_01 nve where nve.STATUS<>''DEL'' and nve.REF_AUF_KOPF=:ref_auf order by nve.PACKAGE_NR');
                          nvequery.Params.ParamByName('ref_auf').Value := query.FieldByName ('REF').AsInteger;

                          nvequery.Open;

                          while not (nvequery.Eof) and (res = 0) do begin
                            if (prttype = 'LASER') then
                              res := PrintModule.PrintReport ('NVE Inhaltsliste : ' + nvequery.FieldByName ('NVE_NR').AsString, PrintModule.StdLaserPrinter.Port, '', -1, query.FieldByName('REF_DOCU_MAND').AsInteger, query.FieldByName('REF_LIEFLAGER').AsInteger, query.FieldByName('SPRACHE').AsString, shipinfo.SCCContForm, '', ['REF:' + nvequery.FieldByName('REF_NVE').AsString], errtxt, Preview, -1, PrintModule.StdLaserPrinter.BinNr)
                            else begin
                              if (PrintModule.NVELabelPrinter.Ref > 0) then
                                res := PrintModule.PrintReport ('NVE Inhaltsliste : ' + nvequery.FieldByName ('NVE_NR').AsString, GetLabelPrinterPort (PrintModule.NVELabelPrinter.Ref), '', -1, nvequery.FieldByName('REF_DOCU_MAND').AsInteger, nvequery.FieldByName('REF_LIEFLAGER').AsInteger, nvequery.FieldByName('SPRACHE').AsString, shipinfo.SCCContForm+'_LABEL', '', ['REF:' + nvequery.FieldByName('REF_NVE').AsString], errtxt, Preview, -1, -1)
                              else
                                errtxt := FormatMessageText (1789, []);
                            end;
                            nvequery.Next;
                          end;

                          nvequery.Close;

                          if (res <> 0) then begin
                            if (Length (ErrorMsg) > 0) then ErrorMsg := ErrorMsg + #13;
                            ErrorMsg := ErrorMsg + 'Fehler beim Drucken der Inhaltsliste' + #13 + #13 + errtxt;
                          end;
                        finally
                          nvequery.Free;
                        end;
                      end;
                    end;
                  end;
                end;
              end;
            end;
          except
            on  E: Exception do begin
              res := -9;
              ErrorTrackingModule.WriteErrorLog ('VersandPapiereNachdruck', e.ClassName + ' : ' + e.Message+#13+#10+query.SQL.Text);

              if (Length (ErrorMsg) > 0) then ErrorMsg := ErrorMsg + #13;
              ErrorMsg := ErrorMsg + 'Fehler beim Drucken der Papiere'+#13+#13+e.Message;
            end;
          end;
        end;
      end;

      oldflag := true;

      if (res = 0) then begin
        if LVSDatenModul.ViewExits('V_AUFTRAG_DMS_DOCUMENT') then begin
          dmsquery := LVSDatenModul.CreateSmartQuery (Nil, 'VersandPapiereNachdruck_dms');

          try
            dmsquery.SQL.Add ('select * from V_AUFTRAG_DMS_DOCUMENT where REF_AUF_KOPF=:ref_auf and DOC_TYPE not in (''SHIPPING_LABEL'',''LIEFERSCHEIN'')');
            dmsquery.Params.ParamByName('ref_auf').Value := query.FieldByName ('REF').AsInteger;

            oldflag := false;

            try
              dmsquery.Open;

              if (dmsquery.RecordCount = 0) then
                oldflag := true
              else begin
                docidx := 0;

                while not (dmsquery.Eof) and (res = 0) do begin
                  Inc (docidx);

                  //Das wären die Amazon-Dokumente von Everstox
                  res := FindDokument (dmsquery.FieldByName ('REF_DMS_FILE').AsInteger, comstr, fname, dmsref);

                  if (dmsref <> -1) then begin
                    if not FileExists (fname) then begin
                      if (Length (ErrorMsg) > 0) then ErrorMsg := ErrorMsg + #13;
                      ErrorMsg := ErrorMsg + 'Dokument '+ExtractFilename (fname)+' im Archiv nicht vorhanden';
                    end else begin
                      if Assigned (dmsquery.FindField ('DOC_TITLE')) then
                        comstr := dmsquery.FieldByName('DOC_TITLE').AsString
                      else if Assigned (dmsquery.FindField ('DOC_HINT')) then
                        comstr := dmsquery.FieldByName('DOC_HINT').AsString;

                      if (Length (comstr) = 0) then
                        comstr := 'Pack-Dokument '+ IntToStr (docidx);


                      if (dmsquery.FieldByName('OUTPUT_TYPE').AsString = 'PAGE') then begin
                        prtref   := -1;
                        prtdaten := PrintModule.LSLaserPrinter;
                      end else if (dmsquery.FieldByName('OUTPUT_TYPE').AsString = 'LABEL') then
                        res := PrintModule.DetectPrinter ('PAKET-LABEL', query.FieldByName('REF_LAGER').AsInteger, prtref, prtdaten)
                      else
                        res := PrintModule.DetectPrinter ('PAKET-LABEL', query.FieldByName('REF_LAGER').AsInteger, prtref, prtdaten);

                      if (res <> 0) or (Length (prtdaten.Port) = 0) then begin
                        prtdaten.Port  := PrintModule.RELaserPrinter.Port;
                        prtdaten.BinNr := PrintModule.RELaserPrinter.BinNr;
                      end;

                      if Preview then begin
                        PrintModule.AddPDFPreview (comstr, fname, 1, prtdaten.BinNr, RefAuftrag);
                      end else begin
                        try
                          if (prtdaten.BinNr = -1) then
                            res := PrintPDFDokument (prtdaten.Port, fname, 1, '')
                          else
                            res := PrintPDFDokument (prtdaten.Port, fname, 1, '#'+IntToStr (prtdaten.BinNr));
                        except
                          on  E: Exception do begin
                            res := -9;
                            ErrorTrackingModule.WriteErrorLog ('Exception PrintPDFDokument (FBA)', e.ClassName + ' : ' + e.Message);

                            if (Length (ErrorMsg) > 0) then ErrorMsg := ErrorMsg + #13;
                            ErrorMsg := ErrorMsg + 'Fehler beim Drucken der FBA PDFs';
                          end;
                        end;
                      end;
                    end;
                  end;

                  dmsquery.Next;
                end;
              end;

              dmsquery.Close;
            except
              oldflag := true;
            end;
          finally
            dmsquery.Free;
          end;
        end;
      end;

      if (res = 0) and oldflag then begin
        //Das wären die Amazon-Dokumente von Everstox
        res := FindDokument (query.FieldByName ('REF_DOCU_MAND').AsInteger, query.FieldByName ('AUFTRAG_NR').AsString, 'AMAZON_LABEL', comstr, fname, dmsref);

        if (dmsref <> -1) then begin
          if not FileExists (fname) then begin
            if (Length (ErrorMsg) > 0) then ErrorMsg := ErrorMsg + #13;
            ErrorMsg := ErrorMsg + 'Dokument '+ExtractFilename (fname)+' im Archiv nicht vorhanden';
          end else begin
            res := PrintModule.DetectPrinter ('PAKET-LABEL', query.FieldByName('REF_LAGER').AsInteger, prtref, prtdaten);
            if (res <> 0) or (Length (prtdaten.Port) = 0) then begin
              prtdaten.Port  := PrintModule.RELaserPrinter.Port;
              prtdaten.BinNr := PrintModule.RELaserPrinter.BinNr;
            end;

            if Preview then begin
              PrintModule.AddPDFPreview (comstr, fname, 1, prtdaten.BinNr, RefAuftrag);
            end else begin
              try
                if (prtdaten.BinNr = -1) then
                  res := PrintPDFDokument (prtdaten.Port, fname, 1, '')
                else
                  res := PrintPDFDokument (prtdaten.Port, fname, 1, '#'+IntToStr (prtdaten.BinNr));
              except
                on  E: Exception do begin
                  res := -9;
                  ErrorTrackingModule.WriteErrorLog ('Exception PrintPDFDokument (FBA)', e.ClassName + ' : ' + e.Message);

                  if (Length (ErrorMsg) > 0) then ErrorMsg := ErrorMsg + #13;
                  ErrorMsg := ErrorMsg + 'Fehler beim Drucken der FBA PDFs';
                end;
              end;
            end;
          end;
        end;

        res := FindDokument (query.FieldByName ('REF_DOCU_MAND').AsInteger,
                             query.FieldByName ('AUFTRAG_NR').AsString,
                             'PACK',
                             comstr, fname, dmsref);

        if (dmsref <> -1) then begin
          if not FileExists (fname) then begin
            if (Length (ErrorMsg) > 0) then ErrorMsg := ErrorMsg + #13;
            ErrorMsg := ErrorMsg + 'Dokument '+ExtractFilename (fname)+' im Archiv nicht vorhanden';
          end else begin
            res := PrintModule.DetectPrinter ('PAKET-LABEL', query.FieldByName('REF_LAGER').AsInteger, prtref, prtdaten);
            if (res <> 0) or (Length (prtdaten.Port) = 0) then begin
              prtdaten.Port  := PrintModule.RELaserPrinter.Port;
              prtdaten.BinNr := PrintModule.RELaserPrinter.BinNr;
            end;

            if Preview then begin
              PrintModule.AddPDFPreview (comstr, fname, 1, prtdaten.BinNr, RefAuftrag);
            end else begin
              if (prtdaten.BinNr = -1) then
                res := PrintPDFDokument (prtdaten.Port, fname, 1, '')
              else
                res := PrintPDFDokument (prtdaten.Port, fname, 1, '#'+IntToStr (prtdaten.BinNr));
            end;
          end;
        end;

        res := FindDokument (query.FieldByName ('REF_DOCU_MAND').AsInteger,
                             query.FieldByName ('AUFTRAG_NR').AsString,
                             'FBA',
                             comstr, fname, dmsref);

        if (dmsref <> -1) then begin
          if not FileExists (fname) then begin
            if (Length (ErrorMsg) > 0) then ErrorMsg := ErrorMsg + #13;
            ErrorMsg := ErrorMsg + 'Dokument '+ExtractFilename (fname)+' im Archiv nicht vorhanden';
          end else begin
            res := PrintModule.DetectPrinter ('PAKET-LABEL', query.FieldByName('REF_LAGER').AsInteger, prtref, prtdaten);
            if (res <> 0) or (Length (prtdaten.Port) = 0) then begin
              prtdaten.Port  := PrintModule.RELaserPrinter.Port;
              prtdaten.BinNr := PrintModule.RELaserPrinter.BinNr;
            end;

            if Preview then begin
              PrintModule.AddPDFPreview (comstr, fname, 1, prtdaten.BinNr, RefAuftrag);
            end else begin
              if (prtdaten.BinNr = -1) then
                res := PrintPDFDokument (prtdaten.Port, fname, 1, '')
              else
                res := PrintPDFDokument (prtdaten.Port, fname, 1, '#'+IntToStr (prtdaten.BinNr));
            end;
          end;
        end;
      end;

      if (res = 0) and not (AufPrint) then begin
        //Emmasbox Anlieferbeleg
        if (UpperCase (query.FieldByName ('VERSAND_ART').AsString) = 'EMMASBOX') then begin
          //Aber nur, wenn der Auftrag schon abgeschlossen ist
          if (query.FieldByName ('STATUS').AsString = 'ABG') then begin
            res := PrintModule.PrintReport ('Emmasbox für Auftrag: '+query.FieldByName ('AUFTRAG_NR').AsString, PrintModule.LSLaserPrinter.Port, '', -1, query.FieldByName('REF_DOCU_MAND').AsInteger, query.FieldByName('REF_LIEFLAGER').AsInteger, query.FieldByName('SPRACHE').AsString, 'EMMASBOX-DELIVERY', '', ['REF:' + IntToStr (RefAuftrag)], errtxt, Preview, kopie, PrintModule.StdLaserPrinter.BinNr);

            if (res <> 0) then begin
              if (Length (ErrorMsg) > 0) then ErrorMsg := ErrorMsg + #13;
              ErrorMsg := ErrorMsg + 'Fehler beim Drucken des Anlieferbeleges' + #13 + #13 + errtxt;
            end;
          end;
        end;

        nvequery := LVSDatenModul.CreateSmartQuery (Nil, 'VersandPapiereNachdruck_new');

        try
          //Wenn ein gesamte Proformarechnung gedruckt werden soll
          if fexp and (shipinfo.OptPrintZoll > '0') and (shipinfo.OptPrintProforma > '0') then begin
            if TryStrToInt (shipinfo.OptPrintProforma, kopie) then
              kopie := kopie - 1
            else kopie := 0;

            //Erst die Proformarechung suchen
            res := FindWaWiProforma (query.FieldByName ('REF_DOCU_MAND').AsInteger,
                                     query.FieldByName ('AUFTRAG_NR').AsString,
                                     comstr, fname, dmsref);

            //Hier nochmas gezielt die Rechnung suchen
            if (dmsref = -1) then begin
              res := FindWaWiRechnung (query.FieldByName ('REF_DOCU_MAND').AsInteger,
                                       query.FieldByName ('AUFTRAG_NR').AsString,
                                       comstr, fname, dmsref);
            end;

            //Bei Export muss eine Rechnung, Proformarechung oder Lieferschein gedruckt werden
            if (dmsref <> -1) then begin
              if Preview then
                {$ifdef UsePDF}
                  PrintModule.PreviewForm.AddPDFPreview ('Rechnung für Export', fname, RefAuftrag)
                {$endif}
              else begin
                if (PrintModule.ZollLaserPrinter.BinNr = -1) then
                  res := PrintPDFDokument (PrintModule.RELaserPrinter.Port, fname, kopie, '')
                else
                  res := PrintPDFDokument (PrintModule.RELaserPrinter.Port, fname, kopie, '#'+IntToStr (PrintModule.RELaserPrinter.BinNr));

                if (res <> 0) then
                  ErrorMsg := FormatMessageText (1593, [errtxt]);
              end;
            end else if not reprint then begin
              res := PrintModule.PrintReport ('Export-Proformarechung für Auftrag: '+query.FieldByName ('AUFTRAG_NR').AsString, PrintModule.RELaserPrinter.Port, '', -1, query.FieldByName('REF_DOCU_MAND').AsInteger, query.FieldByName('REF_LIEFLAGER').AsInteger, query.FieldByName('SPRACHE').AsString, shipinfo.ProformaForm, query.FieldByName ('FORMULAR_KENNZEICHEN').AsString, ['REF:' + query.FieldByName('REF').AsString], errtxt, Preview, kopie, PrintModule.RELaserPrinter.BinNr);

              if (res <> 0) then begin
                if (Length (ErrorMsg) > 0) then ErrorMsg := ErrorMsg + #13;
                ErrorMsg := ErrorMsg + 'Fehler beim Drucken der Proformarechung' + #13 + #13 + errtxt;
              end;
            end;
          end;

          //Wenn ein CMR gedruckt werden soll
          if fexp and ((shipinfo.OptPrintZoll = #0) or (shipinfo.OptPrintZoll > '0')) and (shipinfo.OptPrintCMR > '0') then begin
            if TryStrToInt (shipinfo.OptPrintCMR, kopie) then
              kopie := kopie - 1
            else kopie := 0;

            res := PrintModule.PrintReport ('CMR für Auftrag: '+query.FieldByName ('AUFTRAG_NR').AsString, PrintModule.ZollLaserPrinter.Port, '', -1, query.FieldByName('REF_DOCU_MAND').AsInteger, query.FieldByName('REF_LIEFLAGER').AsInteger, query.FieldByName('SPRACHE').AsString, shipinfo.CMRForm, query.FieldByName ('FORMULAR_KENNZEICHEN').AsString, ['REF:' + query.FieldByName('REF').AsString], errtxt, Preview, kopie, PrintModule.ZollLaserPrinter.BinNr);

            if (res <> 0) then begin
              if (Length (ErrorMsg) > 0) then ErrorMsg := ErrorMsg + #13;
              ErrorMsg := ErrorMsg + 'Fehler beim Drucken des CMRs' + #13 + #13 + errtxt;
            end;
          end;

          //Nur bei Versand ins nicht EU Ausland
          if fexp or (shipinfo.OptPrintCN23 > '0') then begin
            nvequery.SQL.Clear;
            nvequery.SQL.Add ('select nve.REF as REF_NVE, nve.REF_AUF_KOPF'
                            + ' ,spedcfg.OPT_CN23, aufcfg.OPT_PRINT_ZOLL_DOC'
                            + ' ,spedcfg.OPT_PROFORMA as SPED_OPT_PROFORMA'
                            + ' ,aufcfg.OPT_PROFORMA as AUF_OPT_PROFORMA'
                            + ' ,nvl (prod.OPT_NACHNAHME,av.OPT_NACHNAHME) as OPT_NACHNAHME'
                            + ' ,sped.DFUE_KENNZEICHEN'
                            + ' ,nve.PACKAGE_NR'
                            + ' ,(select max (PACKAGE_NR) from V_NVE_01 where STATUS<>''DEL'' and REF_AUF_KOPF=:ref_auf) as MAX_PACKAGE_NR'
                            + ' from V_NVE_01 nve'
                            + ' inner join VQ_AUFTRAG auf on (auf.REF=nve.REF_AUF_KOPF)'
                            + ' inner join V_AUFTRAG_VERSAND av on (av.REF_AUF_KOPF=auf.REF)'
                            + ' left outer join V_AUFTRAG_ART_CONFIG aufcfg on (aufcfg.REF=auf.REF_ART_CONFIG)'
                            + ' left outer join V_SPEDITIONEN sped on (sped.REF=nve.REF_SPED)'
                            + ' left outer join V_SPED_CONFIG spedcfg on (spedcfg.REF_SPED=nve.REF_SPED)'
                            + ' left outer join V_SPED_PRODUKTE prod on (prod.REF=nve.REF_SPED_PRODUKT)'
                            + ' where'
                            + '   nve.STATUS<>''DEL'' and nve.REF_AUF_KOPF=:ref_auf'
                            + ' order by nve.PACKAGE_NR');
            nvequery.Params.ParamByName('ref_auf').Value := query.FieldByName ('REF').AsInteger;

            nvequery.Open;

            while not (nvequery.Eof) and (res = 0) do begin
              //Wenn ein CS23 Zollformular gedruckt werden soll
              if not NVEPrint and ((shipinfo.OptPrintCN23 > '0') or ((nvequery.FieldByName ('OPT_CN23').AsString > '0') and not (IsLandEU (query.FieldByName ('LAND_ISO').AsString, query.FieldByName ('PLZ').AsString)))) then begin
                if ((shipinfo.OptPrintCN23 > '0') and TryStrToInt (shipinfo.OptPrintCN23, kopie)) then
                  kopie := kopie - 1
                else if TryStrToInt (nvequery.FieldByName ('OPT_CN23').AsString, kopie) then
                  kopie := kopie - 1
                else kopie := 0;

                res := PrintModule.PrintReport ('Zolldeklaration CN23 für Packstück: '+nvequery.FieldByName ('PACKAGE_NR').AsString, PrintModule.ZollLaserPrinter.Port, '', -1, query.FieldByName('REF_DOCU_MAND').AsInteger, query.FieldByName('REF_LIEFLAGER').AsInteger, query.FieldByName('SPRACHE').AsString, shipinfo.CN23Form, query.FieldByName ('FORMULAR_KENNZEICHEN').AsString, ['REF:' + nvequery.FieldByName('REF_NVE').AsString], errtxt, Preview, kopie, PrintModule.ZollLaserPrinter.BinNr);

                if (res <> 0) then begin
                  if (Length (ErrorMsg) > 0) then ErrorMsg := ErrorMsg + #13;
                  ErrorMsg := ErrorMsg + 'Fehler beim Drucken der Zolldeklaration' + #13 + #13 + errtxt;
                end;
              end;

              //Wenn überhaupt Zolldokumente erzeugt werden sollen
              if not NVEPrint and (((nvequery.FieldByName ('OPT_PRINT_ZOLL_DOC').AsString > '0') and not (IsLandEU (query.FieldByName ('LAND_ISO').AsString, query.FieldByName ('PLZ').AsString)))) then begin
                //Wenn die Proforma pro NVE in der Spedition nicht explizit ausgeschalten ist
                if not (shipinfo.OptPrintZoll > '0') and (nvequery.FieldByName ('SPED_OPT_PROFORMA').IsNull or (nvequery.FieldByName ('SPED_OPT_PROFORMA').AsString > '0') or (nvequery.FieldByName ('AUF_OPT_PROFORMA').AsString > '0')) then begin
                  //Anzahl der Kopien bestimmen
                  if (nvequery.FieldByName ('AUF_OPT_PROFORMA').AsString > '0') and TryStrToInt (nvequery.FieldByName ('AUF_OPT_PROFORMA').AsString, kopie) then
                    kopie := kopie - 1
                  else if (nvequery.FieldByName ('SPED_OPT_PROFORMA').AsString > '0') and TryStrToInt (nvequery.FieldByName ('SPED_OPT_PROFORMA').AsString, kopie) then
                    kopie := kopie - 1
                  else kopie := 0;

                  //Erst die Proformarechung suchen
                  res := FindWaWiProforma (query.FieldByName ('REF_DOCU_MAND').AsInteger,
                                           query.FieldByName ('AUFTRAG_NR').AsString,
                                           comstr, fname, dmsref);

                  //Hier nochmas gezielt die Rechnung suchen
                  if (dmsref = -1) then begin
                    res := FindWaWiRechnung (query.FieldByName ('REF_DOCU_MAND').AsInteger,
                                             query.FieldByName ('AUFTRAG_NR').AsString,
                                             comstr, fname, dmsref);
                  end;

                  //Bei Export muss eine Rechnung, Proformarechung oder Lieferschein gedruckt werden
                  if (dmsref <> -1) then begin
                    if Preview then
                      {$ifdef UsePDF}
                        PrintModule.PreviewForm.AddPDFPreview ('Rechnung für Export', fname, RefAuftrag)
                      {$endif}
                    else begin
                      if (PrintModule.ZollLaserPrinter.BinNr = -1) then
                        res := PrintPDFDokument (PrintModule.RELaserPrinter.Port, fname, kopie, '')
                      else
                        res := PrintPDFDokument (PrintModule.RELaserPrinter.Port, fname, kopie, '#'+IntToStr (PrintModule.RELaserPrinter.BinNr));

                      if (res <> 0) then
                        ErrorMsg := FormatMessageText (1593, [errtxt]);
                    end;
                  end else if not reprint then begin
                    if (nvequery.FieldByName ('SPED_OPT_PROFORMA').AsString > '0') or (nvequery.FieldByName ('AUF_OPT_PROFORMA').AsString > '0') then begin
                      res := PrintModule.PrintReport ('Export-Proformarechung für Packstück: '+nvequery.FieldByName ('PACKAGE_NR').AsString, PrintModule.RELaserPrinter.Port, '', -1, query.FieldByName('REF_DOCU_MAND').AsInteger, query.FieldByName('REF_LIEFLAGER').AsInteger, query.FieldByName('SPRACHE').AsString, shipinfo.ProformaForm, query.FieldByName ('FORMULAR_KENNZEICHEN').AsString, ['REF:' + nvequery.FieldByName('REF_NVE').AsString], errtxt, Preview, kopie, PrintModule.RELaserPrinter.BinNr);

                      if (res <> 0) then begin
                        if (Length (ErrorMsg) > 0) then ErrorMsg := ErrorMsg + #13;
                        ErrorMsg := ErrorMsg + 'Fehler beim Drucken der Proformarechung' + #13 + #13 + errtxt;
                      end;
                    end else if (nvequery.FieldByName ('PACKAGE_NR').AsInteger = nvequery.FieldByName ('MAX_PACKAGE_NR').AsInteger) then begin
                      res := PrintModule.PrintReport ('Lieferschein für Export', PrintModule.LSLaserPrinter.Port, '', -1, query.FieldByName('REF_DOCU_MAND').AsInteger, query.FieldByName('REF_LIEFLAGER').AsInteger, query.FieldByName('SPRACHE').AsString, shipinfo.DeliveryForm, query.FieldByName('FORMULAR_KENNZEICHEN').AsString, ['REF:' + nvequery.FieldByName('REF_AUF_KOPF').AsString], errtxt, Preview, kopie, PrintModule.LSLaserPrinter.BinNr);

                      if (res <> 0) then begin
                        if (Length (ErrorMsg) > 0) then ErrorMsg := ErrorMsg + #13;
                        ErrorMsg := ErrorMsg + FormatMessageText (1594, [errtxt]);
                      end;
                    end;
                  end;
                end;
              end;

              //Bei Nachnahme den Zahlbeleg für die erste NVE, bei DHL im Ausland immer
              if (nvequery.RecNo = 1) and (nvequery.FieldByName('OPT_NACHNAHME').AsString > '0') and ((query.FieldByName('OPT_NACHNAHME_BELEG').AsString = '1') or (nvequery.FieldByName ('DFUE_KENNZEICHEN').AsString='DHL')) then begin
                if (query.FieldByName ('BRUTTO_BETRAG').IsNull) then begin
                  res := 35;

                  if (Length (ErrorMsg) > 0) then ErrorMsg := ErrorMsg + #13;
                  ErrorMsg := ErrorMsg + 'Bei Nachnahme ist ein Rechnungsbetrag notwendig';
                end else if (query.FieldByName ('IBAN').IsNull) then begin
                  res := 35;

                  if (Length (ErrorMsg) > 0) then ErrorMsg := ErrorMsg + #13;
                  ErrorMsg := ErrorMsg + 'Bei Nachnahme muss für den Mandanten eine Backverbindung eingetragen sein';
                end else begin
                  res := PrintModule.PrintReport ('Nachnahme-Zahlbeleg für: ' + query.FieldByName ('AUFTRAG_NR').AsString, PrintModule.StdLaserPrinter.Port, '', -1, query.FieldByName('REF_DOCU_MAND').AsInteger, query.FieldByName('REF_LIEFLAGER').AsInteger, query.FieldByName('SPRACHE').AsString, 'NACHNAHME_ZAHLSCHEIN', query.FieldByName('FORMULAR_KENNZEICHEN').AsString, ['REF:' + IntToStr (RefAuftrag)], errtxt, Preview, -1, PrintModule.StdLaserPrinter.BinNr);

                  if (res <> 0) then begin
                    if (Length (ErrorMsg) > 0) then ErrorMsg := ErrorMsg + #13;
                    ErrorMsg := ErrorMsg + 'Fehler beim Drucken des Nachnahme-Zahlbelegs' + #13 + #13 + errtxt;
                  end;
                end;
              end;

              nvequery.Next;
            end;

            nvequery.Close;
          end;
        finally
          nvequery.Free;
        end;

        query.Close;
      end;
    except
      on  E: Exception do begin
        res := -9;
        ErrorTrackingModule.WriteErrorLog ('Exception VersandPapiereNachdruck', e.ClassName + ' : ' + e.Message+#13+#10+query.SQL.Text);

        ErrorMsg := 'Fehler beim Lesen der Auftragsdaten';
      end;
    end;

    Screen.Cursor := crDefault;
  finally
    query.Free;
  end;

  Result := res;
end;

//****************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//****************************************************************************
//* Description
//****************************************************************************
//* Return Value :
//****************************************************************************
function WaitResponse (const VersandApp, FileName : String; var RespName : String; const WaitTimeout : Integer) : Integer;
var
  ok        : boolean;
  fres,
  waittime,
  timeout   : Integer;
  srec      : TSearchRec;
  //fdata     : TWin32FindData;
  //fh        : THandle;
begin
  {$ifdef Trace}
    FunctionStart ('WaitResponse');
    TraceParameter ('VersandApp', VersandApp);
    TraceParameter ('FileName  ', FileName);
  {$endif}

  if not (DirectoryExists (SendITServerPath + 'Export\')) then begin
    RespName := '';
    Result := -1;
  end else begin
    if (WaitTimeout > 0) then
      waittime := WaitTimeout * 2
    else waittime := 2*3*60;

    //Warten auf die Rückmeldung
    ok := False;
    timeout := 0;

    repeat
      fres := FindFirst (SendITServerPath + 'Export\*' + FileName + '*.*', faArchive, srec);

      {$ifdef Trace}
        TraceResult ('fres', fres);
      {$endif}

      if (fres <> 0) then begin
        if (VersandApp = BARCODE_SHIPPING) then begin
          fres := FindFirst (SendITServerPath + 'Import\error\*' + FileName + '*.csv.error', faArchive, srec);

          {$ifdef Trace}
            TraceResult ('fres', fres);
          {$endif}

          if (fres = 0) then begin
            ok := true;

            {$ifdef Trace}
              //TraceResult ('fdata.cFileName', fdata.cFileName);
              TraceResult ('srec.Name', srec.Name);
            {$endif}

            RespName := SendITServerPath + 'Import\error\' + srec.Name;

            FindClose (srec);
          end;
        end;

        if (fres <> 0) then begin
          Inc (timeout);

          Application.ProcessMessages;

          Sleep (500);
        end;
      end else begin
        ok := true;

        {$ifdef Trace}
          //TraceResult ('fdata.cFileName', fdata.cFileName);
          TraceResult ('srec.Name', srec.Name);
        {$endif}

        RespName := SendITServerPath + 'Export\' + srec.Name;

        FindClose (srec);
      end;
    until (ok or (timeout > waittime));  //Bis zu drei Minuten warten, wenn der DPD-Tagesabschluss läuft

    Result := 0;

    {$ifdef Trace}
      TraceResult ('RespName', RespName);
      FunctionStop ('WaitResponse');
    {$endif}
  end;
end;

//****************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//****************************************************************************
//* Description
//****************************************************************************
//* Return Value :
//****************************************************************************
function MoveResponse (const FileName : String; const ErrorFlag : Boolean) : Integer;
var
  ok       : Boolean;
  retry    : integer;
  fnewname : String;
begin
  ok    := false;
  retry := 0;

  fnewname := ExtractFileName (FileName);
  fnewname := Copy (fnewname, 1, Length (fnewname) - 4) + '_' + FormatDateTime ('yyyymmdd_hhnnss', Now)+'.csv';

  if not (ErrorFlag) then begin
    if not (DirectoryExists (SendITServerPath + 'Export\Save')) then
      CreateDir (SendITServerPath + 'Export\Save');

    if FileExists (SendITServerPath + 'Export\Save\'+fnewname) then
      DeleteFile (SendITServerPath + 'Export\Save\'+fnewname);

    while not (ok) and (retry < 2) do begin
     if (RenameFile (FileName, SendITServerPath + 'Export\Save\'+fnewname)) then
       ok := true
     else begin
       ErrorTrackingModule.WriteErrorLogNoDB ('MoveResponse', 'Datei '+fnewname+' kann nicht nach ' +SendITServerPath + 'Export\Save'+' verschoben werden: '+GetAPIErrorMessage (GetLastError));

       Inc (retry);
       Sleep (250);
     end;
    end;
  end else begin
    if not (DirectoryExists (SendITServerPath + 'Export\Error')) then
      CreateDir (SendITServerPath + 'Export\Error');

    if FileExists (SendITServerPath + 'Export\Error\'+fnewname) then
      DeleteFile (SendITServerPath + 'Export\Error\'+fnewname);

    while not (ok) and (retry < 2) do begin
      if (RenameFile (FileName, SendITServerPath + 'Export\Error\'+fnewname)) then
        ok := true
      else begin
        ErrorTrackingModule.WriteErrorLogNoDB ('MoveResponse', 'Datei '+fnewname+' kann nicht nach ' +SendITServerPath + 'Export\Error'+' verschoben werden: '+GetAPIErrorMessage (GetLastError));

        Inc (retry);
        Sleep (250);
      end;
    end;
  end;

  Result := 0;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function StartCreateVersandInfos (AOwner: TComponent; const RefNVE : Integer; const PrtInfo : TPrinterPorts; var VersandApp : String; var ResponsFlag : Boolean; var ErrorText : String) : Integer;
var
  res,
  kopie,
  strpos,
  lblref,
  dmsref,
  refsend   : integer;
  fname,
  retnr,
  sendnr,
  sendid,
  fdesc,
  barstr,
  prodstr,
  sendurl   : String;
  returl,
  retsendnr,
  retsendid,
  retbarstr : String;
  sqlstr,
  comstr,
  pdfname,
  prtfname,
  lblname,
  lblformat : String;
  lbldata   : TMemoryStream;
  done,
  retflag     : Boolean;
  query       : TSmartQuery;
  dmsquery    : TSmartQuery;
  dataquery   : TSmartQuery;
  shipinfo    : TShippingInfos;
begin
  res := 0;

  RequesteFileName     := '';
  RequesteRetFileName  := '';

  CreateVersandLabel.DatenPath := LVSConfigModul.GetSessionDataDir;

  if (LVSConfigModul.DoSendITLog) then begin
    if not Assigned (SendITLog) then
      SendITLog := TLogFile.Create;

    if Assigned (SendITLog) then
      SendITLog.LogFileName := LVSConfigModul.GetSessionLogDir + 'sendit.log';
  end else begin
    if Assigned (SendITLog) then begin
      SendITLog.Free;
      SendITLog := Nil;
    end;
  end;

  query := TSmartQuery.Create (Nil);

  try
    query.ReadOnly := True;
    query.Session := LVSDatenModul.OraMainSession;

    sqlstr := '';

    if UseNVESendungsID then
      sqlstr := ',nve.SENDUNGS_ID'
    else
      sqlstr := ',null as SENDUNGS_ID';

    if UseAufConfigFirstImage then
      sqlstr := sqlstr + ',nvl (aufcfg.OPT_CHECK_FIRST_IMAGE, cfg.OPT_CHECK_FIRST_IMAGE) as OPT_CHECK_FIRST_IMAGE'
    else
      sqlstr := sqlstr + ',cfg.OPT_CHECK_FIRST_IMAGE';

    if UseAvisHinweis then
      sqlstr := sqlstr + ',av.AVIS_HINWEIS'
    else
      sqlstr := sqlstr + ',null as AVIS_HINWEIS';

    if UseExportDescription then
      sqlstr := sqlstr + ',auftxt.CARRIER_EXPORT_DESCRIPTION'
    else
      sqlstr := sqlstr + ',null as CARRIER_EXPORT_DESCRIPTION';


    query.SQL.Add ('select nve.REF as REF_NVE, a.REF as REF_AUF_KOPF, sped.REF as REF_SPED, av.SPEDITION_NR, sped.DFUE_ART, a.REF_MAND, a.REF_SUB_MAND, a.MANDANT, nvl (a.REF_SUB_MAND,a.REF_MAND) as REF_DOCU_MAND'
                  +',nvl (a.REF_LAGER, nve.REF_LAGER) as REF_LAGER, nvl (nve.REF_MAIN_NVE,nve.REF) as REF_MAIN_NVE, loc.REF_LOCATION, nvl (adr.LAND_ISO, ''DE'') as LIEFER_LAND_ISO, nve.REF_LT_TYP'
                  +',nvl (a.REF_LIEFLAGER, nve.REF_LAGER) as REF_LIEFLAGER, a.AUFTRAG_NR, a.AUF_REFERENZ, a.KD_KOMM_NR,a.LIEFERSCHEIN_NR,a.REF_KUNDEN_ADR,a.DRUCKART,nve.REF_WA as REF_NVE_WA'
                  +',a.REF_LIEFER_ADR,sped.LABEL_ART,nve.BRUTTO_GEWICHT, nve.NVE_NR, nvl (prod.OPT_NACHNAHME, av.OPT_NACHNAHME) as OPT_NACHNAHME, nve.SENDUNGS_NR, nve.PACKAGE_NR, nve.DATE_PRINT, lif.AUSLIEFER_NR'
                  +',nvl (versplan.OPT_RETOUREN_SCHEIN, aufplan.OPT_RETOUREN_SCHEIN) as OPT_RETOUREN_SCHEIN, ret.RETOUREN_NR,cfg.CSV_EXPORT_PATH,lt.EDI_CODE,m.CONFIG_OPT as MAND_CONFIG_OPT,av.OPT_RETOUREN_LABEL'
                  +',ret.SENDUNGS_NR as RET_SENDUNGS_NR,nvl (re.RECHNUNGS_NR, ''inv_''||a.AUFTRAG_NR) as RECHNUNGS_NR,a.VERSAND_DATUM,av.VERSAND_ART,sped.IFC_KENNZEICHEN,prod.REF as REF_SPED_PRODUKT,prod.SENDIT_PRODUKT,prod.PRODUKT_ID,a.KUNDEN_NR,aufplan.OPT_PRINT_NVE'
                  +',sped.NAME as SPED_NAME,sped.DFUE_KENNZEICHEN,nve.OPT_SPERRGUT,nve.L,nve.B,nve.H,nve.VOLUMEN,route.GATEWAY,gate.SENDIT_RETURN_CLIENT,cfg.OPT_DIM_REQUIRED,cfg.OPT_SPERRGUT_AS_NORMAL,cfg.OPT_INLAND_RETURN,cfg.OPT_AUSLAND_RETURN'
                  +',gate.SENDIT_LOCATION,nvl (prod.SENDIT_PRODUKT,gate.SENDIT_PRODUKT) as GATEWAY_SENDIT_PRODUKT,gate.SENDIT_PRODUKT_AUSLAND as GATEWAY_SENDIT_PRODUKT_AUS'
                  +',gate.SENDIT_RETURN_LOCATION,gate.SENDIT_CLIENT,gate.SENDIT_CONFIG,cfg.OPT_MULTI_SHIPMEND,aq.KD_BESTELL_NR'
                  +',re.NETTO_BETRAG,re.BRUTTO_BETRAG,m.ERP_ID as MANDANT_ERP_ID,re.CURRENCY,auftxt.VERSAND_HINWEIS,aufinfo.IFC_AUFTRAG_NR,a.AUFTRAGSART,a.LIEFER_DATUM,aufinfo.KD_AUFTRAG_NR'
                  + sqlstr
                  +',(select count (*) from V_NVE_01 where STATUS<>''DEL'' and ((sped.REF is null and REF_SPED is null) or (sped.REF=REF_SPED)) and REF_AUF_KOPF=a.REF) as PACKAGE_COUNT,aq.COUNT_FREIGABE'
                  +',(select sum (BRUTTO_GEWICHT) from V_NVE_01 where STATUS<>''DEL'' and ((sped.REF is null and REF_SPED is null) or (sped.REF=REF_SPED)) and REF_AUF_KOPF=a.REF) as SENDUNG_BRUTTO'
                  +',(select NAME from V_MANDANT where REF=a.REF_SUB_MAND) as SUB_MANDANT,gate.REF as REF_SPED_GATEWAY,gate.ZOLL_DEKLARATION,gate.NOTIFICATION_BY_MAIL,gate.DEFAULT_EMAIL_ADRESS,gate.DEFAULT_PHONE_NUMBER'
                  +' from'
                  +' V_NVE_01 nve'
                  +' inner join V_LT_TYPEN lt on (lt.REF=nve.REF_LT_TYP)'
                  +' left outer join V_AUFTRAG_01 a on (a.REF=nve.REF_AUF_KOPF)'
                  +' left outer join VQ_AUFTRAG aq on (aq.REF=a.REF)'
                  +' left outer join V_AUFTRAG_TEXTE auftxt on (auftxt.REF_AUF_KOPF=a.REF)'
                  +' left outer join V_MANDANT m on (m.REF=coalesce (a.REF_SUB_MAND, a.REF_MAND, nve.REF_MAND))'
                  +' left outer join V_AUFTRAG_VERSAND av on (av.REF_AUF_KOPF=a.REF)'
                  +' left outer join V_AUFTRAG_ADR adr on (adr.REF=a.REF_LIEFER_ADR)'
                  +' left outer join V_LIEFERUNG_01 lif on (lif.REF=a.REF_LIEFERUNG)'
                  +' left outer join V_AUFTRAG_RECHNUNG re on (re.REF_AUF_KOPF=a.REF)'
                  +' left outer join V_SPEDITIONEN sped on (sped.REF=nvl (nve.REF_SPED,av.REF_SPED))'
                  +' left outer join V_SPED_CONFIG cfg on (cfg.REF_SPED=sped.REF)'
                  +' left outer join V_SPED_GATEWAY gate on ((gate.REF_SUB_MAND is null or (gate.REF_SUB_MAND=a.REF_SUB_MAND)) and gate.REF_LAGER=nvl (a.REF_LAGER,nve.REF_LAGER) and gate.REF_SPED=sped.REF'
                  +' and (gate.LAND_ISO is null or (instr (gate.LAND_ISO, adr.LAND_ISO) > 0)) and (gate.REF_TRADER is null or gate.REF_TRADER=a.REF_TRADER))'
                  +' left outer join V_SPED_ROUTING route on (route.REF=nve.REF_ROUTING)'
                  //Das Produkt muss auch zum gewählten Versendet passen
                  +' left outer join V_SPED_PRODUKTE prod on (prod.REF_SPED=sped.REF and prod.REF=nvl (nve.REF_SPED_PRODUKT, av.REF_SPED_PRODUKT))'
                  +' left outer join V_RETOUREN_NR ret on (ret.REF_AUF_KOPF=a.REF and ret.STATUS<>''DEL'')'
                  +' left outer join V_AUFTRAG_ART_CONFIG aufcfg on (aufcfg.REF=aq.REF_ART_CONFIG)'
                  +' left outer join V_AUFTRAG_ART_PLANUNG aufplan on (aufplan.REF=a.REF_ART_PLANUNG)'
                  +' left outer join V_AUFTRAG_ART_VERSAND_CONFIG versplan on (versplan.REF=a.REF_AUF_VERS_CFG)'
                  +' left outer join V_AUFTRAG_ADD_INFOS aufinfo on (aufinfo.REF_AUF_KOPF=a.REF)'
                  +' left outer join V_LOCATION_REL_LAGER loc on (loc.REF_LAGER=a.REF_LAGER)'
                  +' where nve.REF=:RefNVE'
                  +' order by gate.LAND_ISO nulls last,gate.REF_SUB_MAND nulls last,gate.REF_TRADER nulls last'
                  );
    query.Params.ParamByName('RefNVE').Value := RefNVE;

    try
      query.Open;

      done := false;

      GetOrderShippingInfos (query.FieldByName ('REF_AUF_KOPF').AsInteger, shipinfo);

      //Beim ersten Karton, erst versuchen ein abgespeichertes Label zu drucken
      if (query.FieldByName ('LABEL_ART').AsString = 'IMAGE') or (query.FieldByName ('DFUE_ART').AsString = 'IMAGE') or (query.FieldByName ('OPT_CHECK_FIRST_IMAGE').AsString = '4') then begin
        dmsquery := TSmartQuery.Create (Nil);

        try
          dmsquery.ReadOnly := True;
          dmsquery.Session := LVSDatenModul.OraMainSession;

          dmsquery.SQL.Add ('select * from V_AUFTRAG_DMS_DOCUMENT where REF_AUF_KOPF=:ref_auf and DOC_TYPE=:doc_type');
          dmsquery.Params.ParamByName('ref_auf').Value := query.FieldByName ('REF_AUF_KOPF').AsInteger;
          dmsquery.Params.ParamByName('doc_type').Value := 'SHIPPING_LABEL';

          try
            dmsquery.Open;

            if (dmsquery.RecordCount = 0) then begin
              res := 11;
              ErrorText := 'Kein Label-Image für den Auftrag vorhanden';
            end else if (dmsquery.FieldByName ('REF_DMS_FILE').IsNull) then begin
              res := 11;
              ErrorText := 'Das Label-Image für den Auftrag wurde noch nicht übernommen';
            end else begin
              done := true;

              while not (dmsquery.Eof) and (res = 0) do begin
                res := FindDokument (dmsquery.FieldByName ('REF_DMS_FILE').AsInteger, comstr, fname, dmsref);

                if (dmsref <> -1) then begin
                  if not FileExists (fname) then begin
                    res := 12;

                    ErrorText := 'Druckendatei '+ExtractFileName(fname) + ' nicht vorhanden';
                    ErrorTrackingModule.WriteErrorLog ('StartCreateVersandInfos.DMS', 'Not found ' + fname);
                  end else begin
                    done := true;

                    if (LowerCase (ExtractFileExt(fname)) = '.pdf') then begin
                      res := PrintPDFDokument (PrtInfo.Port, fname, 1, '');

                      if (res <> 0) then begin
                        ErrorText := 'Fehler beim Drucken von '+ExtractFileName(fname);
                        ErrorTrackingModule.WriteErrorLog ('StartCreateVersandInfos.DMS.PDF', ErrorText + ' (' + fname+ ')');
                      end;
                    end else if (LowerCase (ExtractFileExt(fname)) = '.zpl') then begin
                      res := OpenPrinterPort (PrtInfo.Port, PrtInfo.Model, PrtInfo.FileOutput, PrtInfo.User, PrtInfo.Passwd, prtfname, ErrorText);

                      if (res = 0) then begin
                        res := BeginPrinting (ExtractFileName(fname), ErrorText);

                        if (res = 0) then begin
                          res := PrintFile (fname, ErrorText);

                          EndPrinting;
                        end;

                        ClosePrinterPort;
                      end;

                      if (res <> 0) then
                        ErrorTrackingModule.WriteErrorLog ('StartCreateVersandInfos.DMS.ZPL', ErrorText + ' (' + fname+ ')');
                    end else begin
                      res := PrintGraphiLabel (PrtInfo, fname, sendnr, sendurl, ErrorText);

                      if (res <> 0) then
                        ErrorTrackingModule.WriteErrorLog ('StartCreateVersandInfos.Image', ErrorText + ' (' + fname+ ')');
                    end;
                  end;
                end;

                dmsquery.Next;
              end;

              dmsquery.Close;

              if (res = 0) then
                res := SetNVEGedruckt (RefNVE);
            end;
          except
            on  E: Exception do begin
              res := -9;
              ErrorText := 'Fehler bei der DMS (PRINT)'+#13+#13+e.Message;

              ErrorTrackingModule.WriteErrorLog ('Exception StartCreateVersandInfos.DMS', e.ClassName + ' : ' + e.Message);
            end;
          end;
        finally
          dmsquery.Free;
        end;
      end else if (query.FieldByName ('OPT_CHECK_FIRST_IMAGE').AsString > '0') and (query.FieldByName ('PACKAGE_NR').AsInteger = 1) then begin
        fname := '';

        //OPT_CHECK_FIRST_IMAGE=1 : Auftragsnr, OPT_CHECK_FIRST_IMAGE=2 : Bestellnr, OPT_CHECK_FIRST_IMAGE=3 : AUF_REFERENZ
        if (query.FieldByName ('OPT_CHECK_FIRST_IMAGE').AsString = '1') and not query.FieldByName ('AUFTRAG_NR').IsNull then
          fname := DatenPath + 'LabelImage\' + query.FieldByName ('AUFTRAG_NR').AsString+'.png'
        else if (query.FieldByName ('OPT_CHECK_FIRST_IMAGE').AsString = '2') and not query.FieldByName ('KD_BESTELL_NR').IsNull then
          fname := DatenPath + 'LabelImage\' + query.FieldByName ('KD_BESTELL_NR').AsString+'.png'
        else if (query.FieldByName ('OPT_CHECK_FIRST_IMAGE').AsString = '3') and not query.FieldByName ('AUF_REFERENZ').IsNull then
          fname := DatenPath + 'LabelImage\' + query.FieldByName ('AUF_REFERENZ').AsString+'.png';

        if not (done) and (Length (fname) > 0) then begin
          res := PrintGraphiLabel (PrtInfo, fname, sendnr, sendurl, ErrorText);

          if (res = 0) then begin
            done := true;

            res := SetNVEGedruckt (RefNVE);

            if (res = 0) and (Length (sendnr) > 0) then
              res := SetNVESendungsNrURL (RefNVE, 'IMAGE', query.FieldByName('DFUE_KENNZEICHEN').AsString, sendnr, sendurl);
          end;
        end;
      end;

      //Wenn keins da war, eins erzeugen
      if not done and (res = 0) then begin
        if not (query.FieldByName ('DFUE_ART').IsNull) and query.FieldByName ('REF_SPED_GATEWAY').IsNull then begin
          res := -48;
          ErrorText := FormatMessageText (1812, []);
        end else begin
          done := false;
          ResponsFlag := false;

          LabelUserNumID  := LVSDatenModul.AktUserNumID;
          LabelClientName := LVSDatenModul.AktClientName;

          refauf := query.FieldByName ('REF_AUF_KOPF').AsInteger;

          Screen.Cursor := crHourGlass;

          if (query.FieldByName ('DFUE_ART').AsString ='SENDIT') or (query.FieldByName ('DFUE_ART').AsString ='SENDIT-PRT') then begin
            CheckConfigParameter (query.FieldByName('REF_MAND').AsInteger, -1, query.FieldByName('REF_LIEFLAGER').AsInteger, 'SENDIT_CSV_URL', SendITServerURL);

            if not (query.FieldByName ('CSV_EXPORT_PATH').IsNull) then
              SendITServerPath := query.FieldByName ('CSV_EXPORT_PATH').AsString
            else
              CheckConfigParameter (query.FieldByName('REF_MAND').AsInteger, -1, query.FieldByName('REF_LIEFLAGER').AsInteger, 'SENDIT_CSV_PATH', SendITServerPath);

            if (Length (SendITServerPath) > 0) and (SendITServerPath [Length (SendITServerPath)] <> '\') then SendITServerPath := SendITServerPath + '\';


            if (not (query.FieldByName ('LIEFER_LAND_ISO').IsNull or (query.FieldByName ('LIEFER_LAND_ISO').AsString = 'DE'))) then begin
              dataquery := TSmartQuery.Create (Nil);

              try
                dataquery.ReadOnly := True;
                dataquery.Session := LVSDatenModul.OraMainSession;

                dataquery.SQL.Add ('select * from V_SPED_GATEWAY where REF=:ref_gate');
                dataquery.Params.ParamByName('ref_gate').Value := query.FieldByName ('REF_SPED_GATEWAY').AsInteger;

                dataquery.Open;

                //Erzeugen der Proformarechung zum Upload, nur ausserhalb DE
                if Assigned (dataquery.FindField('OPT_PAPER_UPLOAD')) then begin  //Wenn Tippfehler im Spaltennamen
                  pdfname := SendITServerPath + 'SendIT.Service\Documents\'+query.FieldByName ('RECHNUNGS_NR').AsString+'.pdf';

                  if (copy (dataquery.FieldByName('OPT_PAPER_UPLOAD').AsString,2,1) = '2') then begin
                    if FileExists (pdfname) then begin
                      if not DeleteFile (pdfname) then
                        PrintLogging ('Fehler beim Löschen der Upload Proformarechung (Auftrag): '+pdfname);
                    end;

                    //Proforma pro Auftrag
                    res := PrintModule.PrintReport ('KEP-Proforma-Rechnung für Auftrag: '+query.FieldByName ('AUFTRAG_NR').AsString, PrintModule.RELaserPrinter.Port, pdfname, -1, query.FieldByName('REF_DOCU_MAND').AsInteger, query.FieldByName('REF_LAGER').AsInteger, '', shipinfo.ProformaForm, '', ['REF:' + query.FieldByName('REF_AUF_KOPF').AsString, 'REF_NVE:' + query.FieldByName('REF_NVE').AsString, 'REF_AUF_KOPF:' + query.FieldByName('REF_AUF_KOPF').AsString], ErrorText, false, 0, -1)
                  end else if (copy (dataquery.FieldByName('OPT_PAPER_UPLOAD').AsString,2,1) = '1') then begin
                    if FileExists (pdfname) then begin
                      if not DeleteFile (pdfname) then
                        PrintLogging ('Fehler beim Löschen der Upload Proformarechung (NVE): '+pdfname);
                    end;

                    //Proforma pro NVE
                    res := PrintModule.PrintReport ('Proforma-Rechnung für NVE: '+query.FieldByName ('NVE_NR').AsString, PrintModule.RELaserPrinter.Port, pdfname, -1, query.FieldByName('REF_DOCU_MAND').AsInteger, query.FieldByName('REF_LAGER').AsInteger, '', shipinfo.ProformaForm, '', ['REF:' + query.FieldByName('REF_NVE').AsString, 'REF_NVE:' + query.FieldByName('REF_NVE').AsString, 'REF_AUF_KOPF:' + query.FieldByName('REF_AUF_KOPF').AsString], ErrorText, false, 0, -1);
                  end;
                end else if Assigned (dataquery.FindField('OPT_PAPER_UPLAOD')) then begin
                  if (copy (dataquery.FieldByName('OPT_PAPER_UPLAOD').AsString,2,1) = '2') then begin
                    if FileExists (pdfname) then begin
                      if not DeleteFile (pdfname) then
                        PrintLogging ('Fehler beim Löschen der Upload Proformarechung (Auftrag): '+pdfname);
                    end;

                    //Proforma pro Auftrag
                    res := PrintModule.PrintReport ('KEP-Proforma-Rechnung für Auftrag: '+query.FieldByName ('AUFTRAG_NR').AsString, PrintModule.RELaserPrinter.Port, SendITServerPath + 'SendIT.Service\Documents\'+query.FieldByName ('RECHNUNGS_NR').AsString+'.pdf', -1, query.FieldByName('REF_DOCU_MAND').AsInteger, query.FieldByName('REF_LAGER').AsInteger, '', shipinfo.ProformaForm, '', ['REF:' + query.FieldByName('REF_AUF_KOPF').AsString, 'REF_NVE:' + query.FieldByName('REF_NVE').AsString, 'REF_AUF_KOPF:' + query.FieldByName('REF_AUF_KOPF').AsString], ErrorText, false, 0, -1)
                  end else if (copy (dataquery.FieldByName('OPT_PAPER_UPLAOD').AsString,2,1) = '1') then begin
                    if FileExists (pdfname) then begin
                      if not DeleteFile (pdfname) then
                        PrintLogging ('Fehler beim Löschen der Upload Proformarechung (NVE): '+pdfname);
                    end;

                    //Proforma pro NVE
                    res := PrintModule.PrintReport ('Proforma-Rechnung für NVE: '+query.FieldByName ('NVE_NR').AsString, PrintModule.RELaserPrinter.Port, SendITServerPath + 'SendIT.Service\Documents\'+query.FieldByName ('RECHNUNGS_NR').AsString+'.pdf', -1, query.FieldByName('REF_DOCU_MAND').AsInteger, query.FieldByName('REF_LAGER').AsInteger, '', shipinfo.ProformaForm, '', ['REF:' + query.FieldByName('REF_NVE').AsString, 'REF_NVE:' + query.FieldByName('REF_NVE').AsString, 'REF_AUF_KOPF:' + query.FieldByName('REF_AUF_KOPF').AsString], ErrorText, false, 0, -1);
                  end;

                  if (res <> 0) then
                    PrintLogging ('Fehler '+IntToStr (res)+' beim Erzeugen der Upload Proformarechung '+pdfname+': '+ErrorText);
                end;

                dataquery.Close;
              finally
                dataquery.Free;
              end;
            end;
          end else if (query.FieldByName ('DFUE_ART').AsString ='BARSHIP_CSV') then begin
            if not (query.FieldByName ('CSV_EXPORT_PATH').IsNull) then
              SendITServerPath := query.FieldByName ('CSV_EXPORT_PATH').AsString
            else
              CheckConfigParameter (query.FieldByName('REF_MAND').AsInteger, -1, query.FieldByName('REF_LIEFLAGER').AsInteger, 'BARSHIP_CSV_PATH', SendITServerPath);

            if (SendITServerPath [Length (SendITServerPath)] <> '\') then SendITServerPath := SendITServerPath + '\';
          end;

          if (res = 0)  then begin
            lbldata := TMemoryStream.Create;

            try
              if (query.FieldByName ('DFUE_ART').AsString = 'EXPORTO-API') then
                res := CreateExportoVersand (query, LVSConfigModul.GetSessionDataDir, '', PrtInfo, done, ResponsFlag, VersandApp, prodstr, sendid, sendnr, sendurl, barstr, lblformat, lbldata, lblref, ErrorText)
              else
                res := StartPrintVersandLabel (query, '', PrtInfo, done, ResponsFlag, VersandApp, prodstr, sendid, sendnr, sendurl, barstr, lblformat, lbldata, lblref, ErrorText);

              if done then begin
                if (res <> 0) then begin
                  if UseNVEVersandError and (Length (ErrorText) > 0) then
                    SetNVEVersandError (RefNVE, query.FieldByName ('LABEL_ART').AsString, prodstr, ErrorText);
                end else begin
                  if (Length (sendnr) > 0) and (sendnr <> query.FieldByName ('SENDUNGS_NR').AsString) then
                    res := SetNVESendungsNrURL (query.FieldByName('REF_NVE').AsInteger, VersandApp, prodstr, sendnr, sendurl, barstr, refsend);

                  if ((res = 0) and (lbldata.Size > 0) and (lblref <= 0)) then
                    res := StoreNVELabel (query.FieldByName('REF_NVE').AsInteger, query.FieldByName ('REF_SPED').AsInteger, query.FieldByName ('LABEL_ART').AsString, lblformat, lbldata, lblref);

                  if (res = 0) then begin
                    if (lbldata.Size > 0) and not ((PrtInfo.Name = 'SENDIT_DUMP') or (PrtInfo.Port = 'dump') or (PrtInfo.Port = 'pdf')) then begin
                      if (lblformat = 'pdf') then
                        res := PrintPDFDokument (PrtInfo.Port, lbldata, 1, '', ErrorText)
                      else begin
                        res := OpenPrinterPort (PrtInfo.Port, PrtInfo.Model, PrtInfo.FileOutput, PrtInfo.User, PrtInfo.Passwd, fname, ErrorText);
                        if (res = 0) then begin
                          res := BeginPrinting (query.FieldByName ('LABEL_ART').AsString + '\' + prodstr+'_'+sendnr, ErrorText);

                          if (res = 0) then begin
                            res := PrintFile (lbldata, ErrorText);

                            EndPrinting;
                          end;

                          ClosePrinterPort;
                        end;
                      end;
                    end;
                  end;

                  if (query.FieldByName ('DFUE_ART').AsString ='SENDIT') or (query.FieldByName ('DFUE_ART').AsString ='SENDIT-PRT') then begin
                    retflag := false;

                    //Prüfen, ob ein Retourenlabel ab 40 € reiner Warenwert gedruckt werden soll
                    if (not query.FieldByName ('PACKAGE_NR').IsNull and (query.FieldByName ('PACKAGE_NR').AsInteger = 1)) then begin
                      if (Copy (query.FieldByName('MAND_CONFIG_OPT').AsString, cMandRetourenLabel, 1) > '0') or
                         (Copy (query.FieldByName('OPT_RETOUREN_SCHEIN').AsString, 1, 1) = '2') or
                         (Copy (query.FieldByName('OPT_RETOUREN_LABEL').AsString, 1, 1) = '1')
                      then begin
                        //Bei OPT_RETOUREN_SCHEIN=2 wird immer ein Label erzeugt
                        if (query.FieldByName('LIEFER_LAND_ISO').AsString = 'DE') then begin
                          if (query.FieldByName ('OPT_INLAND_RETURN').AsString = '1') then
                            retflag := true;
                        end else begin
                          if (query.FieldByName ('OPT_AUSLAND_RETURN').AsString = '1') then
                            retflag := true;
                        end;
                      (*
                      end else if (Copy (query.FieldByName('OPT_RETOUREN_SCHEIN').AsString, 1, 1) = '1') and (query.FieldByName('LIEFER_LAND_ISO').AsString = 'DE') then begin
                        //Bei OPT_RETOUREN_SCHEIN=1 wird immer ein Label nur im Inland ab einem Warenwert erzeugt
                        if (query.FieldByName ('OPT_INLAND_RETURN').AsString = '1') then begin
                          //Nachnahme nur, wenn es auch einen Wert gibt
                          if (WarenWert > 0) and (query.FieldByName('OPT_NACHNAHME').AsString > '0') then begin
                            //Sendungswert - Versandkosten plus NN-Gebühr, aber ohne DHL NN-Gebühr
                            if (warenwert > (40.00 + (4.95 + 3.00))) then
                              RequesteRetFileName := 'pcd_r_' + LabelClientName + '_' + retnr;
                          end else begin
                            //Sendungswert - Versandkosten
                            if (warenwert > (40.00 + (4.95))) then
                              RequesteRetFileName := 'pcd_r_' + LabelClientName + '_' + retnr;
                          end;
                        end;
                      *)
                      end;

                      if retflag then begin
                        res := StartPrintVersandLabel (query, 'Return', PrtInfo, done, ResponsFlag, VersandApp, prodstr, retsendid, retsendnr, returl, retbarstr, lblformat, lbldata, lblref, ErrorText);

                        if (res <> 0) then
                          RequesteRetFileName := ''
                        else if (Length (retsendnr) > 0) then begin
                          RequesteRetFileName := '';

                          res := SetAuftragSendungsRetoure (query.FieldByName ('REF_AUF_KOPF').AsInteger, VersandApp, prodstr, retsendnr);

                          if ((res = 0) and (lbldata.Size > 0) and (lblref <= 0)) then
                            res := StoreNVELabel (-1, query.FieldByName('REF_AUF_KOPF').AsInteger, query.FieldByName ('REF_SPED').AsInteger, query.FieldByName ('LABEL_ART').AsString, 'RETURN', lblformat, lbldata, lblref);

                          if (res = 0) then begin
                            if (lbldata.Size > 0) and not ((PrtInfo.Name = 'SENDIT_DUMP') or (PrtInfo.Port = 'dump') or (PrtInfo.Port = 'pdf')) then begin
                              if (lblformat = 'pdf') then
                                res := PrintPDFDokument (PrtInfo.Port, lbldata, 1, '', ErrorText)
                              else begin
                                res := OpenPrinterPort (PrtInfo.Port, PrtInfo.Model, PrtInfo.FileOutput, PrtInfo.User, PrtInfo.Passwd, fname, ErrorText);
                                if (res = 0) then begin
                                  res := BeginPrinting (query.FieldByName ('LABEL_ART').AsString + '\' + prodstr+'_'+retsendnr, ErrorText);

                                  if (res = 0) then begin
                                    res := PrintFile (lbldata, ErrorText);

                                    EndPrinting;
                                  end;

                                  ClosePrinterPort;
                                end;
                              end;
                            end;
                          end;
                        end;
                      end;
                    end;
                  end;
                end;
              end else if (res = 0) then begin
                if not (query.FieldByName ('LABEL_ART').IsNull) then begin
                  if (query.FieldByName ('LABEL_ART').AsString = 'NO_LABEL') then begin
                  end else if ((copy (query.FieldByName ('LABEL_ART').AsString, 1, 13) = 'AMAZONE_PRIME') or (copy (query.FieldByName ('LABEL_ART').AsString, 1, 13) = 'AMAZON_PRIME')) then begin
                    fname := DatenPath + 'Prime\' + query.FieldByName ('AUFTRAG_NR').AsString+'.png';
                    res := PrintGraphiLabel (PrtInfo, fname, sendnr, sendurl, ErrorText);

                    if (res = 0) then begin
                      res := SetNVEGedruckt (query.FieldByName('REF_NVE').AsInteger);

                      if (res = 0) and (Length (sendnr) > 0) then
                        res := SetNVESendungsNrURL (query.FieldByName('REF_NVE').AsInteger, 'AMAZON_PRIME', 'AMAZON_PRIME', sendnr, sendurl);
                    end;
                  end else if (query.FieldByName ('LABEL_ART').AsString = 'NVE') then begin
                    if (query.FieldByName ('OPT_PRINT_NVE').AsString = '0') then
                    else begin
                      if TryStrToInt (query.FieldByName ('OPT_PRINT_NVE').AsString, kopie) then
                        Dec (kopie)
                      else kopie := -1;

                      if (query.FieldByName ('DATE_PRINT').IsNull) then begin
                        if (PrtInfo.PrtTyp = 'LASER') then begin
                          if CheckMischNVE (query.FieldByName('REF_NVE').AsInteger) then
                            res := PrintModule.PrintReport('', PrtInfo.Port, '', kopie, query.FieldByName ('REF_LAGER').AsInteger, query.FieldByName ('REF_MAND').AsInteger, '', 'LASER-NVE-MISCH', '', ['REF:' + query.FieldByName('REF_NVE').AsString], ErrorText, False)
                          else
                            res := PrintModule.PrintReport('', PrtInfo.Port, '', kopie, query.FieldByName ('REF_LAGER').AsInteger, query.FieldByName ('REF_MAND').AsInteger, '', 'LASER-NVE', '', ['REF:' + query.FieldByName('REF_NVE').AsString], ErrorText, False);
                        end else begin
                          res := CreateNVEPrintJob (query.FieldByName('REF_NVE').AsInteger, query.FieldByName ('REF_MAND').AsInteger, query.FieldByName ('REF_LAGER').AsInteger, kopie, PrtInfo.Name);

                          if (res <> 0) then
                            ErrorText := LVSDatenModul.LastSQLErrorText;
                        end;
                      end;
                    end;
                  end else if (copy (query.FieldByName ('LABEL_ART').AsString, 1, 4) = 'NVE_') then begin
                    if (query.FieldByName ('OPT_PRINT_NVE').AsString = '0') then
                    else begin
                      if TryStrToInt (query.FieldByName ('OPT_PRINT_NVE').AsString, kopie) then
                        Dec (kopie)
                      else kopie := -1;

                      if (query.FieldByName ('DATE_PRINT').IsNull) then begin
                        if (PrtInfo.PrtTyp = 'LASER') then begin
                          res := PrintModule.PrintReport('', PrtInfo.Port, '', kopie, query.FieldByName ('REF_LAGER').AsInteger, query.FieldByName ('REF_MAND').AsInteger, '', 'LASER-'+query.FieldByName ('LABEL_ART').AsString, '', ['REF:' + query.FieldByName('NVENr').AsString], ErrorText, False)
                        end else begin
                          res := CreateNVEPrintJob (query.FieldByName('REF_NVE').AsInteger, kopie, PrtInfo.Name, query.FieldByName ('LABEL_ART').AsString);

                          if (res <> 0) then
                            ErrorText := LVSDatenModul.LastSQLErrorText;
                        end;
                      end;
                    end;
                  end else if (copy (query.FieldByName ('LABEL_ART').AsString, 1, 5) = 'SPED_') then begin
                    res := CreateNVELabelPrintJob (query.FieldByName('REF_NVE').AsInteger, -1, PrtInfo.Name);

                    if (res <> 0) then
                      ErrorText := LVSDatenModul.LastSQLErrorText;
                  end else if (copy (query.FieldByName ('LABEL_ART').AsString, 1, 4) = 'KEP_') then begin
                    res := CreateVersandPrintJob (query.FieldByName('REF_NVE').AsInteger, '', -1, PrtInfo.Name);

                    if (res <> 0) then
                      ErrorText := LVSDatenModul.LastSQLErrorText;
                  end else begin
                    res := CreateAdrPrintJob (query.FieldByName('REF_NVE').AsInteger, -1, PrtInfo.Name, query.FieldByName ('LABEL_ART').AsString);

                    if (res <> 0) then
                      ErrorText := LVSDatenModul.LastSQLErrorText;
                  end;
                end else if (query.FieldByName ('DATE_PRINT').IsNull) then begin
                  if (PrtInfo.PrtTyp = 'LASER') then begin
                    if CheckMischNVE (query.FieldByName('REF_NVE').AsInteger) then
                      res := PrintModule.PrintReport('', PrtInfo.Port, '', -1, query.FieldByName ('REF_LAGER').AsInteger, query.FieldByName ('REF_MAND').AsInteger, '', 'LASER-NVE-MISCH', '', ['REF:' + query.FieldByName('NVENr').AsString], ErrorText, False)
                    else
                      res := PrintModule.PrintReport('', PrtInfo.Port, '', -1, query.FieldByName ('REF_LAGER').AsInteger, query.FieldByName ('REF_MAND').AsInteger, '', 'LASER-NVE', '', ['REF:' + query.FieldByName('NVENr').AsString], ErrorText, False);
                  end else begin
                    res := CreateNVEPrintJob (query.FieldByName('REF_NVE').AsInteger, query.FieldByName ('REF_MAND').AsInteger, query.FieldByName ('REF_LAGER').AsInteger, -1, PrtInfo.Name);

                    if (res <> 0) then
                      ErrorText := LVSDatenModul.LastSQLErrorText;
                  end;
                end;
              end;

              //Hier kann noch ein eigen Retouren-Adresslabel gedrucket werden
              if (res = 0) and (Length (query.FieldByName('OPT_RETOUREN_SCHEIN').AsString) > 2) and (Copy (query.FieldByName('OPT_RETOUREN_SCHEIN').AsString, 3, 1) = '1') then begin
                res := PrintRetourenLabel (PrtInfo, query.FieldByName('REF_AUF_KOPF').AsInteger, ErrorText);
              end;

              //Hier kann noch auftragsbezogen ein spezielles Zusatzlabel gedrucket werden
              if (res = 0) then begin
                strpos := Pos ('ZUSATZ_LABEL:', query.FieldByName('DRUCKART').AsString);

                if (res = 0) and (strpos > 0) then begin
                  lblname := copy (query.FieldByName('DRUCKART').AsString, strpos + Length ('ZUSATZ_LABEL:'));

                  strpos := Pos (';', lblname);

                  if (strpos > 0) then
                    lblname := copy (lblname, 1, strpos - 1);

                  res := PrintZusatzLabel (PrtInfo, lblname, query.FieldByName('REF_AUF_KOPF').AsInteger, ErrorText);
                end;
              end;
            finally
              lbldata.Free;

              Screen.Cursor := crDefault;
            end;
          end;
        end;
      end;

      query.Close;
    except
      on  E: Exception do begin
        res := -9;
        ErrorText := 'Fehler bei der Druckdatenaufbereitung (PRINT)';

        ErrorTrackingModule.WriteErrorLog ('Exception StartCreateVersandInfos', e.ClassName + ' : ' + e.Message);
      end;
    end;
  finally
    query.Free;
  end;

  Result := res;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function StartCreateRetoureLabel (AOwner: TComponent; const RefAuftrag : Integer; const PrtInfo : TPrinterPorts; var VersandApp, LabelFormat : String; var LabelStream : TMemoryStream; var ResponsFlag : Boolean; var ErrorText : String) : Integer;
var
  res,
  kopie,
  strpos,
  lblref,
  dmsref,
  refsend   : integer;
  fname,
  retnr,
  sendnr,
  sendid,
  fdesc,
  barstr,
  prodstr,
  sendurl   : String;
  returl,
  retsendnr,
  retsendid,
  retbarstr : String;
  sqlstr,
  comstr,
  prtfname,
  lblname   : String;
  done,
  retflag   : Boolean;
  query     : TSmartQuery;
  dmsquery  : TSmartQuery;
  dataquery : TSmartQuery;
begin
  res := 0;

  RequesteFileName     := '';
  RequesteRetFileName  := '';

  CreateVersandLabel.DatenPath := LVSConfigModul.GetSessionDataDir;

  if (LVSConfigModul.DoSendITLog) then begin
    if not Assigned (SendITLog) then
      SendITLog := TLogFile.Create;

    if Assigned (SendITLog) then
      SendITLog.LogFileName := LVSConfigModul.GetSessionLogDir + 'sendit.log';
  end else begin
    if Assigned (SendITLog) then begin
      SendITLog.Free;
      SendITLog := Nil;
    end;
  end;

  query := TSmartQuery.Create (Nil);

  try
    query.ReadOnly := True;
    query.Session := LVSDatenModul.OraMainSession;

    sqlstr := '';

    if UseNVESendungsID then
      sqlstr := ',nve.SENDUNGS_ID'
    else
      sqlstr := ',null as SENDUNGS_ID';

    if UseAufConfigFirstImage then
      sqlstr := sqlstr + ',nvl (aufcfg.OPT_CHECK_FIRST_IMAGE, cfg.OPT_CHECK_FIRST_IMAGE) as OPT_CHECK_FIRST_IMAGE'
    else
      sqlstr := sqlstr + ',cfg.OPT_CHECK_FIRST_IMAGE';

    if UseAvisHinweis then
      sqlstr := sqlstr + ',av.AVIS_HINWEIS'
    else
      sqlstr := sqlstr + ',null as AVIS_HINWEIS';

    query.SQL.Add ('select nve.REF as REF_NVE, a.REF as REF_AUF_KOPF, sped.REF as REF_SPED, av.SPEDITION_NR, sped.DFUE_ART, a.REF_MAND, a.REF_SUB_MAND, a.MANDANT, nvl (a.REF_SUB_MAND,a.REF_MAND) as REF_DOCU_MAND'
                  +',nvl (a.REF_LAGER, nve.REF_LAGER) as REF_LAGER, nvl (nve.REF_MAIN_NVE,nve.REF) as REF_MAIN_NVE, loc.REF_LOCATION, nvl (adr.LAND_ISO, ''DE'') as LIEFER_LAND_ISO, nve.REF_LT_TYP'
                  +',nvl (a.REF_LIEFLAGER, nve.REF_LAGER) as REF_LIEFLAGER, a.AUFTRAG_NR, a.AUF_REFERENZ, a.KD_KOMM_NR,a.LIEFERSCHEIN_NR,a.REF_KUNDEN_ADR,a.DRUCKART,nve.REF_WA as REF_NVE_WA'
                  +',a.REF_LIEFER_ADR,sped.LABEL_ART,nve.BRUTTO_GEWICHT, nve.NVE_NR, nvl (prod.OPT_NACHNAHME, av.OPT_NACHNAHME) as OPT_NACHNAHME, nve.SENDUNGS_NR, nve.PACKAGE_NR, nve.DATE_PRINT, lif.AUSLIEFER_NR'
                  +',nvl (versplan.OPT_RETOUREN_SCHEIN, aufplan.OPT_RETOUREN_SCHEIN) as OPT_RETOUREN_SCHEIN, ret.RETOUREN_NR,cfg.CSV_EXPORT_PATH,lt.EDI_CODE,m.CONFIG_OPT as MAND_CONFIG_OPT,av.OPT_RETOUREN_LABEL'
                  +',ret.SENDUNGS_NR as RET_SENDUNGS_NR,nvl (re.RECHNUNGS_NR, ''inv_''||a.AUFTRAG_NR) as RECHNUNGS_NR,a.VERSAND_DATUM,av.VERSAND_ART,sped.IFC_KENNZEICHEN,prod.REF as REF_SPED_PRODUKT,prod.SENDIT_PRODUKT,prod.PRODUKT_ID,a.KUNDEN_NR,aufplan.OPT_PRINT_NVE'
                  +',sped.NAME as SPED_NAME,sped.DFUE_KENNZEICHEN,nve.OPT_SPERRGUT,nve.L,nve.B,nve.H,nve.VOLUMEN,route.GATEWAY,gate.SENDIT_RETURN_CLIENT,cfg.OPT_DIM_REQUIRED,cfg.OPT_SPERRGUT_AS_NORMAL,cfg.OPT_INLAND_RETURN,cfg.OPT_AUSLAND_RETURN'
                  +',gate.SENDIT_LOCATION,nvl (prod.SENDIT_PRODUKT,gate.SENDIT_PRODUKT) as GATEWAY_SENDIT_PRODUKT,gate.SENDIT_PRODUKT_AUSLAND as GATEWAY_SENDIT_PRODUKT_AUS'
                  +',gate.SENDIT_RETURN_LOCATION,gate.SENDIT_CLIENT,gate.SENDIT_CONFIG,cfg.OPT_MULTI_SHIPMEND,aq.KD_BESTELL_NR'
                  +',re.NETTO_BETRAG,re.BRUTTO_BETRAG,m.ERP_ID as MANDANT_ERP_ID,re.CURRENCY,auftxt.VERSAND_HINWEIS,aufinfo.IFC_AUFTRAG_NR,a.AUFTRAGSART,a.LIEFER_DATUM,aufinfo.KD_AUFTRAG_NR'
                  + sqlstr
                  +',(select count (*) from V_NVE_01 where STATUS<>''DEL'' and ((sped.REF is null and REF_SPED is null) or (sped.REF=REF_SPED)) and REF_AUF_KOPF=a.REF) as PACKAGE_COUNT,aq.COUNT_FREIGABE'
                  +',(select sum (BRUTTO_GEWICHT) from V_NVE_01 where STATUS<>''DEL'' and ((sped.REF is null and REF_SPED is null) or (sped.REF=REF_SPED)) and REF_AUF_KOPF=a.REF) as SENDUNG_BRUTTO'
                  +',(select NAME from V_MANDANT where REF=a.REF_SUB_MAND) as SUB_MANDANT,gate.REF as REF_SPED_GATEWAY,gate.ZOLL_DEKLARATION,gate.NOTIFICATION_BY_MAIL,gate.DEFAULT_EMAIL_ADRESS,gate.DEFAULT_PHONE_NUMBER'
                  +' from'
                  +' V_NVE_01 nve'
                  +' inner join V_LT_TYPEN lt on (lt.REF=nve.REF_LT_TYP)'
                  +' left outer join V_AUFTRAG_01 a on (a.REF=nve.REF_AUF_KOPF)'
                  +' left outer join VQ_AUFTRAG aq on (aq.REF=a.REF)'
                  +' left outer join V_AUFTRAG_TEXTE auftxt on (auftxt.REF_AUF_KOPF=a.REF)'
                  +' left outer join V_MANDANT m on (m.REF=coalesce (a.REF_SUB_MAND, a.REF_MAND, nve.REF_MAND))'
                  +' left outer join V_AUFTRAG_VERSAND av on (av.REF_AUF_KOPF=a.REF)'
                  +' left outer join V_AUFTRAG_ADR adr on (adr.REF=a.REF_LIEFER_ADR)'
                  +' left outer join V_LIEFERUNG_01 lif on (lif.REF=a.REF_LIEFERUNG)'
                  +' left outer join V_AUFTRAG_RECHNUNG re on (re.REF_AUF_KOPF=a.REF)'
                  +' left outer join V_SPEDITIONEN sped on (sped.REF=nvl (nve.REF_SPED,av.REF_SPED))'
                  +' left outer join V_SPED_CONFIG cfg on (cfg.REF_SPED=sped.REF)'
                  +' left outer join V_SPED_GATEWAY gate on ((gate.REF_SUB_MAND is null or (gate.REF_SUB_MAND=a.REF_SUB_MAND)) and gate.REF_LAGER=nvl (a.REF_LAGER,nve.REF_LAGER) and gate.REF_SPED=sped.REF'
                  +' and (gate.LAND_ISO is null or (instr (gate.LAND_ISO, adr.LAND_ISO) > 0)) and (gate.REF_TRADER is null or gate.REF_TRADER=a.REF_TRADER))'
                  +' left outer join V_SPED_ROUTING route on (route.REF=nve.REF_ROUTING)'
                  //Das Produkt muss auch zum gewählten Versendet passen
                  +' left outer join V_SPED_PRODUKTE prod on (prod.REF_SPED=sped.REF and prod.REF=nvl (nve.REF_SPED_PRODUKT, av.REF_SPED_PRODUKT))'
                  +' left outer join V_RETOUREN_NR ret on (ret.REF_AUF_KOPF=a.REF and ret.STATUS<>''DEL'')'
                  +' left outer join V_AUFTRAG_ART_CONFIG aufcfg on (aufcfg.REF=aq.REF_ART_CONFIG)'
                  +' left outer join V_AUFTRAG_ART_PLANUNG aufplan on (aufplan.REF=a.REF_ART_PLANUNG)'
                  +' left outer join V_AUFTRAG_ART_VERSAND_CONFIG versplan on (versplan.REF=a.REF_AUF_VERS_CFG)'
                  +' left outer join V_AUFTRAG_ADD_INFOS aufinfo on (aufinfo.REF_AUF_KOPF=a.REF)'
                  +' left outer join V_LOCATION_REL_LAGER loc on (loc.REF_LAGER=a.REF_LAGER)'
                  +' where nve.REF_AUF_KOPF=:RefAuf'
                  +' order by gate.LAND_ISO nulls last,gate.REF_SUB_MAND nulls last,gate.REF_TRADER nulls last'
                  );
    query.Params.ParamByName('RefAuf').Value := RefAuftrag;

    try
      query.Open;

      if (query.RecordCount = 0) then begin
        res := 48;
        ErrorText := 'Für den Auftrag wurde noch keine Sendung erstellt';
      end else begin
        done := false;

        if not (query.FieldByName ('DFUE_ART').IsNull) and query.FieldByName ('REF_SPED_GATEWAY').IsNull then begin
          res := -48;
          ErrorText := FormatMessageText (1812, []);
        end else begin
          done := false;
          ResponsFlag := false;

          LabelUserNumID  := LVSDatenModul.AktUserNumID;
          LabelClientName := LVSDatenModul.AktClientName;

          refauf := query.FieldByName ('REF_AUF_KOPF').AsInteger;

          Screen.Cursor := crHourGlass;

          if (query.FieldByName ('DFUE_ART').AsString ='SENDIT') or (query.FieldByName ('DFUE_ART').AsString ='SENDIT-PRT') then begin
            CheckConfigParameter (query.FieldByName('REF_MAND').AsInteger, -1, query.FieldByName('REF_LIEFLAGER').AsInteger, 'SENDIT_CSV_URL', SendITServerURL);

            if not (query.FieldByName ('CSV_EXPORT_PATH').IsNull) then
              SendITServerPath := query.FieldByName ('CSV_EXPORT_PATH').AsString
            else
              CheckConfigParameter (query.FieldByName('REF_MAND').AsInteger, -1, query.FieldByName('REF_LIEFLAGER').AsInteger, 'SENDIT_CSV_PATH', SendITServerPath);

            if (Length (SendITServerPath) > 0) and (SendITServerPath [Length (SendITServerPath)] <> '\') then SendITServerPath := SendITServerPath + '\';
          end else if (query.FieldByName ('DFUE_ART').AsString ='BARSHIP_CSV') then begin
            if not (query.FieldByName ('CSV_EXPORT_PATH').IsNull) then
              SendITServerPath := query.FieldByName ('CSV_EXPORT_PATH').AsString
            else
              CheckConfigParameter (query.FieldByName('REF_MAND').AsInteger, -1, query.FieldByName('REF_LIEFLAGER').AsInteger, 'BARSHIP_CSV_PATH', SendITServerPath);

            if (SendITServerPath [Length (SendITServerPath)] <> '\') then SendITServerPath := SendITServerPath + '\';
          end;

          LabelStream := TMemoryStream.Create;

          try
            res := StartPrintVersandLabel (query, 'Return', PrtInfo, done, ResponsFlag, VersandApp, prodstr, retsendid, retsendnr, returl, retbarstr, LabelFormat, LabelStream, lblref, ErrorText);

            if (res <> 0) then
              RequesteRetFileName := ''
            else if (Length (retsendnr) > 0) then begin
              RequesteRetFileName := '';

              res := SetAuftragSendungsRetoure (query.FieldByName ('REF_AUF_KOPF').AsInteger, VersandApp, prodstr, retsendnr);

              if ((res = 0) and (LabelStream.Size > 0) and (lblref <= 0) and not query.FieldByName('REF_NVE').IsNull) then
                res := StoreNVELabel (query.FieldByName('REF_NVE').AsInteger, query.FieldByName('REF_AUF_KOPF').AsInteger, query.FieldByName ('REF_SPED').AsInteger, query.FieldByName ('LABEL_ART').AsString, 'RETURN', LabelFormat, LabelStream, lblref);
            end;
          finally
            Screen.Cursor := crDefault;
          end;
        end;
      end;

      query.Close;
    except
      on  E: Exception do begin
        res := -9;
        ErrorText := 'Fehler bei der Druckdatenaufbereitung (RETOURE)';

        ErrorTrackingModule.WriteErrorLog ('Exception StartCreateRetoureLabel', e.ClassName + ' : ' + e.Message);
      end;
    end;
  finally
    query.Free;
  end;

  Result := res;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function FinishCreateVersandInfos (AOwner: TComponent; const RefNVE : Integer; const VersandApp, Sped : String; var TrackingNr, ErrorText : String) : Integer;
var
  res,
  idx,
  fres         : Integer;
  readanz      : Integer;
  count        : Integer;
  timeout      : Integer;
  ref_send_nr  : Integer;
  iostr        : AnsiString;
  fstream,
  floadstream  : TFileStream;
  implist      : TStringList;
  srec         : TSearchRec;
  url,
  csvstr,
  errstr,
  fname,
  loadfname,
  retrspfname,
  errrspfname,
  sendrspfname : String;
  starttime    : Int64;
begin
  res := 0;

  ErrorText := '';
  TrackingNr := '';

  {$ifdef Trace}
    FunctionStart ('FinishCreateVersandInfos');
    TraceParameter ('RefNVE    ', RefNVE);
    TraceParameter ('VersandApp', VersandApp);
    TraceParameter ('Sped      ', Sped);
  {$endif}

  retrspfname  := '';
  sendrspfname := '';
  errrspfname  := '';

  if (Length (RequesteFileName) > 0) or (Length (RequesteRetFileName) > 0) then begin
    starttime := GetPerformanceCount;

    CheckConfigParameter (-1, LVSDatenModul.AktLocationRef, -1, 'SENDIT_CSV_TIMEOUT', timeout, -1);

    if Assigned (SendITLog) then begin
      SendITLog.Open;
      SendITLog.Write ('RefNVE:  '+IntToStr (RefNVE)+';Sped:'+Sped+CR+LF);
      SendITLog.Write ('Versand: '+RequesteFileName+CR+LF);

      if (Length (RequesteRetFileName) > 0) then
        SendITLog.Write ('Retoure: '+RequesteRetFileName+CR+LF);

      SendITLog.Close;
    end;

    infowin := TInfoWin.Create (AOwner);

    infowin.Caption := FormatResourceText (1236, [Sped]);
    infowin.Label1.Caption := GetResourceText (1237);

    infowin.BeginShowModal;

    {$ifdef Trace}
      TraceParameter ('SendITServerPath', SendITServerPath);
      TraceParameter ('RequesteFileName', RequesteFileName);
      TraceParameter ('RequesteRetFileName', RequesteRetFileName);
    {$endif}

    try
      Screen.Cursor := crHourGlass;

      try
        if (Length (RequesteFileName) > 0) then
          WaitResponse (VersandApp, RequesteFileName, sendrspfname, timeout);

        if (Length (RequesteRetFileName) > 0) then
          WaitResponse (VersandApp, RequesteRetFileName, retrspfname, timeout);
      finally
        Screen.Cursor := crDefault;
      end;
    finally
      infowin.EndShowModal;
      infowin.Free;
    end;

    Application.ProcessMessages;

    if Assigned (SendITLog) then begin
      SendITLog.Open;
      SendITLog.Write ('Versand: '+sendrspfname+CR+LF);

      if (Length (retrspfname) > 0) then
        SendITLog.Write ('Retoure: '+retrspfname+CR+LF);

      SendITLog.Close;
    end;

    if (Length (sendrspfname) = 0) then begin
      res := -25;
      ErrorText := 'Versand: Timeout beim '+VersandApp+' Rückmeldung';

      if Assigned (SendITLog) then begin
        SendITLog.Logging (clError, 'Versand: '+sendrspfname+', Error:'+ErrorText+CR+LF);
      end;
    end else begin
      count := 0;

      //Die Loop soll selten auftretende Lesefehler umgehen
      repeat
        try
          fstream := TFileStream.Create (sendrspfname, fmOpenRead + fmShareDenyWrite);
        except
          fstream := Nil;
        end;

        if not Assigned (fstream) and (ExtractFileExt (sendrspfname) <> '.csv') then begin
          Sleep (100);

          fname := ChangeFileExt (sendrspfname, '.csv');

          try
            fstream := TFileStream.Create (fname, fmOpenRead + fmShareDenyWrite);

            sendrspfname := fname;

            if Assigned (SendITLog) then begin
              SendITLog.Logging (clError, 'Versand: switch to '+fname);
            end;
          except
            fstream := Nil;
          end;

          if not Assigned (fstream) then begin
            fname := ChangeFileExt (sendrspfname, '');

            try
              fstream := TFileStream.Create (fname, fmOpenRead + fmShareDenyWrite);

              sendrspfname := fname;

              if Assigned (SendITLog) then begin
                SendITLog.Logging (clNormal, 'Versand: switch to '+fname);
              end;
            except
              fstream := Nil;
            end;
          end;
        end;

        if not Assigned (fstream) then begin
          Inc (count);
          Sleep (100);
        end;
      until (count > 10) or Assigned (fstream);

      if not Assigned (fstream) then begin
        res := -26;
        ErrorText := 'Versand: '+VersandApp+' Rückmeldung nicht lesbar';

        if Assigned (SendITLog) then begin
          SendITLog.Logging (clError, 'Versand: '+sendrspfname+', Error:'+ErrorText+CR+LF);
        end;
      end else begin
        try
          SetLength (iostr, 1024);

          readanz := fstream.Read (PChar(iostr)^, 1024);
          SetLength (iostr, readanz);

          {$ifdef Trace}
            TraceResult ('iostr', iostr);
          {$endif}

          implist := TStringList.Create;

          try
            implist.Delimiter := ';';
            implist.StrictDelimiter := True;

            implist.DelimitedText := iostr;

            {$ifdef Trace}
              TraceResult ('implist.Count', implist.Count);
            {$endif}

            if (VersandApp = SENDIT) then begin
              if (implist.Count < 6) then begin
                res := -27;
                ErrorText := 'Versand: Ungültige '+VersandApp+' Rückmeldung';
              end else begin
                //Fehler in der SendIT URL das &amp; stört da
                if ((implist.Count >= 17) and (copy (implist [4],1,4) = 'idc=') and (implist [12] = '0')) then begin
                  if (res = 0) then begin
                    if (copy (implist [3], Length (implist [3]) - 3) = '&amp') then
                      url := copy (implist [3], 1, Length (implist [3]) - 3) + implist [4]
                    else
                      url := implist [3];

                    if (Length (implist [0]) = 0) or (Length (url) = 0) then begin
                      TrackingNr := implist [2];

                      res := SetNVESendungsNr (RefNVE, VersandApp, Sped, implist [2])
                    end else if (implist.Count > 16) and (Length (implist [16]) > 0) then begin
                      TrackingNr := implist [2];

                      res := SetNVESendungsNrURL (RefNVE, VersandApp, implist [0], implist [2], url, implist [16], ref_send_nr)
                    end else begin
                      TrackingNr := implist [2];

                      res := SetNVESendungsNrURL (RefNVE, VersandApp, implist [0], implist [2], url);
                    end;

                    if (res <> 0) then
                      ErrorText := LVSDatenModul.LastLVSErrorText;
                  end;
                end else if ((implist.Count >= 17) and (implist [11] = '0')) then begin
                  if (res = 0) then begin
                    if (Length (implist [0]) = 0) or (Length (implist [3]) = 0) then begin
                      TrackingNr := implist [2];

                      res := SetNVESendungsNr (RefNVE, VersandApp, Sped, implist [2])
                    end else if (implist.Count > 15) and (Length (implist [15]) > 0) then begin
                      if (Length (implist [2]) > 0) then
                        TrackingNr := implist [2]
                      else if (Length (implist [15]) > 0) then
                        TrackingNr := implist [15]
                      else
                        TrackingNr := '';

                      res := SetNVESendungsNrURL (RefNVE, VersandApp, implist [0], TrackingNr, implist [3], implist [15], ref_send_nr)
                    end else begin
                      TrackingNr := implist [2];

                      res := SetNVESendungsNrURL (RefNVE, VersandApp, implist [0], implist [2], implist [3]);
                    end;

                    if (res <> 0) then
                      ErrorText := LVSDatenModul.LastLVSErrorText;
                  end;
                end else if ((implist.Count < 17) and (implist [11] = '0')) then begin
                  if (res = 0) then begin
                    if (Length (implist [3]) = 0) then begin
                      TrackingNr := implist [2];

                      res := SetNVESendungsNr (RefNVE, VersandApp, Sped, implist [2])
                    end else if (implist.Count > 15) and (Length (implist [15]) > 0) then begin
                      TrackingNr := implist [2];

                      res := SetNVESendungsNrURL (RefNVE, VersandApp, implist [0], implist [2], implist [3], implist [15], ref_send_nr)
                    end else begin
                      TrackingNr := implist [2];

                      res := SetNVESendungsNrURL (RefNVE, VersandApp, implist [0], implist [2], implist [3]);
                    end;

                    if (res <> 0) then
                      ErrorText := LVSDatenModul.LastLVSErrorText;
                  end;
                end else begin
                  res := -28;

                  ErrorText := 'Versand: '+VersandApp+' Fehler:'+#13+implist [12];

                  if (copy (ErrorText, 1, Length ('Error from GLS-Box')) = 'Error from GLS-Box') then begin
                    if (Pos ('E006', ErrorText) > 0) then begin
                      if (Pos ('T330', ErrorText) > 0) then
                        ErrorText := ErrorText +#13+#13+'Postleitzahl stimmt nicht'
                      else ErrorText := ErrorText +#13+#13+'Routing kann nicht ermittelt werden';
                    end;
                  end;
                end;
              end;
            end else if (VersandApp = BARCODE_SHIPPING) then begin
              if (ExtractFileExt(sendrspfname) = '.error') then begin
                res := -28;

                idx := 1;
                errstr := implist [0];

                while ((idx <= Length (errstr)) and (errstr [idx]<>#10) and (errstr [idx]<>#13)) do
                  Inc (idx);

                if (idx <= Length (errstr)) and ((errstr [idx] = #10) or (errstr [idx] = #13)) then
                  Dec (idx);

                errstr := Utf8ToAnsi (copy (errstr, 1, idx));

                ErrorText := 'Versand: '+VersandApp+' Fehler:'+#13+copy (errstr, 1, idx);
              end else if (implist.Count = 2) then begin
                TrackingNr := implist [0];

                if (Length (TrackingNr) > 0) then
                  res := SetNVESendungsNrURL (RefNVE, VersandApp, Sped, TrackingNr, implist [1], '', implist [1], ref_send_nr);
              end else if (implist.Count < 50) then begin
                res := -27;
                ErrorText := 'Versand: Ungültige '+VersandApp+' Rückmeldung';
              end else begin
                if (Length (implist [49]) > 0) then
                  TrackingNr := implist [49]
                else
                  TrackingNr := implist [1];

                if (Length (TrackingNr) > 0) then
                  res := SetNVESendungsNrURL (RefNVE, VersandApp, Sped, TrackingNr, '', implist [1], implist [1], ref_send_nr);
              end;
            end;
          finally
            implist.Free;
          end;
        finally
          fstream.Free;
        end;

        MoveResponse (sendrspfname, (res <> 0));

        if (res <> 0) and Assigned (SendITLog) then begin
          if (Length (ErrorText) = 0) then
            SendITLog.Logging (clError, 'Versand: '+sendrspfname+CR+LF)
          else
            SendITLog.Logging (clError, 'Versand: '+sendrspfname+', Error:'+ErrorText+CR+LF);
        end;
      end;
    end;

    if (Length (retrspfname) > 0) then begin
      fres := FindFirst (SendITServerPath + 'Export\pcd_d_ret*.*', faArchive, srec);

      if (fres = 0) then begin
        while (fres = 0) do begin
          DeleteFile (SendITServerPath + 'Export\' + srec.Name);

          fres := FindNext (srec);
        end;

        FindClose (srec);
      end;

      count := 0;

      //Die Loop soll selten auftretende Lesefehler umgehen
      repeat
        try
          fstream := TFileStream.Create (retrspfname, fmOpenRead + fmShareDenyWrite);
        except
          fstream := Nil;
        end;

        if not Assigned (fstream) and (ExtractFileExt (retrspfname) <> '.csv') then begin
          try
            fstream := TFileStream.Create (ChangeFileExt (retrspfname, '.csv'), fmOpenRead + fmShareDenyWrite);

            retrspfname := ChangeFileExt (retrspfname, '.csv');

            if Assigned (SendITLog) then begin
              SendITLog.Logging (clNormal, 'Retoure: switch to '+retrspfname);
            end;
          except
            fstream := Nil;
          end;
        end;

        if not Assigned (fstream) then begin
          Inc (count);
          Sleep (100);
        end;
      until (count > 10) or Assigned (fstream);

      if not Assigned (fstream) then begin
        res := -26;

        if (Length (ErrorText) > 0) then
          ErrorText := ErrorText + #13;

        ErrorText := ErrorText + 'Retoure: '+VersandApp+' Rückmeldung nicht lesbar';

        if Assigned (SendITLog) then begin
          SendITLog.Logging (clError, 'Retoure: '+retrspfname+', Error:'+ErrorText+CR+LF);
        end;
      end else begin
        try
          SetLength (iostr, 1024);

          readanz := fstream.Read (PChar(iostr)^, 1024);
          SetLength (iostr, readanz);

          {$ifdef Trace}
            TraceResult ('iostr', iostr);
          {$endif}

          implist := TStringList.Create;

          try
            implist.Delimiter := ';';
            implist.StrictDelimiter := True;

            implist.DelimitedText := iostr;

            {$ifdef Trace}
              TraceResult ('implist.Count', implist.Count);
            {$endif}

            if (implist.Count < 6) then begin
              res := -27;

              if (Length (ErrorText) > 0) then
                ErrorText := ErrorText + #13;

              ErrorText := ErrorText + 'Retoure: Ungültige '+VersandApp+' Rückmeldung';
            end else begin
              if (implist [11] = '0') then begin
                if (res = 0) then begin
                  res := SetAuftragSendungsRetoure (refauf, VersandApp, implist [0], implist [2]);

                  if (res <> 0) then begin
                    if (Length (ErrorText) > 0) then
                      ErrorText := ErrorText + #13;

                    ErrorText := ErrorText + LVSDatenModul.LastLVSErrorText;
                  end;
                end;
              end else begin
                res := -28;

                if (Length (ErrorText) > 0) then
                  ErrorText := ErrorText + #13;

                ErrorText := ErrorText + 'Retoure: '+VersandApp+' Fehler:'+#13+implist [12];
              end;
            end;
          finally
            implist.Free;
          end;
        finally
          fstream.Free;
        end;

        MoveResponse (retrspfname, (res <> 0));

        if (res <> 0) and Assigned (SendITLog) then begin
          if (Length (ErrorText) = 0) then
            SendITLog.Logging (clError, 'Retoure: '+retrspfname+CR+LF)
          else
            SendITLog.Logging (clError, 'Retoure: '+retrspfname+', Error:'+ErrorText+CR+LF);
        end;
      end;
    end;

    if Assigned (SendITLog) then
      SendITLog.Logging (clNormal, 'Sped:'+Sped+';TrackingNr:='+TrackingNr+';Dauer: '+IntToStr (GetPerformanceCountDiff (starttime) div 1000)+ ' ms');
  end;

  Result := res;

  {$ifdef Trace}
    TraceResult ('ErrorText', ErrorText);
    FunctionStop (Result);
  {$endif}
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function PrintVersandFehlerLabel  (AOwner: TComponent; const RefNVE : Integer; const PrtInfo : TPrinterPorts; const ErrorMsg : String; var ErrorText : String) : Integer;
var
  res          : Integer;
  query        : TADOQuery;
  fname,
  errtext      : String;
  forminfo     : TFormInfos;
  paramarray   : array [0..31] of AnsiString;
begin
  res := 0;
  ErrorText := '';

  {$ifdef Trace}
    FunctionStart ('PrintVersandFehlerLabel');
    TraceParameter ('RefNVE', RefNVE);
  {$endif}

  query := TADOQuery.Create (Nil);

  try
    query.LockType := ltReadOnly;
    query.Connection := LVSDatenModul.MainADOConnection;

    query.SQL.Add ('select a.*, nve.NVE_NR, adr.NAME1, adr.STRASSE, adr.ORT, adr.PLZ, adr.LAND, sped.NAME as SPED_NAME');
    query.SQL.Add ('from V_AUFTRAG_01 a inner join V_AUFTRAG_VERSAND av on (av.REF_AUF_KOPF=a.REF) inner join V_AUFTRAG_ADR adr on (adr.REF=a.REF_LIEFER_ADR) inner join V_LIEFERUNG_01 lif on (lif.REF=a.REF_LIEFERUNG)');
    query.SQL.Add ('inner join V_NVE_01 nve on (a.REF=nve.REF_AUF_KOPF) left outer join V_SPEDITIONEN sped on (sped.REF=nvl (nve.REF_SPED,av.REF_SPED)) where nve.REF=:ref_nve');
    query.Parameters.ParamByName('ref_nve').Value := RefNVE;

    try
      query.Open;

      res := DetectFormular (query.FieldByName ('REF_MAND').AsInteger, query.FieldByName ('REF_LAGER').AsInteger, -1, '', '', PrtInfo.Model, 'VERSAND_FEHLER', forminfo);

      if (res <> 0) Then begin
        ErrorText := FormatMessageText (1083, ['VERSAND_FEHLER', PrtInfo.Model])
      end else begin
        res := OpenPrinterPort (PrtInfo.Port, PrtInfo.Model, PrtInfo.FileOutput, PrtInfo.User, PrtInfo.Passwd, fname, errtext);

        if (res = 0) then begin
          res := BeginPrinting ('VERSAND_FEHLER', errtext);

          if (res <> 0) then
            ErrorText := FormatMessageText (1360, [errtext])
          else begin
            paramarray [0] := 'AUFTRAG_NR:'+query.FieldByName ('AUFTRAG_NR').AsString;
            paramarray [1] := 'SPEDITION:'+query.FieldByName ('SPED_NAME').AsString;
            paramarray [2] := 'NAME:'+query.FieldByName ('NAME1').AsString;
            paramarray [3] := 'STRASSE:'+query.FieldByName ('STRASSE').AsString;
            paramarray [4] := 'PLZ:'+query.FieldByName ('PLZ').AsString;
            paramarray [5] := 'ORT:'+query.FieldByName ('ORT').AsString;
            paramarray [6] := 'LAND:'+query.FieldByName ('LAND').AsString;
            paramarray [7] := 'ERROR_TEXT:'+ErrorMsg;

            if (query.FieldByName ('NVE_NR').IsNull) then begin
              paramarray [8] := 'NVE_NR:';
              paramarray [9] := 'BARCODE:';
              paramarray [10] := 'BARCODE_TEXT:';
            end else begin
              paramarray [8] := 'NVE_NR:'+query.FieldByName ('NVE_NR').AsString;
              paramarray [9] := 'BARCODE:'+'00'+query.FieldByName ('NVE_NR').AsString;
              paramarray [10] := 'BARCODE_TEXT:'+'(00)'+query.FieldByName ('NVE_NR').AsString;
            end;

            res := PrintTemplate (LabelTemplatePath+forminfo.FormularName, paramarray, errtext);
            if (res <> 0) Then
              ErrorText := 'Fehler beim Drucker der Formulardaten'+#13+#13+errtext;

            EndPrinting;
          end;

          if (res <> 0) then
            ErrorText := FormatMessageText (1360, [errtext]);

          ClosePrinterPort;
        end;
      end;

      query.Close;
    finally

    end;
  finally
    query.Free;
  end;

  Result := res;

  {$ifdef Trace}
    TraceResult ('ErrorText', ErrorText);
    FunctionStop (Result);
  {$endif}
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function StartLoadVersandInfos (AOwner: TComponent; const RefNVE : Integer; var ErrorText : String) : Integer;
var
  refauf       : Integer;
  csvstr       : AnsiString;
  res          : Integer;
  fstream      : TFileStream;
  query        : TADOQuery;
begin
  res := 0;
  ErrorText := '';

  {$ifdef Trace}
    FunctionStart ('StartLoadVersandInfos');
    TraceParameter ('RefNVE', RefNVE);
  {$endif}

  query := TADOQuery.Create (Nil);

  try
    query.LockType := ltReadOnly;
    query.Connection := LVSDatenModul.MainADOConnection;

    query.SQL.Add ( 'select a.REF, sped.REF as REF_SPED, av.SPEDITION_NR, sped.DFUE_ART, a.REF_MAND, a.MANDANT, a.REF_LIEFLAGER, a.AUFTRAG_NR, a.REF_KUNDEN_ADR, a.REF_LIEFER_ADR, nve.BRUTTO_GEWICHT'
                   +',nve.NVE_NR, av.OPT_NACHNAHME, nve.SENDUNGS_NR, nve.PACKAGE_NR, lif.AUSLIEFER_NR'
                   +',(select count (*) from V_NVE_01 where STATUS<>''DEL'' and REF_AUF_KOPF=a.REF) as PACKAGE_COUNT'
                   +' from V_AUFTRAG_01 a inner join V_AUFTRAG_VERSAND av on (av.REF_AUF_KOPF=a.REF) inner join V_LIEFERUNG_01 lif on (lif.REF=a.REF_LIEFERUNG)'
                   +' inner join V_NVE_01 nve on (a.REF=nve.REF_AUF_KOPF)'
                   +' left outer join V_SPEDITIONEN sped on (sped.REF=nvl (nve.REF_SPED,av.REF_SPED))'
                   +' where nve.REF=:ref_nve');
    query.Parameters.ParamByName('ref_nve').Value := RefNVE;

    try
      query.Open;

      if not (query.FieldByName ('REF_SPED').IsNull) then begin
        refauf := query.FieldByName ('REF').AsInteger;

        if (query.FieldByName ('DFUE_ART').AsString ='SENDIT') or (query.FieldByName ('DFUE_ART').AsString ='SENDIT-PRT') then begin
          loadfname := '';

          CheckConfigParameter (query.FieldByName('REF_MAND').AsInteger, -1, query.FieldByName('REF_LIEFLAGER').AsInteger, 'SENDIT_CSV_PATH', SendITServerPath);

          if (Length (SendITServerPath) = 0) then
            res := -11
          else if (CheckSendITPath (SendITServerPath, ErrorText) <> 0) then
            res := -12
          else begin
            if (SendITServerPath [Length (SendITServerPath)] <> '\') then SendITServerPath := SendITServerPath + '\';

            csvstr := 'Load' + ';;;;;;;;';

            csvstr := csvstr + ';' + query.FieldByName('NVE_NR').AsString;

            csvstr := csvstr + ';;;;;;;;;;;';

            loadfname := 'pcd_l_'+ query.FieldByName('MANDANT').AsString+'_'+ query.FieldByName('AUFTRAG_NR').AsString;

            try
              fstream := TFileStream.Create (SendITServerPath + 'Import\' + loadfname + '.tmp', fmCreate);
            except
              fstream := Nil;
            end;

            if not Assigned (fstream) then
              res := -7
            else begin
              fstream.WriteBuffer (Pointer (csvstr)^, Length (csvstr));

              fstream.Free;

              //Und dann erst freigeben
              RenameFile (SendITServerPath + 'Import\' + loadfname + '.tmp', SendITServerPath + 'Import\' + loadfname + '.csv');
            end;
          end;
        end;

        query.Close;
      end;
    except
      on  E: Exception do begin
        res := -9;
        ErrorText := 'Fehler bei der Druckdatenaufbereitung (LOAD)';

        ErrorTrackingModule.WriteErrorLog ('Exception StartLoadVersandInfos', e.ClassName + ' : ' + e.Message);
      end;
    end;
  finally
    query.Free;
  end;

  Result := res;

  {$ifdef Trace}
    TraceResult ('ErrorText', ErrorText);
    FunctionStop (Result);
  {$endif}
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function FinishLoadVersandInfos   (AOwner: TComponent; const RefNVE : Integer; const Sped : String; var ErrorText : String) : Integer;
var
  res       : Integer;
  readanz   : Integer;
  timeout   : Integer;
  iostr     : AnsiString;
  fstream   : TFileStream;
  implist   : TStringList;
  loadrspfname : String;
begin
  res := 0;
  ErrorText := '';

  {$ifdef Trace}
    FunctionStart ('FinishLoadVersandInfos');
    TraceParameter ('RefNVE', RefNVE);
  {$endif}

  CheckConfigParameter (-1, LVSDatenModul.AktLocationRef, -1, 'SENDIT_CSV_TIMEOUT', timeout, -1);

  loadrspfname  := '';

  infowin := TInfoWin.Create (AOwner);

  infowin.Caption := FormatResourceText (1238, [Sped]);
  infowin.Label1.Caption := GetResourceText (1237);

  infowin.BeginShowModal;

  {$ifdef Trace}
    TraceParameter ('SendITServerPath', SendITServerPath);
    TraceParameter ('loadfname', loadfname);
  {$endif}

  try
    Screen.Cursor := crHourGlass;

    try
      WaitResponse (SENDIT, loadfname, loadrspfname, timeout);
    finally
      Screen.Cursor := crDefault;
    end;
  finally
    infowin.EndShowModal;
    infowin.Free;
  end;

  Application.ProcessMessages;

  if (Length (loadrspfname) = 0) then
    res := -25
  else begin
    try
      fstream := TFileStream.Create (loadrspfname, fmOpenRead + fmShareDenyWrite);
    except
      fstream := Nil;
    end;

    if not Assigned (fstream) then
      res := -26
    else begin
      try
        SetLength (iostr, 1024);

        readanz := fstream.Read (PChar(iostr)^, 1024);
        SetLength (iostr, readanz);

        {$ifdef Trace}
          TraceResult ('iostr', iostr);
        {$endif}

        implist := TStringList.Create;

        try
          implist.Delimiter := ';';
          implist.StrictDelimiter := True;

          implist.DelimitedText := iostr;

          {$ifdef Trace}
            TraceResult ('implist.Count', implist.Count);
          {$endif}

          if (implist.Count < 6) then
            res := -27
          else begin
            if (implist [11] = '0') then begin
              if (res = 0) then begin
                res := SetNVESendungsNr (RefNVE, 'SendIT', Sped, implist [2]);

                if (res <> 0) then
                  ErrorText := LVSDatenModul.LastLVSErrorText;
              end;
            end else begin
              res := -28;
              ErrorText := implist [12];
            end;
          end;
        finally
          implist.Free;
        end;
      finally
        fstream.Free;
      end;

      MoveResponse (loadrspfname, (res <> 0));
    end;
  end;

  Result := res;

  {$ifdef Trace}
    TraceResult ('ErrorText', ErrorText);
    FunctionStop (Result);
  {$endif}
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function CloseVersandDFUE (AOwner: TComponent; const RefSped : Integer; const DruckerPort, DruckerStation : String; var ErrorText : String) : Integer;

  function CloseGLSITWorkDay (const RefSped, RefGateway : Integer) : Integer;
  var
    res,
    errcode  : Integer;
    errtext  : String;
    urlparam : String;
    body     : String;
    resp     : String;
    telstr   : String;
    gipw,
    giuser,
    gicode,
    gisede   : String;

    keystr   : String;
    strlist  : TStringList;
    sdata    : TMemoryStream;
    selquery,
    cfgquery : TADOQuery;
    olddec   : Char;
  begin
    res := 0;

    olddec := {$IFDEF DELPHIXE10_UP}FormatSettings.DecimalSeparator{$else}DecimalSeparator{$endif};

    try
      {$IFDEF DELPHIXE10_UP}FormatSettings.DecimalSeparator{$else}DecimalSeparator{$endif} := '.';

      ErrorText := '';

      cfgquery  := TADOQuery.Create (Nil);

      try
        cfgquery.LockType := ltReadOnly;
        cfgquery.Connection := LVSDatenModul.MainADOConnection;

        cfgquery.SQL.Add ('select * from V_SPED_GATEWAY where REF=:ref');
        cfgquery.Parameters [0].Value := RefGateway;

        cfgquery.Open;

        if not Assigned (cfgquery.FindField('API_KEY')) then
          ErrorText := 'Not perpared for api key'
        else if cfgquery.FieldByName('API_KEY').IsNull then
          ErrorText := 'No api key'
        else
          keystr := cfgquery.FieldByName('API_KEY').AsString;

        telstr := cfgquery.FieldByName('DEFAULT_PHONE_NUMBER').AsString;

        cfgquery.Close;
      finally
        cfgquery.Free;
      end;

      if (Length (keystr) = 0) then
        res := 23
      else begin
        //Die Zugangsdaten stehen im API_KEY
        strlist := TStringList.Create;
        try
          strlist.Delimiter := ';';
          strlist.StrictDelimiter := true;
          strlist.DelimitedText := keystr;

          if (strlist.Count < 4) then
            res := 24
          else begin
            gisede := strlist [0];     //SIGLA SEDE
            giuser := strlist [1];     //CODICE CLIENTE
            gipw   := strlist [2];     //PASSWORD
            gicode := strlist [3];     //CONTRATTO
          end;
        finally
          strlist.Free;
        end;
     end;

     if (res = 0) then begin
        sdata := TMemoryStream.Create;
        selquery  := TADOQuery.Create (Nil);

        try
          selquery.LockType := ltReadOnly;
          selquery.Connection := LVSDatenModul.MainADOConnection;

          selquery.SQL.Add ('select nve.REF as REF_NVE,nve.NVE_NR,nve.SENDUNGS_NR,send.REF as REF_SENDUNGS_NR'
                            +' from V_NVE_01 nve, V_SENDUNGS_NR send'
                            +' where send.REF_NVE=nve.REF and send.STATUS=''ANG'' and'
                            +' nve.STATUS in (''WA'',''TRA'',''ABG'') and send.DFUE_DATUM is null and'
                            +' nve.SENDUNGS_NR is not null and nve.REF_SPED=:ref_sped'
                           );
          selquery.Parameters [0].Value := RefSped;

          selquery.Open;

          if (selquery.RecordCount > 0) then begin
            if Assigned (SendITLog) then begin
              SendITLog.Logging (clNormal, 'CloseGLSITWorkDay: GLS-IT, RefSped: '+IntToStr (RefSped)+', RefGateway: '+IntToStr (RefGateway)+CR+LF);
            end;

            ForceDirectories(DatenPath + RESTDumpDir + 'soap\GLS_IT\'+FormatDateTime ('yyyymmdd', Now));

            //Token erzeugen
            urlparam := '';

            body := '_xmlRequest='
                   +'<Info>'
                   +'  <SedeGls>'+gisede+'</SedeGls>'
                   +'  <CodiceClienteGls>'+giuser+'</CodiceClienteGls>'
                   +'  <PasswordClienteGls>'+gipw+'</PasswordClienteGls>';

            while not (selquery.Eof) and (res = 0) do begin
              body := body + '<Parcel><NumeroDiSpedizioneGLSDaConfermare>'+selquery.Fields [2].AsString+'</NumeroDiSpedizioneGLSDaConfermare></Parcel>';

              res := SetNVEEDI (selquery.Fields [0].AsInteger);

              selquery.Next;
            end;

            body := body + '</Info>';

            if (res = 0) then begin
              StrToFile (DatenPath + 'soap\GLS_IT\'+FormatDateTime ('yyyymmdd', Now)+'\close_work_day_'+FormatDateTime ('yyyymmdd_hhnnss', Now)+'.xml', body);

              sdata.Clear;
              if SendRequest('labelservice.gls-italy.com', // Host,
                              -1, //Port
                              'ilswebservice.asmx/CloseWorkDayByShipmentNumber', // Service
                              'POST', //Methode
                              '', // Proxy,
                              '', '', // User , PW
                              '', //Action
                              'application/x-www-form-urlencoded', //ContentType
                              [], //AddHeader
                              body,         // RequestData
                              resp,
                              sdata, //ResponseStream
                              errcode, // Fehlercode
                              errtext) // Fehlertext
                            then
              begin
                sdata.Position := 0;
                sdata.SaveToFile(DatenPath + 'soap\GLS_IT\'+FormatDateTime ('yyyymmdd', Now)+'\parcel_list_'+FormatDateTime ('yyyymmdd_hhnnss', Now)+'.xml');
              end else begin
                res := 21;
                ErrorText := 'Fehler CloseGLSITWorkDay';
              end;
            end;
          end;

          selquery.Close;
        finally
          sdata.Free;
          selquery.Free;
        end;
      end;
    finally
      {$IFDEF DELPHIXE10_UP}FormatSettings.DecimalSeparator{$else}DecimalSeparator{$endif} := olddec;
    end;

    if (Length (ErrorText) > 0) then begin
      if Assigned (SendITLog) then begin
        SendITLog.Logging (clError, 'Versand: GLS-IT, Error:'+ErrorText+CR+LF);
      end;
    end;

    Result := res;
  end;

var
  res : Integer;
  fres      : Integer;
  timeout   : Integer;
  ok        : boolean;
  readanz   : Integer;
  iostr     : AnsiString;
  csvstr    : AnsiString;
  fname,
  logfname,
  fnewname,
  datestr,
  versstr,
  csvpath,
  urlstr,
  apistr,
  userstr,
  respstr,
  passwdstr,
  jsonstr,
  errtext,
  logpath,
  gatestr,
  cliendstr : String;
  stridx,
  refgate,
  portint,
  errcode   : Integer;
  sdata     : TMemoryStream;
  js        : TlkJSONobject;
  fs,
  us,
  errfs     : TlkJSONbase;
  query     : TSmartQuery;
  relquery  : TSmartQuery;
  selquery  : TSmartQuery;
  fstream   : TFileStream;
  srec      : TSearchRec;
  implist   : TStringList;
  infowin   : TInfoWin;

  {$ifdef UNICODE}
    utfstr       : AnsiString;
    datastr      : String;
  {$endif}
begin
  res := 0;
  ErrorText := '';

  {$ifdef Trace}
    FunctionStart ('CloseVersandDFUE');
    TraceParameter ('RefSped       ', RefSped);
    TraceParameter ('DruckerPort   ', DruckerPort);
    TraceParameter ('DruckerStation', DruckerStation);
  {$endif}

  CreateVersandLabel.DatenPath := LVSConfigModul.GetSessionDataDir;

  urlstr  := '';
  portint := -1;

  csvpath := '';

  query     := LVSDatenModul.CreateSmartQuery (Nil, 'CloseVersandDFUE_query');
  relquery  := LVSDatenModul.CreateSmartQuery (Nil, 'CloseVersandDFUE_rel');
  selquery  := LVSDatenModul.CreateSmartQuery (Nil, 'CloseVersandDFUE_sel');

  try
    query.SQL.Add ('select sped.REF as REF_SPED, sped.DFUE_ART, nvl (sped.DFUE_KENNZEICHEN, sped.NAME) as SPEDITION, sped.REF_MAND, m.NAME as MANDANT, sped.REF_LAGER, l.NAME as LAGER, cfg.CSV_EXPORT_PATH, cfg.REST_URL'
                  +' from V_SPEDITIONEN sped inner join V_SPED_CONFIG cfg on (cfg.REF_SPED=sped.REF) inner join V_MANDANT m on (m.REF=sped.REF_MAND) left outer join V_LAGER l on (l.REF=sped.REF_LAGER) where sped.REF=:ref_sped');
    query.Params.ParamByName('ref_sped').Value := RefSped;

    try
      query.Open;

      if (query.FieldByName ('DFUE_ART').AsString ='SENDIT') or (query.FieldByName ('DFUE_ART').AsString ='SENDIT-PRT') then begin
        if not (query.FieldByName ('REST_URL').IsNull) then
          urlstr := query.FieldByName ('REST_URL').AsString
        else if not (query.FieldByName ('CSV_EXPORT_PATH').IsNull) then
          csvpath := query.FieldByName ('CSV_EXPORT_PATH').AsString
        else begin
          CheckConfigParameter (query.FieldByName('REF_MAND').AsInteger, LVSDatenModul.AktLocationRef, query.FieldByName('REF_LAGER').AsInteger, 'SENDIT_CLOSE_CSV_PATH', csvpath);

          if (Length (csvpath) = 0) then
            CheckConfigParameter (query.FieldByName('REF_MAND').AsInteger, LVSDatenModul.AktLocationRef, DBGetReferenz (query.FieldByName('REF_LAGER')), 'SENDIT_CSV_PATH', csvpath);
        end;

        if (Length (DruckerStation) = 0) then
          res := -10
        else if (Length (csvpath) = 0) and (Length (urlstr) = 0) then
          res := -11
        else if (Length (csvpath) > 0) and (CheckSendITPath (csvpath, ErrorText) <> 0) then
          res := -12
        else begin
          if (Length (csvpath) > 0) and (csvpath [Length (csvpath)] <> '\') then csvpath := csvpath + '\';

          relquery.SQL.Clear;

          //Es muss nur jedes Gateway einmal aufgerufen werden
          relquery.SQL.Add ('select nvl (SENDIT_LOCATION, GATEWAY) as SENDIT_LOCATION,trim (SENDIT_CLIENT) as SENDIT_CLIENT from V_SPED_GATEWAY where SENDIT_CLIENT is not null and REF_SPED=:ref_sped and REF_LOCATION=:ref_loc'
                           +' group by SENDIT_LOCATION,GATEWAY,SENDIT_CLIENT'
                           +' union'
                           +' select nvl (SENDIT_RETURN_LOCATION, GATEWAY) as SENDIT_LOCATION,trim (SENDIT_RETURN_CLIENT) as SENDIT_CLIENT from V_SPED_GATEWAY where SENDIT_RETURN_LOCATION is not null and SENDIT_RETURN_CLIENT is not null and REF_SPED=:ref_sped and REF_LOCATION=:ref_loc'
                           +' group by SENDIT_RETURN_LOCATION,GATEWAY,SENDIT_RETURN_CLIENT');
          relquery.Params.ParamByName('ref_sped').Value := RefSped;
          relquery.Params.ParamByName('ref_loc').Value := LVSDatenModul.AktLocationRef;

          infowin := TInfoWin.Create (AOwner);

          infowin.Caption := FormatResourceText (1239, [query.FieldByName('SPEDITION').AsString]);
          infowin.Label1.Caption := GetResourceText (1237);

          infowin.BeginShowModal;

          Screen.Cursor := crHourGlass;

          try
            relquery.Open;

            while not (relquery.Eof) and (res = 0) do begin
              if not (relquery.FieldByName('SENDIT_CLIENT').IsNull) then begin
                if (relquery.Fields [0].IsNull) then
                  versstr := '1000'
                else
                  versstr := relquery.Fields [0].AsString;

                if (Length (urlstr) > 0) then begin
                  //Portnummer ermitteln
                  stridx := Length (urlstr);
                  while ((stridx > 1) and (urlstr [stridx] <> '/') and (urlstr [stridx] <> ':')) do
                    dec (stridx);

                  if (stridx > 1) and (urlstr [stridx] = ':') then begin
                    if not TryStrToInt (copy (urlstr, stridx + 1), portint) then
                      portint := -1;

                    urlstr := copy (urlstr, 1, stridx - 1);
                  end;

                  userstr := 'testuser';
                  passwdstr := 'testpw';

                  apistr  := 'api/v2/closing/one';

                  jsonstr := '{';
                    jsonstr := jsonstr + '"webServiceUser": "'+userstr+'"';
                    jsonstr := jsonstr + ',"webServicePassword": "'+passwdstr+'"';
                    jsonstr := jsonstr + ',"ClientID": "'+relquery.FieldByName('SENDIT_CLIENT').AsString+'"';
                    jsonstr := jsonstr + ',"ShippingLocationID": "'+relquery.FieldByName('SENDIT_LOCATION').AsString+'"';
                    jsonstr := jsonstr + ',"Shipper": "'+query.FieldByName('SPEDITION').AsString+'"';
                    jsonstr := jsonstr + ',"StationID": "'+DruckerStation+'"';
                  jsonstr := jsonstr + '}';

                  ForceDirectories(DatenPath + RESTDumpDir + 'soap\SendIT\'+FormatDateTime ('yyyymmdd', Now));

                  StrToFile (DatenPath + RESTDumpDir + 'soap\SendIT\'+FormatDateTime ('yyyymmdd', Now)+'\close_'+query.FieldByName('SPEDITION').AsString+'_'+FormatDateTime ('hhnnss', Now)+'.json', jsonstr);

                  sdata := TMemoryStream.Create;

                  try
                    sdata.Clear;
                    if SendRequest (urlstr, // Host,
                                    portint, //Port
                                    apistr, // Service
                                    'POST', //Methode
                                    '', // Proxy,
                                    '', '', // User , PW
                                    '', //Action
                                    'application/json', //ContentType
                                    [], //AddHeader
                                    jsonstr,         // RequestData
                                    respstr,
                                    sdata, //ResponseStream
                                    errcode, // Fehlercode
                                    errtext) // Fehlertext
                                  then
                    begin
                      StrToFile (DatenPath + RESTDumpDir + 'soap\SendIT\'+FormatDateTime ('yyyymmdd', Now)+'\close_resp_'+query.FieldByName('SPEDITION').AsString+'_'+FormatDateTime ('hhnnss', Now)+'.json', respstr);

                      sdata.Position := 0;
                      sdata.SaveToFile(DatenPath + RESTDumpDir + 'soap\SendIT\'+FormatDateTime ('yyyymmdd', Now)+'\close_'+query.FieldByName('SPEDITION').AsString+'_'+FormatDateTime ('hhnnss', Now)+'.json');

                      if (copy (uppercase (respstr), 1, 12) <> 'HTTP/1.1 200') then begin
                        res := 55;

                        fs := Nil;

                        {$ifdef UNICODE}
                          SetLength(utfstr, sdata.Size);
                          sdata.Read(PAnsiChar (utfstr)^, sdata.Size);
                          datastr := StringUtils.StringToUTF (utfstr);

                          js := TlkJSONstreamed.ParseText (datastr) as TlkJsonObject;
                        {$else}
                          sdata.Position := 0;
                          js := TlkJSONstreamed.LoadFromStream (sdata) as TlkJsonObject;
                        {$endif}

                        if not Assigned (js) then begin
                          {$ifdef UNICODE}
                            ErrorText := datastr;
                          {$else}
                            ErrorText := MemoryStreamToString (sdata);
                          {$endif}
                        end else begin
                          us := js.Field['Message'];
                          if Assigned (us) then
                            ErrorText := us.Value
                          else
                            ErrorText := MemoryStreamToString (sdata)
                        end;
                      end else begin
                        fs := Nil;

                        {$ifdef UNICODE}
                          SetLength(utfstr, sdata.Size);
                          sdata.Read(PAnsiChar (utfstr)^, sdata.Size);
                          datastr := StringUtils.StringToUTF (utfstr);

                          js := TlkJSONstreamed.ParseText (datastr) as TlkJsonObject;
                        {$else}
                          sdata.Position := 0;
                          js := TlkJSONstreamed.LoadFromStream (sdata) as TlkJsonObject;
                        {$endif}
                      end;
                    end;
                  finally
                    sdata.Free;
                  end;
                end else begin
                  csvstr := 'Closing';

                  if (AktSendITIFCImpVersion > 1) then
                    csvstr := csvstr + ';' + query.FieldByName('SPEDITION').AsString+'_C'
                  else
                    csvstr := csvstr + ';' + query.FieldByName('SPEDITION').AsString;

                  csvstr := csvstr + ';' + DruckerStation;
                  csvstr := csvstr + ';' + '0';

                  if not (relquery.FieldByName('SENDIT_CLIENT').IsNull) then
                    cliendstr := relquery.FieldByName('SENDIT_CLIENT').AsString
                  else cliendstr := query.FieldByName('MANDANT').AsString;

                  csvstr := csvstr + ';' + cliendstr;
                  csvstr := csvstr + ';';
                  csvstr := csvstr + ';';
                  csvstr := csvstr + ';';
                  csvstr := csvstr + ';;EUR';

                  csvstr := csvstr + ';';
                  csvstr := csvstr + ';';
                  csvstr := csvstr + ';';
                  csvstr := csvstr + ';';
                  csvstr := csvstr + ';';
                  csvstr := csvstr + ';';
                  csvstr := csvstr + ';';
                  csvstr := csvstr + ';';

                  if (AktSendITIFCImpVersion > 1) then
                    csvstr := csvstr + ';;;;;' + versstr;

                  csvstr := csvstr + ';';
                  csvstr := csvstr + ';';
                  csvstr := csvstr + ';';
                  csvstr := csvstr + ';';
                  csvstr := csvstr + ';';
                  csvstr := csvstr + ';';
                  csvstr := csvstr + ';';
                  csvstr := csvstr + ';';
                  csvstr := csvstr + ';';
                  csvstr := csvstr + ';';
                  csvstr := csvstr + ';';
                  csvstr := csvstr + ';';
                  csvstr := csvstr + ';';
                  csvstr := csvstr + ';';
                  csvstr := csvstr + ';';
                  csvstr := csvstr + ';';
                  csvstr := csvstr + ';';
                  csvstr := csvstr + ';';
                  csvstr := csvstr + ';';
                  csvstr := csvstr + ';';
                  csvstr := csvstr + ';';
                  csvstr := csvstr + ';';
                  csvstr := csvstr + ';';
                  csvstr := csvstr + ';';

                  datestr := FormatDateTime ('yyyymmddhhnnss', Now);

                  fname := 'pcd_c_'+LabelClientName+'_'+versstr+'_'+cliendstr+'_'+ query.FieldByName('SPEDITION').AsString + '_'+datestr;

                  try
                    fstream := TFileStream.Create (csvpath + 'Import\'+fname + '.tmp', fmCreate);
                  except
                    fstream := Nil;
                  end;

                  if not Assigned (fstream) then
                    res := -7
                  else begin
                    fstream.WriteBuffer (Pointer (csvstr)^, Length (csvstr));

                    fstream.Free;

                    //Und dann erst freigeben
                    RenameFile (csvpath + 'Import\'+fname + '.tmp', csvpath + 'Import\'+fname+'.csv');

                    try
                      Sleep (2000);

                      //Warten auf die Rückmeldung
                      ok := False;
                      timeout := 0;

                      repeat
                        fres := FindFirst (csvpath + 'Export\' + fname + '.*', faArchive, srec);

                        {$ifdef Trace}
                          TraceResult ('fres', fres);
                        {$endif}

                        if (fres <> 0) then begin
                          Inc (timeout);

                          Application.ProcessMessages;

                          Sleep (250);
                        end else begin
                          ok := true;

                          {$ifdef Trace}
                            TraceResult ('srec.Name', srec.Name);
                          {$endif}

                          fname := csvpath + 'Export\' + srec.Name;

                          FindClose (srec);
                        end;
                      until (ok or (timeout > 4*3*60));  //Das kann schon mal etwas dauern

                      Application.ProcessMessages;

                      if not (ok) then begin
                        res := -25;
                        ErrorText := 'Timeout beim Warten auf die Rückmeldung';
                      end else begin
                        try
                          fstream := TFileStream.Create (fname, fmOpenRead + fmShareDenyWrite);
                        except
                          fstream := Nil;
                        end;

                        if not Assigned (fstream) then
                          res := -26
                        else begin
                          try
                            SetLength (iostr, 1024);

                            readanz := fstream.Read (PChar(iostr)^, 1024);
                            SetLength (iostr, readanz);

                            {$ifdef Trace}
                              TraceResult ('iostr', iostr);
                            {$endif}

                            implist := TStringList.Create;

                            try
                              implist.Delimiter := ';';
                              implist.StrictDelimiter := True;

                              implist.DelimitedText := iostr;

                              {$ifdef Trace}
                                TraceResult ('implist.Count', implist.Count);
                              {$endif}

                              if (implist.Count < 2) then
                                res := -27
                              else begin
                                if (implist [0] = '1') then begin
                                  if (Pos ('No publishingdata', implist [1]) > 0) then
                                  else begin
                                    res := -28;
                                    ErrorText := implist [1];
                                  end;
                                end;

                                if (res = 0) then begin
                                  res := SetSpedSendungsDFUE (query.FieldByName('REF_SPED').AsInteger, relquery.Fields [0].AsString);

                                  if (res <> 0) then
                                    ErrorText := LVSDatenModul.LastLVSErrorText;
                                end;
                              end;
                            finally
                              implist.Free;
                            end;
                          finally
                            fstream.Free;
                          end;

                          fnewname := ExtractFileName (fname);
                          fnewname := Copy (fnewname, 1, Length (fnewname) - 4) + '_' + FormatDateTime ('yyyymmdd_hhnnss', Now)+'.csv';

                          if (res = 0) then begin
                            if not (DirectoryExists (csvpath + 'Export\Save')) then
                              CreateDir (csvpath + 'Export\Save');

                            if FileExists (csvpath + 'Export\Save\'+fnewname) then
                              DeleteFile (csvpath + 'Export\Save\'+fnewname);

                            RenameFile (fname, csvpath + 'Export\Save\'+fnewname)
                          end else begin
                            if not (DirectoryExists (csvpath + 'Export\Error')) then
                              CreateDir (csvpath + 'Export\Error');

                            if FileExists (csvpath + 'Export\Error\'+fnewname) then
                              DeleteFile (csvpath + 'Export\Error\'+fnewname);

                            RenameFile (fname, csvpath + 'Export\Error\'+fnewname);
                          end;
                        end;
                      end;
                    except
                      res := -4;
                    end;
                  end;
                end;
              end;

              relquery.Next;
            end;

            relquery.Close;

            CheckConfigParameter (query.FieldByName('REF_MAND').AsInteger, -1, query.FieldByName('REF_LAGER').AsInteger, 'SENDIT_CLOSE_LOG_PATH', logpath);

            if (Length (logpath) > 0) then begin
              logpath := IncludeTrailingBackslash (logpath);

              logfname := 'close_'+query.FieldByName('LAGER').AsString+'_'+query.FieldByName('SPEDITION').AsString+'_'+FormatDateTime ('yyyymmddhhnnss', Now);

              try
                fstream := TFileStream.Create (logpath+logfname + '.tmp', fmCreate);
              except
                fstream := Nil;
              end;

              if not Assigned (fstream) then
                ErrorTrackingModule.WriteErrorLogNoDB ('SENDIT_CLOSE_LOG_PATH', 'Datei '+logpath + logfname + '.tmp'+' kann nicht angelegt werden: '+GetAPIErrorMessage (GetLastError))
              else begin
                csvstr := FormatDateTime ('dd.mm.yyyy hh:nn:ss', Now)+';'+LVSDatenModul.AktLocation+';'+query.FieldByName('LAGER').AsString+';'+query.FieldByName('SPEDITION').AsString;
                fstream.WriteBuffer (Pointer (csvstr)^, Length (csvstr));

                fstream.Free;

                //Und dann erst freigeben
                RenameFile (logpath + logfname + '.tmp', logpath + logfname+'.csv');
              end;
            end;
          finally
            Screen.Cursor := crDefault;

            infowin.EndShowModal;
            infowin.Free;
          end;
        end;
      end else if (query.FieldByName ('DFUE_ART').AsString = 'GLS-IT-WEB') then begin
        relquery.SQL.Clear;

        //Es muss nur jedes Gateway einmal aufgerufen werden
        relquery.SQL.Add ('select REF from V_SPED_GATEWAY where REF_SPED=:ref_sped and REF_LOCATION=:ref_loc');
        relquery.SQL.Add ('union');
        relquery.SQL.Add ('select REF from V_SPED_GATEWAY where REF_SPED=:ref_sped and REF_LOCATION=:ref_loc');
        relquery.Params.ParamByName('ref_sped').Value := RefSped;
        relquery.Params.ParamByName('ref_loc').Value := LVSDatenModul.AktLocationRef;

        infowin := TInfoWin.Create (AOwner);

        infowin.Caption := FormatResourceText (1239, [query.FieldByName('SPEDITION').AsString]);
        infowin.Label1.Caption := GetResourceText (1237);

        infowin.BeginShowModal;

        Screen.Cursor := crHourGlass;

        try
          relquery.Open;

          while not (relquery.Eof) and (res = 0) do begin
            res := CloseGLSITWorkDay (RefSped, relquery.Fields [0].AsInteger);

            relquery.Next;
          end;

          relquery.Close;
        finally
          Screen.Cursor := crDefault;

          infowin.EndShowModal;
          infowin.Free;
        end;
      end;

      relquery.SQL.Clear;

      //Es muss nur jedes Gateway einmal aufgerufen werden
      relquery.SQL.Add ('select * from V_SPED_GATEWAY where REF_SPED=:ref_sped and REF_LOCATION=:ref_loc');
      relquery.Params.ParamByName('ref_sped').Value := RefSped;
      relquery.Params.ParamByName('ref_loc').Value := LVSDatenModul.AktLocationRef;

      relquery.Open;

      while not (relquery.Eof) do begin
        if (Assigned (relquery.FindField ('EXPORT_NOTIFICATION')) and (relquery.FieldByName ('EXPORT_NOTIFICATION').AsString = 'EXPORTO')) then begin
          selquery.SQL.Clear;

          if relquery.FieldByName ('REF_SUB_MAND').IsNull then begin
            selquery.SQL.Add ('select * from V_SPED_GATEWAY where REF_LOCATION=:ref_loc and REF_SPED=:ref_sped and REF_MAND=:ref_mand and LAND_ISO=:name');
            selquery.Params.ParamByName('ref_mand').Value := relquery.FieldByName ('REF_MAND').AsInteger;
          end else begin
            selquery.SQL.Add ('select * from V_SPED_GATEWAY where REF_LOCATION=:ref_loc and REF_SPED=:ref_sped and REF_SUB_MAND=:ref_sub and LAND_ISO=:name');
            selquery.Params.ParamByName('ref_sub').Value := relquery.FieldByName ('REF_SUB_MAND').AsInteger;
          end;

          selquery.Params.ParamByName('name').Value := relquery.FieldByName ('EXPORT_NOTIFICATION').AsString;
          selquery.Params.ParamByName('ref_loc').Value := LVSDatenModul.AktLocationRef;
          selquery.Params.ParamByName('ref_sped').Value := RefSped;

          selquery.Open;

          refgate := selquery.FieldByName ('REF').AsInteger;
          gatestr := selquery.FieldByName ('GATEWAY').AsString;

          selquery.Close;

          if (refgate > 0) then begin
            selquery.SQL.Clear;

            //Über alle Aufträge, die abgeschlossen sind und NVEs von diesem Versender enthalten, welche noch nicht avisiert wurden.
            if relquery.FieldByName ('REF_SUB_MAND').IsNull then
            begin
              selquery.SQL.Add ('select REF,AUFTRAG_NR from VQ_AUFTRAG where STATUS=''ABG'' and REF_SUB_MAND is null and REF_MAND=:ref_mand and REF in (select REF_AUF_KOPF from VQ_LAGER_NVE where STATUS in (''WA'',''ABG'') and EXPORT_DATUM is null and REF_SPED=:ref_sped)');
              selquery.Params.ParamByName('ref_mand').Value := relquery.FieldByName ('REF_MAND').AsInteger;
            end
            else
            begin
              selquery.SQL.Add ('select REF,AUFTRAG_NR from VQ_AUFTRAG where STATUS=''ABG'' and REF_SUB_MAND=:ref_sub and REF in (select REF_AUF_KOPF from VQ_LAGER_NVE where STATUS in (''WA'',''ABG'') and EXPORT_DATUM is null and REF_SPED=:ref_sped)');
              selquery.Params.ParamByName('ref_sub').Value := relquery.FieldByName ('REF_SUB_MAND').AsInteger;
            end;

            selquery.Params.ParamByName('ref_sped').Value := RefSped;

            selquery.Open;

            while not (selquery.Eof) do begin
              errtext := '';

              res := CreateExportoAuftrag (refgate, RefSped, selquery.Fields [0].AsInteger, DatenPath, errtext);

              if (Length (errtext) > 0) then begin
                if (Length (ErrorText) > 0) then ErrorText := ErrorText + #13 + #13;
                ErrorText := ErrorText + FormatMessageText (1879, [selquery.Fields [1].AsString, errtext]);
              end;

              selquery.Next;
            end;

            selquery.Close;

            //Prüfen ob in der Konfig auch die Tour gemeldet werden muss, -> automatisch Verzollung aller Sendungen aus der Tour
            if (res = 0) and (gatestr = 'TOUR') then begin
              res := CreateExportoNVETour (refgate, RefSped, DatenPath, errtext);

              if (Length (errtext) > 0) then begin
                if (Length (ErrorText) > 0) then ErrorText := ErrorText + #13 + #13;
                ErrorText := ErrorText + 'Fehler beim Tagestourabschluss'+#13+errtext;
              end;
            end;
          end;
        end;

        relquery.Next;
      end;

      relquery.Close;

      query.Close;
    except
      res := -2;
    end;
  finally
    query.Free;
    selquery.Free;
    relquery.Free;
  end;

  Result := res;

  {$ifdef Trace}
    TraceResult ('ErrorText', ErrorText);
    FunctionStop (Result);
  {$endif}
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function CloseWorkDay (AOwner: TComponent; const RefSped : Integer; const RefAbschluss : Integer; var ErrorText : String) : Integer;

  function CloseGLSITWorkDay (const RefSped, RefGateway : Integer; var Finished : Boolean) : Integer;
  var
    res,
    reccount,
    errcode  : Integer;
    errtext  : String;
    urlparam : String;
    body     : String;
    resp     : String;
    telstr   : String;
    gipw,
    giuser,
    gicode,
    gisede   : String;

    keystr   : String;
    strlist  : TStringList;
    sdata    : TMemoryStream;
    selquery,
    cfgquery : TADOQuery;
    olddec   : Char;
  begin
    res := 0;
    Finished := false;

    olddec := {$IFDEF DELPHIXE10_UP}FormatSettings.DecimalSeparator{$else}DecimalSeparator{$endif};

    try
      {$IFDEF DELPHIXE10_UP}FormatSettings.DecimalSeparator{$else}DecimalSeparator{$endif} := '.';

      ErrorText := '';

      cfgquery  := TADOQuery.Create (Nil);

      try
        cfgquery.LockType := ltReadOnly;
        cfgquery.Connection := LVSDatenModul.MainADOConnection;

        cfgquery.SQL.Add ('select * from V_SPED_GATEWAY where REF=:ref');
        cfgquery.Parameters [0].Value := RefGateway;

        cfgquery.Open;

        if not Assigned (cfgquery.FindField('API_KEY')) then
          ErrorText := 'Not perpared for api key'
        else if cfgquery.FieldByName('API_KEY').IsNull then
          ErrorText := 'No api key'
        else
          keystr := cfgquery.FieldByName('API_KEY').AsString;

        telstr := cfgquery.FieldByName('DEFAULT_PHONE_NUMBER').AsString;

        cfgquery.Close;
      finally
        cfgquery.Free;
      end;

      if (Length (keystr) = 0) then
        res := 23
      else begin
        //Die Zugangsdaten stehen im API_KEY
        strlist := TStringList.Create;
        try
          strlist.Delimiter := ';';
          strlist.StrictDelimiter := true;
          strlist.DelimitedText := keystr;

          if (strlist.Count < 4) then
            res := 24
          else begin
            gisede := strlist [0];     //SIGLA SEDE
            giuser := strlist [1];     //CODICE CLIENTE
            gipw   := strlist [2];     //PASSWORD
            gicode := strlist [3];     //CONTRATTO
          end;
        finally
          strlist.Free;
        end;
     end;

     if (res = 0) then begin
        sdata := TMemoryStream.Create;
        selquery  := TADOQuery.Create (Nil);

        try
          selquery.LockType := ltReadOnly;
          selquery.Connection := LVSDatenModul.MainADOConnection;

          selquery.SQL.Add ('select send.REF_NVE,send.NVE_NR,send.SENDUNGS_NR,send.REF as REF_SENDUNGS_NR'
                            +' from V_SENDUNGS_NR send'
                            +' where send.STATUS=''ANG'' and'
                            +' send.NVE_STATUS in (''WA'',''TRA'',''ABG'') and send.DFUE_DATUM is null and'
                            +' send.SENDUNGS_NR is not null and send.REF_SPED=:ref_sped'
                           );
          selquery.Parameters [0].Value := RefSped;

          selquery.Open;

          if (selquery.RecordCount = 0) then
            Finished := true
          else begin
            if Assigned (SendITLog) then begin
              SendITLog.Logging (clNormal, 'CloseGLSITWorkDay: GLS-IT, RefSped: '+IntToStr (RefSped)+', RefGateway: '+IntToStr (RefGateway)+CR+LF);
            end;

            ForceDirectories(DatenPath + RESTDumpDir + 'soap\GLS_IT\'+FormatDateTime ('yyyymmdd', Now));

            //Token erzeugen
            urlparam := '';

            reccount := 0;

            body := '_xmlRequest='
                   +'<Info>'
                   +'  <SedeGls>'+gisede+'</SedeGls>'
                   +'  <CodiceClienteGls>'+giuser+'</CodiceClienteGls>'
                   +'  <PasswordClienteGls>'+gipw+'</PasswordClienteGls>';

            while not (selquery.Eof) and (res = 0) and (reccount < 200) do begin
              Inc (reccount);
              
              //body := body + '<Parcel><NumeroDiSpedizioneGLSDaConfermare>'+selquery.Fields [2].AsString+'</NumeroDiSpedizioneGLSDaConfermare></Parcel>';
              body := body + '<Parcel><NumeroDiSpedizioneGLSDaConfermare>'+copy (selquery.Fields [2].AsString,3)+'</NumeroDiSpedizioneGLSDaConfermare></Parcel>';

              if (RefAbschluss > 0) then
                res := AddNVETagesabschluss (RefAbschluss, selquery.Fields [0].AsInteger)
              else
                res := SetNVEEDI (selquery.Fields [0].AsInteger);

              selquery.Next;
            end;

            body := body + '</Info>';

            if (res = 0) then begin
              StrToFile (DatenPath + 'soap\GLS_IT\'+FormatDateTime ('yyyymmdd', Now)+'\close_work_day_'+FormatDateTime ('yyyymmdd_hhnnss', Now)+'.xml', body);

              sdata.Clear;
              if SendRequest('labelservice.gls-italy.com', // Host,
                              -1, //Port
                              'ilswebservice.asmx/CloseWorkDayByShipmentNumber', // Service
                              'POST', //Methode
                              '', // Proxy,
                              '', '', // User , PW
                              '', //Action
                              'application/x-www-form-urlencoded', //ContentType
                              [], //AddHeader
                              body,         // RequestData
                              resp,
                              sdata, //ResponseStream
                              errcode, // Fehlercode
                              errtext) // Fehlertext
                            then
              begin
                sdata.Position := 0;
                sdata.SaveToFile(DatenPath + 'soap\GLS_IT\'+FormatDateTime ('yyyymmdd', Now)+'\parcel_list_'+FormatDateTime ('yyyymmdd_hhnnss', Now)+'.xml');
              end else begin
                res := 21;
                ErrorText := 'Fehler CloseGLSITWorkDay';
              end;
            end;
          end;

          selquery.Close;
        finally
          sdata.Free;
          selquery.Free;
        end;
      end;
    finally
      {$IFDEF DELPHIXE10_UP}FormatSettings.DecimalSeparator{$else}DecimalSeparator{$endif} := olddec;
    end;

    if (Length (ErrorText) > 0) then begin
      if Assigned (SendITLog) then begin
        SendITLog.Logging (clError, 'Versand: GLS-IT, Error:'+ErrorText+CR+LF);
      end;
    end;

    Result := res;
  end;

var
  res       : Integer;
  flag      : boolean;
  query     : TADOQuery;
  relquery  : TADOQuery;
  infowin   : TInfoWin;
begin
  res := 0;
  ErrorText := '';

  {$ifdef Trace}
    FunctionStart ('CloseWorkDay');
    TraceParameter ('RefSped       ', RefSped);
    TraceParameter ('RefAbschluss  ', RefAbschluss);
  {$endif}

  query     := TADOQuery.Create (Nil);
  relquery  := TADOQuery.Create (Nil);

  try
    query.LockType := ltReadOnly;
    query.Connection := LVSDatenModul.MainADOConnection;

    relquery.LockType := ltReadOnly;
    relquery.Connection := LVSDatenModul.MainADOConnection;

    query.SQL.Add ('select sped.REF as REF_SPED, sped.DFUE_ART, nvl (sped.DFUE_KENNZEICHEN, sped.NAME) as SPEDITION, sped.REF_MAND, m.NAME as MANDANT, sped.REF_LAGER, l.NAME as LAGER, cfg.CSV_EXPORT_PATH');
    query.SQL.Add ('from V_SPEDITIONEN sped inner join V_SPED_CONFIG cfg on (cfg.REF_SPED=sped.REF) inner join V_MANDANT m on (m.REF=sped.REF_MAND) left outer join V_LAGER l on (l.REF=sped.REF_LAGER) where sped.REF=:ref_sped');
    query.Parameters.ParamByName('ref_sped').Value := RefSped;

    try
      query.Open;

      if (query.FieldByName ('DFUE_ART').AsString = 'GLS-IT-WEB') then begin
        relquery.SQL.Clear;

        //Es muss nur jedes Gateway einmal aufgerufen werden
        relquery.SQL.Add ('select REF from V_SPED_GATEWAY where REF_SPED=:ref_sped and REF_LOCATION=:ref_loc');
        relquery.SQL.Add ('union');
        relquery.SQL.Add ('select REF from V_SPED_GATEWAY where REF_SPED=:ref_sped_r and REF_LOCATION=:ref_loc_r');
        relquery.Parameters.ParamByName('ref_sped').Value := RefSped;
        relquery.Parameters.ParamByName('ref_loc').Value := LVSDatenModul.AktLocationRef;
        relquery.Parameters.ParamByName('ref_sped_r').Value := RefSped;
        relquery.Parameters.ParamByName('ref_loc_r').Value := LVSDatenModul.AktLocationRef;

        infowin := TInfoWin.Create (AOwner);

        infowin.Caption := FormatResourceText (1239, [query.FieldByName('SPEDITION').AsString]);
        infowin.Label1.Caption := GetResourceText (1237);

        infowin.BeginShowModal;

        Screen.Cursor := crHourGlass;

        try
          relquery.Open;

          while not (relquery.Eof) and (res = 0) do begin
            flag := false;

            repeat
              res := CloseGLSITWorkDay (RefSped, relquery.Fields [0].AsInteger, flag);
            until (res <> 0) or flag;

            relquery.Next;
          end;

          relquery.Close;
        finally
          Screen.Cursor := crDefault;

          infowin.EndShowModal;
          infowin.Free;
        end;
      end;

      query.Close;
    except
      res := -2;
    end;
  finally
    query.Free;
    relquery.Free;
  end;

  Result := res;

  {$ifdef Trace}
    TraceResult ('ErrorText', ErrorText);
    FunctionStop (Result);
  {$endif}
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function DeleteVersandInfos (AOwner: TComponent; const RefNVE : Integer; const Sped : String; var ErrorText : String) : Integer;
var
  refauf       : Integer;
  csvstr       : AnsiString;
  fname        : String;
  res          : Integer;
  fstream      : TFileStream;
  query        : TADOQuery;
begin
  res := 0;
  ErrorText := '';

  {$ifdef Trace}
    FunctionStart ('DeleteVersandInfos');
    TraceParameter ('RefNVE', RefNVE);
  {$endif}

  query := TADOQuery.Create (Nil);

  try
    query.LockType := ltReadOnly;
    query.Connection := LVSDatenModul.MainADOConnection;

    query.SQL.Add ( 'select a.REF, sped.REF as REF_SPED, av.SPEDITION_NR, sped.DFUE_ART, a.REF_MAND, a.MANDANT, a.REF_LIEFLAGER, a.AUFTRAG_NR, a.REF_KUNDEN_ADR, a.REF_LIEFER_ADR, nve.BRUTTO_GEWICHT'
                   +',nve.NVE_NR, av.OPT_NACHNAHME, nve.SENDUNGS_NR, nve.PACKAGE_NR, lif.AUSLIEFER_NR, nvl (sped.DFUE_KENNZEICHEN, sped.NAME) as SPED_NAME'
                   +',(select count (*) from V_NVE_01 where STATUS<>''DEL'' and REF_AUF_KOPF=a.REF) as PACKAGE_COUNT'
                   +',gate.GATEWAY,gate.SENDIT_CLIENT'
                   +' from V_AUFTRAG_01 a inner join V_AUFTRAG_VERSAND av on (av.REF_AUF_KOPF=a.REF) inner join V_LIEFERUNG_01 lif on (lif.REF=a.REF_LIEFERUNG) left outer join V_SPED_GATEWAY gate on (gate.REF_SPED=sped.REF and gate.REF_LOCATION=:ref_loc)'
                   +' inner join V_NVE_01 nve on (a.REF=nve.REF_AUF_KOPF) left outer join V_SPEDITIONEN sped on (sped.REF=nvl (nve.REF_SPED,av.REF_SPED)) where nve.REF=:ref_nve');
    query.Parameters.ParamByName('ref_nve').Value := RefNVE;
    query.Parameters.ParamByName('ref_loc').Value := LVSDatenModul.AktLocationRef;

    try
      query.Open;

      if not (query.FieldByName ('REF_SPED').IsNull) then begin
        refauf := query.FieldByName ('REF').AsInteger;

        if (query.FieldByName ('DFUE_ART').AsString ='SENDIT') or (query.FieldByName ('DFUE_ART').AsString ='SENDIT-PRT') then begin
          loadfname := '';

          CheckConfigParameter (query.FieldByName('REF_MAND').AsInteger, -1, query.FieldByName('REF_LIEFLAGER').AsInteger, 'SENDIT_CSV_PATH', SendITServerPath);

          if (Length (SendITServerPath) = 0) then
            res := -11
          else if (CheckSendITPath (SendITServerPath, ErrorText) <> 0) then
            res := -12
          else begin
            if (SendITServerPath [Length (SendITServerPath)] <> '\') then SendITServerPath := SendITServerPath + '\';

            csvstr := 'Cancel' + ';'+query.FieldByName('SPED_NAME').AsString+';;0;';

            if not (query.FieldByName('SENDIT_CLIENT').IsNull) then
              csvstr := csvstr + query.FieldByName('SENDIT_CLIENT').AsString
            else csvstr := csvstr + query.FieldByName('MANDANT').AsString;

            csvstr := csvstr + ';;;;';

            csvstr := csvstr + ';' + query.FieldByName('NVE_NR').AsString;

            csvstr := csvstr + ';;;;;;;;;;;';

            fname := SendITServerPath + 'Import\' + 'pcd_d_' + query.FieldByName('MANDANT').AsString+'_' + query.FieldByName('AUFTRAG_NR').AsString;

            try
              fstream := TFileStream.Create (fname + '.tmp', fmCreate);
            except
              fstream := Nil;
            end;

            if not Assigned (fstream) then
              res := -7
            else begin
              fstream.WriteBuffer (Pointer (csvstr)^, Length (csvstr));

              fstream.Free;

              //Und dann erst freigeben
              RenameFile (fname + '.tmp', fname + '.csv');
            end;
          end;
        end;

        query.Close;
      end;
    except
      res := -2;
    end;
  finally
    query.Free;
  end;

  Result := res;

  {$ifdef Trace}
    TraceResult ('ErrorText', ErrorText);
    FunctionStop (Result);
  {$endif}
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function ImportVersandInfos (const RefSped : Integer; const VersandApp : String; var ErrorText : String) : integer;
var
  res         : Integer;
  fres        : Integer;
  readanz,
  ref_send_nr : Integer;
  spedquery   : TADOQuery;
  selquery    : TADOQuery;
  iostr,
  url,
  csvpath     : String;
  srec        : TSearchRec;
  fstream     : TFileStream;
  implist     : TStringList;
begin
  res := 0;
  ErrorText := '';

  spedquery     := TADOQuery.Create (Nil);
  selquery     := TADOQuery.Create (Nil);

  try
    spedquery.LockType := ltReadOnly;
    spedquery.Connection := LVSDatenModul.MainADOConnection;

    selquery.LockType := ltReadOnly;
    selquery.Connection := LVSDatenModul.MainADOConnection;

    spedquery.SQL.Add ('select sped.REF as REF_SPED, sped.DFUE_ART, nvl (sped.DFUE_KENNZEICHEN, sped.NAME) as SPEDITION, sped.REF_MAND, m.NAME as MANDANT, sped.REF_LAGER, l.NAME as LAGER, cfg.CSV_EXPORT_PATH');
    spedquery.SQL.Add ('from V_SPEDITIONEN sped inner join V_SPED_CONFIG cfg on (cfg.REF_SPED=sped.REF) inner join V_MANDANT m on (m.REF=sped.REF_MAND) left outer join V_LAGER l on (l.REF=sped.REF_LAGER) where sped.REF=:ref_sped');
    spedquery.Parameters.ParamByName('ref_sped').Value := RefSped;

    try
      spedquery.Open;

      if (spedquery.FieldByName ('DFUE_ART').AsString ='SENDIT') or (spedquery.FieldByName ('DFUE_ART').AsString ='SENDIT-PRT') or (spedquery.FieldByName ('DFUE_ART').AsString ='BARSHIP_CSV') then begin
        if not (spedquery.FieldByName ('CSV_EXPORT_PATH').IsNull) then
          csvpath := spedquery.FieldByName ('CSV_EXPORT_PATH').AsString
        else if (VersandApp = SENDIT) then begin
          CheckConfigParameter (spedquery.FieldByName('REF_MAND').AsInteger, LVSDatenModul.AktLocationRef, DBGetReferenz (spedquery.FieldByName('REF_LAGER')), 'SENDIT_CSV_PATH', csvpath);
        end else if (VersandApp = BARCODE_SHIPPING) then begin
          CheckConfigParameter (spedquery.FieldByName('REF_MAND').AsInteger, LVSDatenModul.AktLocationRef, DBGetReferenz (spedquery.FieldByName('REF_LAGER')), 'BARSHIP_CSV_PATH', csvpath);
        end;

        if (Length (csvpath) = 0) then
          res := -11
        else if (VersandApp = SENDIT) and (CheckSendITPath (csvpath, ErrorText) <> 0) then
          res := -12
        else begin
          if (csvpath [Length (csvpath)] <> '\') then csvpath := csvpath + '\';

          fres := FindFirst (csvpath + 'Export\*.csv', faArchive, srec);

          if (fres = 0) then begin
            while (fres = 0) and (res = 0) do begin
              try
                fstream := TFileStream.Create (csvpath + 'Export\' + srec.Name, fmOpenRead + fmShareDenyWrite);
              except
                fstream := Nil;
              end;

              if not Assigned (fstream) then begin
                if (res <> 0) and Assigned (SendITLog) then begin
                  SendITLog.Logging (clError, 'Import: '+csvpath + srec.Name+', Error:'+'Datei kann nicht geöffnet werden, '+GetAPIErrorMessage (GetLastError)+CR+LF);
                end;
              end else begin
                try
                  SetLength (iostr, 1024);

                  readanz := fstream.Read (PChar(iostr)^, 1024);
                  SetLength (iostr, readanz);

                  {$ifdef Trace}
                    TraceResult ('iostr', iostr);
                  {$endif}

                  implist := TStringList.Create;

                  try
                    implist.Delimiter := ';';
                    implist.StrictDelimiter := True;

                    implist.DelimitedText := iostr;

                    {$ifdef Trace}
                      TraceResult ('implist.Count', implist.Count);
                    {$endif}

                    if (VersandApp = SENDIT) then begin
                      if (implist.Count < 6) then begin
                        res := -27;
                        ErrorText := 'Versand: Ungültige '+VersandApp+' Rückmeldung';
                      end else begin
                        //Fehler in der SendIT URL das &amp; stört da
                        if ((implist.Count >= 17) and (copy (implist [4],1,4) = 'idc=') and (implist [12] = '0')) then begin
                          if (res = 0) then begin
                            if (copy (implist [3], Length (implist [3]) - 3) = '&amp') then
                              url := copy (implist [3], 1, Length (implist [3]) - 3) + implist [4]
                            else
                              url := implist [3];

                            if (Length (implist [0]) = 0) or (Length (url) = 0) then begin
                              //res := SetNVESendungsNr (RefNVE, VersandApp, Sped, implist [2])
                            end else if (Length (implist [16]) > 0) then begin
                              //res := SetNVESendungsNrURL (RefNVE, VersandApp, implist [0], implist [2], url, implist [16], ref_send_nr)
                            end else begin
                              //res := SetNVESendungsNrURL (RefNVE, VersandApp, implist [0], implist [2], url);
                            end;

                            if (res <> 0) then
                              ErrorText := LVSDatenModul.LastLVSErrorText;
                          end;
                        end else if ((implist.Count >= 17) and (implist [11] = '0')) then begin
                          if (res = 0) then begin
                            if (Length (implist [0]) = 0) or (Length (implist [3]) = 0) then begin
                              //res := SetNVESendungsNr (RefNVE, VersandApp, Sped, implist [2])
                            end else if (Length (implist [15]) > 0) then begin
                              //res := SetNVESendungsNrURL (RefNVE, VersandApp, implist [0], implist [2], implist [3], implist [15], ref_send_nr)
                            end else begin
                              //res := SetNVESendungsNrURL (RefNVE, VersandApp, implist [0], implist [2], implist [3]);
                            end;

                            if (res <> 0) then
                              ErrorText := LVSDatenModul.LastLVSErrorText;
                          end;
                        end else if ((implist.Count < 17) and (implist [11] = '0')) then begin
                          if (res = 0) then begin
                            if (Length (implist [3]) = 0) then begin
                              //res := SetNVESendungsNr (RefNVE, VersandApp, Sped, implist [2])
                            end else begin
                              //res := SetNVESendungsNrURL (RefNVE, VersandApp, implist [0], implist [2], implist [3]);
                            end;

                            if (res <> 0) then
                              ErrorText := LVSDatenModul.LastLVSErrorText;
                          end;
                        end else begin
                          res := -28;

                          ErrorText := 'Versand: '+VersandApp+' Fehler:'+#13+implist [12];

                          if (copy (ErrorText, 1, Length ('Error from GLS-Box')) = 'Error from GLS-Box') then begin
                            if (Pos ('E006', ErrorText) > 0) then begin
                              if (Pos ('T330', ErrorText) > 0) then
                                ErrorText := ErrorText +#13+#13+'Postleitzahl stimmt nicht'
                              else ErrorText := ErrorText +#13+#13+'Routing kann nicht ermittelt werden';
                            end;
                          end;
                        end;
                      end;
                    end else if (VersandApp = BARCODE_SHIPPING) then begin
                      if (implist.Count = 2) then begin
                        ErrorText := ErrorText +#13+#13+'Keine infos zum Auftrag';
                      end else if (implist.Count < 50) then begin
                        res := -27;
                        ErrorText := 'Versand: Ungültige '+VersandApp+' Rückmeldung';
                      end else begin

                        if (Length (implist [22]) > 0) and (Length (implist [49]) > 0) then begin
                          selquery.SQL.Clear;
                          selquery.SQL.Add ('select nve.REF from VQ_AUFTRAG auf left outer join V_NVE nve on (nve.REF_AUF_KOPF=auf.REF and nve.SENDUNGS_NR is null) where auf.AUFTRAG_NR=:auf_nr');
                          selquery.Parameters [0].Value := implist [22];

                          selquery.Open;

                          if (selquery.RecordCount = 0) then
                            ErrorText := ErrorText +#13+#13+'Keine Infos zum Auftrag '+implist [22]
                          else if(selquery.Fields [0].IsNull) then
                            ErrorText := ErrorText +#13+#13+'Keine NVEs ohne Sendungsnummer für Auftrag '+implist [22]
                          else begin
                            res := SetNVESendungsNrURL (selquery.Fields [0].AsInteger, VersandApp, spedquery.FieldByName ('SPEDITION').AsString, implist [49], implist [1], '', implist [1], ref_send_nr);
                          end;

                          selquery.Close;
                        end;
                      end;
                    end;
                  finally
                    implist.Free;
                  end;
                finally
                  fstream.Free;
                end;

                MoveResponse (csvpath + 'Export\' + srec.Name, (res <> 0));

                if Assigned (SendITLog) then begin
                  if (Length (ErrorText) = 0) then
                    SendITLog.Logging (clNormal, 'Import: '+csvpath + srec.Name+CR+LF)
                  else
                    SendITLog.Logging (clError, 'Import: '+csvpath + srec.Name+', Error:'+ErrorText+CR+LF);
                end;
              end;

              fres := FindNext (srec);
            end;

            FindClose (srec);
          end;
        end;
      end;
    except
    end;
  finally
    selquery.Free;
    spedquery.Free;
  end;

  if Assigned (SendITLog) then
    SendITLog.CheckLogRotation;

  Result := res;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description  : Reparaturscrip für DHL ES Sendungsnummern
//******************************************************************************
//* Return Value :
//******************************************************************************
function GetDHLESSendungsNr : Integer;
var
  res         : Integer;
  ref_send_nr : Integer;
  selquery    : TADOQuery;
  barstr,
  sendnr,
  fname    : String;
  sdata    : TFileStream;
  xmlrd    : TXMLFile;
  xmlnd    : TXMLTagEntry;
  xmsnd    : TXMLTagEntry;
  tagstr   : String;
  ErrorText : String;
begin
  res := 0;
  ErrorText := '';

  selquery     := TADOQuery.Create (Nil);

  try
    selquery.LockType := ltReadOnly;
    selquery.Connection := LVSDatenModul.MainADOConnection;

    selquery.SQL.Add ('select * from LAGER_NVE where REF_SPED in (1144, 1163) and SENDUNGS_NR is not null and not (substr (SENDUNGS_NR, 3) >= ''0'' and substr (SENDUNGS_NR, 3) <= ''9'') order by REF desc');

    selquery.Open;

    while not (selquery.Eof) do begin
      try
        if not (selquery.FieldByName ('VERSAND_DATUM').IsNull) then
          fname := 's:\ZEN\Daten\soap\GLS_IT\'+FormatDateTime ('yyyymmdd', selquery.FieldByName ('VERSAND_DATUM').AsDateTime) + '\tracking_' + selquery.FieldByName ('NVE_NR').AsString + '.xml'
        else
          fname := 's:\ZEN\Daten\soap\GLS_IT\'+FormatDateTime ('yyyymmdd', selquery.FieldByName ('DATE_PRINT').AsDateTime) + '\tracking_' + selquery.FieldByName ('NVE_NR').AsString + '.xml';

        sdata := TFileStream.Create (fname, 0);

        xmlrd := TXMLFile.Create (Nil);

        xmlrd.ParseXMLFile (sdata);

        xmsnd := xmlrd.FindTag ('NumeroSpedizione');
        if not Assigned (xmsnd) then begin
          res := 25;

          xmlnd := xmlrd.FindTag ('NoteSpedizione');

          if Assigned (xmlnd) then
            ErrorText := xmlnd.NodeValue
          else begin
            xmlnd := xmlrd.FindTag ('DescrizioneErrore');

            if Assigned (xmlnd) then
              ErrorText := xmlnd.NodeValue
            else
              ErrorText := 'tracking code error'
          end;
        end else begin
          //Prüfen, ob es sich um ein Fehlerlabel handelt
          xmlnd := xmlrd.FindTag ('DescrizioneSedeDestino');

          if Assigned (xmlnd) then begin
            tagstr := xmlnd.NodeValue;

            if (tagstr = 'GLS Check') or (uppercase (tagstr) = 'ERRORE') then begin
              res := 25;

              xmlnd := xmlrd.FindTag ('DescrizioneErrore');

              if Assigned (xmlnd) then
                ErrorText := xmlnd.NodeValue
              else begin
                xmlnd := xmlrd.FindTag ('NoteSpedizione');

                if Assigned (xmlnd) then
                  ErrorText := xmlnd.NodeValue
                else
                  ErrorText := 'tracking code error'
              end;
            end;
          end;

          if (res = 0) then begin
            barstr := 'M4' + xmsnd.NodeValue;
            sendnr :=  'M4' + xmsnd.NodeValue;

            res := SetNVESendungsNrURL (selquery.FieldByName ('REF').AsInteger, 'GLS-IT', 'GLS_IT', sendnr, '', barstr, ref_send_nr);
          end;
        end;

        xmlrd.Free;
        sdata.Free;
      except
      end;

      selquery.Next;
    end;

    selquery.Close;
  finally
  end;

  Result := 0;
end;

end.
