object EditLagerbereichForm: TEditLagerbereichForm
  Left = 406
  Top = 273
  Anchors = [akLeft, akTop, akRight, akBottom]
  BorderStyle = bsDialog
  Caption = 'EditLagerbereichForm'
  ClientHeight = 566
  ClientWidth = 633
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'MS Sans Serif'
  Font.Style = []
  KeyPreview = True
  Position = poDesigned
  OnCloseQuery = FormCloseQuery
  OnCreate = FormCreate
  OnDestroy = FormDestroy
  OnShow = FormShow
  DesignSize = (
    633
    566)
  TextHeight = 13
  object Label1: TLabel
    Left = 8
    Top = 8
    Width = 124
    Height = 13
    Margins.Left = 8
    Margins.Top = 8
    Margins.Right = 8
    Margins.Bottom = 8
    Caption = 'Name des Lagerbereiches'
  end
  object Label2: TLabel
    Left = 8
    Top = 50
    Width = 65
    Height = 13
    Margins.Left = 8
    Margins.Top = 8
    Margins.Right = 8
    Margins.Bottom = 8
    Caption = 'Beschreibung'
  end
  object Label3: TLabel
    Left = 8
    Top = 98
    Width = 79
    Height = 13
    Margins.Left = 8
    Margins.Top = 8
    Margins.Right = 8
    Margins.Bottom = 8
    Caption = 'Lagerbereichsart'
  end
  object Bevel1: TBevel
    Left = 6
    Top = 92
    Width = 621
    Height = 9
    Margins.Left = 8
    Margins.Top = 8
    Margins.Right = 8
    Margins.Bottom = 8
    Anchors = [akLeft, akTop, akRight]
    Shape = bsTopLine
    ExplicitWidth = 615
  end
  object Bevel4: TBevel
    Left = 6
    Top = 185
    Width = 621
    Height = 7
    Margins.Left = 8
    Margins.Top = 8
    Margins.Right = 8
    Margins.Bottom = 8
    Anchors = [akLeft, akTop, akRight]
    Shape = bsTopLine
    ExplicitWidth = 615
  end
  object Label14: TLabel
    Left = 511
    Top = 50
    Width = 29
    Height = 13
    Margins.Left = 8
    Margins.Top = 8
    Margins.Right = 8
    Margins.Bottom = 8
    Anchors = [akTop, akRight]
    Caption = 'K'#252'rzel'
    ExplicitLeft = 505
  end
  object Label21: TLabel
    Left = 505
    Top = 98
    Width = 83
    Height = 13
    Caption = 'Projektzuordnung'
  end
  object NameEdit: TEdit
    Left = 8
    Top = 24
    Width = 617
    Height = 21
    Margins.Left = 8
    Margins.Top = 8
    Margins.Right = 8
    Margins.Bottom = 8
    Anchors = [akLeft, akTop, akRight]
    MaxLength = 64
    TabOrder = 0
    Text = 'NameEdit'
  end
  object BeschreibungEdit: TEdit
    Left = 8
    Top = 64
    Width = 487
    Height = 21
    Margins.Left = 8
    Margins.Top = 8
    Margins.Right = 8
    Margins.Bottom = 8
    MaxLength = 64
    TabOrder = 1
    Text = 'BeschreibungEdit'
  end
  object ArtComboBox: TComboBoxPro
    Left = 8
    Top = 114
    Width = 493
    Height = 21
    Margins.Left = 5
    Margins.Top = 5
    Margins.Right = 5
    Margins.Bottom = 5
    Style = csOwnerDrawFixed
    Anchors = [akLeft, akTop, akRight]
    ItemHeight = 15
    TabOrder = 3
    OnChange = ArtComboBoxChange
    Items.Strings = (
      'WE'
      'WA'
      'LAGER')
  end
  object OkButton: TButton
    Left = 466
    Top = 535
    Width = 75
    Height = 25
    Margins.Left = 8
    Margins.Top = 8
    Margins.Right = 8
    Margins.Bottom = 8
    Anchors = [akRight, akBottom]
    Caption = 'Ok'
    Default = True
    ModalResult = 1
    TabOrder = 8
  end
  object AbortButton: TButton
    Left = 550
    Top = 535
    Width = 75
    Height = 25
    Margins.Left = 8
    Margins.Top = 8
    Margins.Right = 8
    Margins.Bottom = 8
    Anchors = [akRight, akBottom]
    Cancel = True
    Caption = '&Abbrechen'
    ModalResult = 3
    TabOrder = 9
  end
  object UseForVorratCheckBox: TCheckBox
    Left = 8
    Top = 142
    Width = 265
    Height = 16
    Margins.Left = 8
    Margins.Top = 8
    Margins.Right = 8
    Margins.Bottom = 8
    Caption = 'F'#252'r die Vorratshaltung zugelassen'
    TabOrder = 5
  end
  object UseForKommCheckBox: TCheckBox
    Left = 8
    Top = 161
    Width = 265
    Height = 16
    Margins.Left = 8
    Margins.Top = 8
    Margins.Right = 8
    Margins.Bottom = 8
    Caption = 'F'#252'r die Kommissionierung zugelassen'
    TabOrder = 6
    OnClick = UseForKommCheckBoxClick
  end
  object PageControl1: TPageControl
    Left = 6
    Top = 198
    Width = 619
    Height = 329
    Margins.Left = 8
    Margins.Top = 8
    Margins.Right = 8
    Margins.Bottom = 8
    ActivePage = LPTabSheet
    Anchors = [akLeft, akTop, akRight, akBottom]
    TabOrder = 7
    object WETabSheet: TTabSheet
      Margins.Left = 8
      Margins.Top = 8
      Margins.Right = 8
      Margins.Bottom = 8
      Caption = 'Warenannahme'
      ImageIndex = 6
      DesignSize = (
        611
        301)
      object LTAnnahmeRadioGroup: TRadioGroup
        Left = 8
        Top = 41
        Width = 603
        Height = 174
        Margins.Left = 8
        Margins.Top = 8
        Margins.Right = 8
        Margins.Bottom = 8
        Anchors = [akLeft, akTop, akRight]
        Caption = 'Ladungstr'#228'ger f'#252'r die Annahme'
        Items.Strings = (
          'Keine Palettenbildung'
          'Vollpaletten Annahme '
          'Vollpaletten Annahme '#252'ber Palettenfaktor'
          'LE scannen und packen'
          'VPE annehmen und einlagern')
        TabOrder = 1
      end
      object MDEDirectStoreCheckBox: TCheckBox
        Left = 8
        Top = 232
        Width = 457
        Height = 17
        Margins.Left = 8
        Margins.Top = 8
        Margins.Right = 8
        Margins.Bottom = 8
        Caption = 'Annahme mit anschliessender Einlagerung (MDE)'
        TabOrder = 2
      end
      object SelWELPCheckBox: TCheckBox
        Left = 8
        Top = 8
        Width = 585
        Height = 17
        Margins.Left = 8
        Margins.Top = 8
        Margins.Right = 8
        Margins.Bottom = 8
        Caption = 'Es muss bei der Annahme ein WE-Lagerplatz gew'#228'hlt werden'
        TabOrder = 0
      end
      object RetLEOptCheckBox: TCheckBox
        Left = 282
        Top = 155
        Width = 289
        Height = 17
        Margins.Left = 8
        Margins.Top = 8
        Margins.Right = 8
        Margins.Bottom = 8
        Caption = 'Bei Retouren optional'
        TabOrder = 3
      end
    end
    object EinlTabSheet: TTabSheet
      Margins.Left = 8
      Margins.Top = 8
      Margins.Right = 8
      Margins.Bottom = 8
      Caption = 'Einlagerung'
      DesignSize = (
        611
        301)
      object Label7: TLabel
        Left = 8
        Top = 8
        Width = 158
        Height = 13
        Margins.Left = 8
        Margins.Top = 8
        Margins.Right = 8
        Margins.Bottom = 8
        Caption = 'Automatische Palettenbildung auf'
      end
      object Label10: TLabel
        Left = 8
        Top = 50
        Width = 104
        Height = 13
        Margins.Left = 8
        Margins.Top = 8
        Margins.Right = 8
        Margins.Bottom = 8
        Caption = 'Automatisch einlagern'
      end
      object Label16: TLabel
        Left = 204
        Top = 50
        Width = 18
        Height = 13
        Margins.Left = 8
        Margins.Top = 8
        Margins.Right = 8
        Margins.Bottom = 8
        Caption = 'Prio'
      end
      object Label17: TLabel
        Left = 284
        Top = 50
        Width = 55
        Height = 13
        Margins.Left = 8
        Margins.Top = 8
        Margins.Right = 8
        Margins.Bottom = 8
        Caption = 'ABC-Klasse'
      end
      object LTComboBox: TComboBoxPro
        Left = 8
        Top = 23
        Width = 601
        Height = 22
        Margins.Left = 8
        Margins.Top = 8
        Margins.Right = 8
        Margins.Bottom = 8
        Style = csOwnerDrawFixed
        Anchors = [akLeft, akTop, akRight]
        TabOrder = 0
      end
      object AutoStoreComboBox: TComboBox
        Left = 8
        Top = 64
        Width = 177
        Height = 21
        Margins.Left = 8
        Margins.Top = 8
        Margins.Right = 8
        Margins.Bottom = 8
        TabOrder = 1
        Text = 'Nein'
        Items.Strings = (
          'Nein'
          'Ja, unmittelbar'
          'Ja, nur planen'
          'Ja, '#252'ber Einlagertransport')
      end
      object StoreResCheckBox: TCheckBox
        Left = 8
        Top = 94
        Width = 457
        Height = 15
        Margins.Left = 8
        Margins.Top = 8
        Margins.Right = 8
        Margins.Bottom = 8
        Caption = 'Einlagerplatz reservieren'
        TabOrder = 5
      end
      object StoreDistributeCheckBox: TCheckBox
        Left = 8
        Top = 122
        Width = 457
        Height = 16
        Margins.Left = 8
        Margins.Top = 8
        Margins.Right = 8
        Margins.Bottom = 8
        Caption = #220'ber die Zonen verteilen'
        TabOrder = 6
      end
      object StoreDistrRegalCheckBox: TCheckBox
        Left = 8
        Top = 138
        Width = 457
        Height = 17
        Margins.Left = 8
        Margins.Top = 8
        Margins.Right = 8
        Margins.Bottom = 8
        Caption = #220'ber die Regale verteilen'
        TabOrder = 7
      end
      object StorePrioUpDown: TIntegerUpDown
        Left = 247
        Top = 64
        Width = 16
        Height = 21
        Margins.Left = 8
        Margins.Top = 8
        Margins.Right = 8
        Margins.Bottom = 8
        Associate = StorePrioEdit
        Min = -1
        Max = 999
        TabOrder = 3
      end
      object StorePrioEdit: TEdit
        Left = 204
        Top = 64
        Width = 43
        Height = 21
        Margins.Left = 8
        Margins.Top = 8
        Margins.Right = 8
        Margins.Bottom = 8
        TabOrder = 2
        Text = '0'
        OnChange = StorePrioEditChange
      end
      object ABCComboBox: TComboBox
        Left = 284
        Top = 64
        Width = 325
        Height = 21
        Margins.Left = 8
        Margins.Top = 8
        Margins.Right = 8
        Margins.Bottom = 8
        Style = csDropDownList
        Anchors = [akLeft, akTop, akRight]
        ItemIndex = 0
        TabOrder = 4
        Items.Strings = (
          ''
          'A'
          'B'
          'C')
      end
      object AutoWECheckBox: TCheckBox
        Left = 8
        Top = 167
        Width = 457
        Height = 16
        Margins.Left = 8
        Margins.Top = 8
        Margins.Right = 8
        Margins.Bottom = 8
        Caption = 'Avise automatisch einlagern'
        TabOrder = 8
      end
      object OptWEAKTCheckBox: TCheckBox
        Left = 8
        Top = 189
        Width = 239
        Height = 16
        Margins.Left = 8
        Margins.Top = 8
        Margins.Right = 8
        Margins.Bottom = 8
        Caption = 'WE-Positionen nach Einlagerung verf'#252'gbar'
        TabOrder = 9
      end
      object StoreSelectionRadioGroup: TRadioGroup
        Left = 9
        Top = 218
        Width = 358
        Height = 43
        Margins.Left = 8
        Margins.Top = 8
        Margins.Right = 8
        Margins.Bottom = 8
        Caption = 'Einlager Auswahl'
        Columns = 3
        Items.Strings = (
          'Immer LE'
          'Immer Bestand'
          'Auswahl')
        TabOrder = 11
      end
      object RetourenSortCheckBox: TCheckBox
        Left = 284
        Top = 189
        Width = 239
        Height = 16
        Margins.Left = 8
        Margins.Top = 8
        Margins.Right = 8
        Margins.Bottom = 8
        Caption = 'Als Retourenbereich vorsortieren'
        TabOrder = 10
      end
    end
    object LPTabSheet: TTabSheet
      Margins.Left = 8
      Margins.Top = 8
      Margins.Right = 8
      Margins.Bottom = 8
      Caption = 'Lagerpl'#228'tze'
      ImageIndex = 3
      DesignSize = (
        611
        301)
      object Label15: TLabel
        Left = 8
        Top = 231
        Width = 137
        Height = 13
        Margins.Left = 8
        Margins.Top = 8
        Margins.Right = 8
        Margins.Bottom = 8
        Caption = 'Max. Anzahl untersch. Artikel'
      end
      object Label22: TLabel
        Left = 327
        Top = 62
        Width = 165
        Height = 13
        Caption = 'Recht f'#252'r die Bestands'#228'nderungen'
      end
      object LPShowRadioGroup: TRadioGroup
        Left = 8
        Top = 11
        Width = 605
        Height = 40
        Margins.Left = 8
        Margins.Top = 8
        Margins.Right = 8
        Margins.Bottom = 8
        Anchors = [akLeft, akTop, akRight]
        Caption = 'Anzeige als'
        Columns = 4
        Items.Strings = (
          'Koordinaten'
          'LP-Nummer'
          'ERP-Namen'
          'LP-Name')
        TabOrder = 0
      end
      object AutoLEDeleteCheckBox: TCheckBox
        Left = 8
        Top = 48
        Width = 257
        Height = 46
        Margins.Left = 8
        Margins.Top = 8
        Margins.Right = 8
        Margins.Bottom = 8
        Caption = 
          'Leer gewordene LEs werden automatisch von den LPs abgebucht und ' +
          'gel'#246'scht'
        TabOrder = 1
        WordWrap = True
      end
      object StoreSingleMatCheckBox: TCheckBox
        Left = 8
        Top = 210
        Width = 257
        Height = 16
        Margins.Left = 8
        Margins.Top = 8
        Margins.Right = 8
        Margins.Bottom = 8
        Caption = 'Artikelreine Lagerung'
        TabOrder = 6
        OnClick = StoreSingleMatCheckBoxClick
      end
      object StoreAddMatCheckBox: TCheckBox
        Left = 8
        Top = 132
        Width = 257
        Height = 16
        Margins.Left = 8
        Margins.Top = 8
        Margins.Right = 8
        Margins.Bottom = 8
        Caption = 'Zulagerung zul'#228'ssig'
        TabOrder = 3
        OnClick = ArtComboBoxChange
      end
      object StoreChargenCheckBox: TCheckBox
        Left = 8
        Top = 181
        Width = 257
        Height = 15
        Margins.Left = 8
        Margins.Top = 8
        Margins.Right = 8
        Margins.Bottom = 8
        Caption = 'Nur chargenreine Lagerung zul'#228'ssig'
        TabOrder = 5
        OnClick = ArtComboBoxChange
      end
      object MaxDiffArEdit: TEdit
        Left = 158
        Top = 228
        Width = 40
        Height = 21
        Margins.Left = 8
        Margins.Top = 8
        Margins.Right = 8
        Margins.Bottom = 8
        MaxLength = 3
        TabOrder = 7
        Text = 'MaxDiffArEdit'
        OnKeyPress = IntegerEditKeyPress
      end
      object StoreMHDReinCheckBox: TCheckBox
        Left = 8
        Top = 163
        Width = 368
        Height = 16
        Margins.Left = 8
        Margins.Top = 8
        Margins.Right = 8
        Margins.Bottom = 8
        AllowGrayed = True
        Caption = 'Nur ein MHD auf dem Platz zul'#228'ssig'
        State = cbGrayed
        TabOrder = 4
      end
      object MergeBesCheckBox: TCheckBox
        Left = 8
        Top = 105
        Width = 257
        Height = 16
        Margins.Left = 8
        Margins.Top = 8
        Margins.Right = 8
        Margins.Bottom = 8
        Caption = 'Best'#228'nde auf LEs und LPs verdichten'
        TabOrder = 2
        OnClick = ArtComboBoxChange
      end
      object StoreMixCategoryCheckBox: TCheckBox
        Left = 327
        Top = 210
        Width = 257
        Height = 16
        Margins.Left = 8
        Margins.Top = 8
        Margins.Right = 8
        Margins.Bottom = 8
        Caption = 'Mehrer Qualit'#228'ten auf einem Platz zul'#228'ssig'
        TabOrder = 9
        OnClick = StoreSingleMatCheckBoxClick
      end
      object ACOBestandComboBox: TComboBoxPro
        Left = 327
        Top = 77
        Width = 257
        Height = 21
        TabOrder = 8
        Text = 'ACOBestandComboBox'
      end
      object UseNullInvCheckBox: TCheckBox
        Left = 8
        Top = 250
        Width = 257
        Height = 16
        Margins.Left = 8
        Margins.Top = 8
        Margins.Right = 8
        Margins.Bottom = 8
        AllowGrayed = True
        Caption = 'Nulldurchgangsinventur nutzen'
        TabOrder = 10
        OnClick = StoreSingleMatCheckBoxClick
      end
    end
    object KommTabSheet: TTabSheet
      Margins.Left = 8
      Margins.Top = 8
      Margins.Right = 8
      Margins.Bottom = 8
      Caption = 'Kommissionierung'
      ImageIndex = 4
      DesignSize = (
        611
        301)
      object Label6: TLabel
        Left = 8
        Top = 8
        Width = 157
        Height = 13
        Margins.Left = 8
        Margins.Top = 8
        Margins.Right = 8
        Margins.Bottom = 8
        Caption = 'Name des Kommissionierbereichs'
      end
      object Label4: TLabel
        Left = 415
        Top = 8
        Width = 58
        Height = 13
        Margins.Left = 8
        Margins.Top = 8
        Margins.Right = 8
        Margins.Bottom = 8
        Anchors = [akTop, akRight]
        Caption = 'Komm-Folge'
        ExplicitLeft = 409
      end
      object Label8: TLabel
        Left = 8
        Top = 92
        Width = 99
        Height = 13
        Margins.Left = 8
        Margins.Top = 8
        Margins.Right = 8
        Margins.Bottom = 8
        Caption = 'Kommissionier-Ablauf'
      end
      object Gruppe: TLabel
        Left = 263
        Top = 48
        Width = 35
        Height = 13
        Margins.Left = 8
        Margins.Top = 8
        Margins.Right = 8
        Margins.Bottom = 8
        Caption = 'Gruppe'
      end
      object Label9: TLabel
        Left = 8
        Top = 48
        Width = 104
        Height = 13
        Margins.Left = 8
        Margins.Top = 8
        Margins.Right = 8
        Margins.Bottom = 8
        Caption = 'Kommissionierplanung'
      end
      object Label18: TLabel
        Left = 263
        Top = 92
        Width = 110
        Height = 13
        Margins.Left = 8
        Margins.Top = 8
        Margins.Right = 8
        Margins.Bottom = 8
        Caption = 'Kommissionier-Freigabe'
      end
      object KommNameEdit: TEdit
        Left = 8
        Top = 23
        Width = 387
        Height = 21
        Margins.Left = 8
        Margins.Top = 8
        Margins.Right = 8
        Margins.Bottom = 8
        Anchors = [akLeft, akTop, akRight]
        MaxLength = 32
        TabOrder = 0
        Text = 'KommNameEdit'
      end
      object NVEGroupBox: TGroupBox
        Left = 8
        Top = 209
        Width = 605
        Height = 61
        Margins.Left = 8
        Margins.Top = 8
        Margins.Right = 8
        Margins.Bottom = 8
        Anchors = [akLeft, akTop, akRight]
        Caption = 'NVE-Wechsel wenn...'
        TabOrder = 10
        object NVEChangeVolCheckBox: TCheckBox
          Left = 8
          Top = 20
          Width = 123
          Height = 15
          Margins.Left = 8
          Margins.Top = 8
          Margins.Right = 8
          Margins.Bottom = 8
          Caption = 'Packvolumen erreicht'
          TabOrder = 0
        end
        object NVEChangeGwCheckBox: TCheckBox
          Left = 8
          Top = 40
          Width = 123
          Height = 17
          Margins.Left = 8
          Margins.Top = 8
          Margins.Right = 8
          Margins.Bottom = 8
          Caption = 'Packgewicht erreicht'
          TabOrder = 1
        end
        object NVEChangePALCheckBox: TCheckBox
          Left = 144
          Top = 20
          Width = 117
          Height = 15
          Margins.Left = 8
          Margins.Top = 8
          Margins.Right = 8
          Margins.Bottom = 8
          Caption = 'PAL-Faktor erreicht'
          TabOrder = 2
        end
      end
      object KommOptCheckBox: TCheckBox
        Left = 8
        Top = 155
        Width = 368
        Height = 17
        Margins.Left = 8
        Margins.Top = 8
        Margins.Right = 8
        Margins.Bottom = 8
        Caption = 'Kommissionierverdichtung f'#252'r St'#252'ckartikel zul'#228'ssig'
        TabOrder = 8
      end
      object VollPALCheckBox: TCheckBox
        Left = 8
        Top = 135
        Width = 368
        Height = 16
        Margins.Left = 8
        Margins.Top = 8
        Margins.Right = 8
        Margins.Bottom = 8
        Caption = 'Vollpaletten-Kommissionierung zul'#228'ssig'
        TabOrder = 7
      end
      object KommFolgeUpDown: TUpDown
        Left = 471
        Top = 23
        Width = 14
        Height = 21
        Margins.Left = 8
        Margins.Top = 8
        Margins.Right = 8
        Margins.Bottom = 8
        Anchors = [akTop, akRight]
        Associate = KommFolgeEdit
        Min = -1
        TabOrder = 2
      end
      object KommFolgeEdit: TEdit
        Left = 415
        Top = 23
        Width = 56
        Height = 21
        Margins.Left = 8
        Margins.Top = 8
        Margins.Right = 8
        Margins.Bottom = 8
        Anchors = [akTop, akRight]
        TabOrder = 1
        Text = '0'
        OnChange = KommFolgeEditChange
        OnKeyPress = IntegerEditKeyPress
      end
      object KommAblaufComboBox: TComboBoxPro
        Left = 8
        Top = 107
        Width = 229
        Height = 22
        Margins.Left = 5
        Margins.Top = 5
        Margins.Right = 5
        Margins.Bottom = 5
        Style = csOwnerDrawFixed
        TabOrder = 5
      end
      object KommGrpComboBox: TComboBoxPro
        Left = 263
        Top = 62
        Width = 207
        Height = 22
        Margins.Left = 8
        Margins.Top = 8
        Margins.Right = 8
        Margins.Bottom = 8
        Style = csOwnerDrawFixed
        TabOrder = 4
      end
      object KommArtComboBox: TComboBoxPro
        Left = 8
        Top = 62
        Width = 229
        Height = 22
        Margins.Left = 8
        Margins.Top = 8
        Margins.Right = 8
        Margins.Bottom = 8
        Style = csOwnerDrawFixed
        TabOrder = 3
        OnChange = KommArtComboBoxChange
      end
      object KommFreigabeComboBox: TComboBoxPro
        Left = 263
        Top = 107
        Width = 205
        Height = 21
        Margins.Left = 8
        Margins.Top = 8
        Margins.Right = 8
        Margins.Bottom = 8
        Style = csDropDownList
        TabOrder = 6
        Items.Strings = (
          'Immer manuell'
          'Immer automatisch'
          'Bei Auftragsfreigabe'
          'Bei Verladefreigabe')
      end
      object DistributePicksCheckBox: TCheckBox
        Left = 8
        Top = 183
        Width = 368
        Height = 15
        Margins.Left = 8
        Margins.Top = 8
        Margins.Right = 8
        Margins.Bottom = 8
        Caption = 'Pick auf alle m'#246'glichen Pl'#228'tze gleichm'#228'ssig verteilen'
        TabOrder = 9
      end
    end
    object NachsTabSheet: TTabSheet
      Margins.Left = 8
      Margins.Top = 8
      Margins.Right = 8
      Margins.Bottom = 8
      Caption = 'Nachschub'
      ImageIndex = 1
      DesignSize = (
        611
        301)
      object Label11: TLabel
        Left = 8
        Top = 8
        Width = 67
        Height = 13
        Margins.Left = 8
        Margins.Top = 8
        Margins.Right = 8
        Margins.Bottom = 8
        Caption = 'Nachschubart'
      end
      object Label20: TLabel
        Left = 342
        Top = 8
        Width = 144
        Height = 13
        Margins.Left = 8
        Margins.Top = 8
        Margins.Right = 8
        Margins.Bottom = 8
        Anchors = [akTop, akRight]
        Caption = 'Zul'#228'ssige Nachschubbereiche'
        ExplicitLeft = 336
      end
      object NachArtComboBox: TComboBoxPro
        Left = 8
        Top = 23
        Width = 272
        Height = 22
        Margins.Left = 8
        Margins.Top = 8
        Margins.Right = 8
        Margins.Bottom = 8
        Style = csOwnerDrawFixed
        Anchors = [akLeft, akTop, akRight]
        TabOrder = 0
      end
      object NachsGroupBox: TGroupBox
        Left = 8
        Top = 80
        Width = 272
        Height = 81
        Margins.Left = 8
        Margins.Top = 8
        Margins.Right = 8
        Margins.Bottom = 8
        Anchors = [akLeft, akTop, akRight]
        TabOrder = 2
        object Label12: TLabel
          Left = 8
          Top = 27
          Width = 106
          Height = 13
          Margins.Left = 8
          Margins.Top = 8
          Margins.Right = 8
          Margins.Bottom = 8
          Caption = 'Faktor f'#252'r Untergrenze'
        end
        object Label13: TLabel
          Left = 8
          Top = 49
          Width = 103
          Height = 13
          Margins.Left = 8
          Margins.Top = 8
          Margins.Right = 8
          Margins.Bottom = 8
          Caption = 'Faktor f'#252'r Obergrenze'
        end
        object MinFaktorEdit: TEdit
          Left = 156
          Top = 25
          Width = 35
          Height = 21
          Margins.Left = 8
          Margins.Top = 8
          Margins.Right = 8
          Margins.Bottom = 8
          MaxLength = 4
          TabOrder = 0
          Text = 'MinFaktorEdit'
          OnKeyPress = FaktorEditKeyPress
        end
        object MaxFaktorEdit: TEdit
          Left = 156
          Top = 46
          Width = 35
          Height = 21
          Margins.Left = 8
          Margins.Top = 8
          Margins.Right = 8
          Margins.Bottom = 8
          MaxLength = 4
          TabOrder = 2
          Text = 'MaxFaktorEdit'
          OnKeyPress = FaktorEditKeyPress
        end
        object MinNachComboBox: TComboBox
          Left = 197
          Top = 25
          Width = 48
          Height = 21
          Margins.Left = 8
          Margins.Top = 8
          Margins.Right = 8
          Margins.Bottom = 8
          Style = csDropDownList
          ItemIndex = 0
          TabOrder = 1
          Text = '%'
          Items.Strings = (
            '%'
            'VPE')
        end
        object MaxNachComboBox: TComboBox
          Left = 197
          Top = 46
          Width = 48
          Height = 21
          Margins.Left = 8
          Margins.Top = 8
          Margins.Right = 8
          Margins.Bottom = 8
          Style = csDropDownList
          ItemIndex = 0
          TabOrder = 3
          Text = '%'
          Items.Strings = (
            '%'
            'VPE')
        end
      end
      object AutoNachCheckBox: TCheckBox
        Left = 16
        Top = 76
        Width = 183
        Height = 17
        Margins.Left = 8
        Margins.Top = 8
        Margins.Right = 8
        Margins.Bottom = 8
        AllowGrayed = True
        Caption = 'Automatische Nachschubsteuerung'
        TabOrder = 1
        OnClick = AutoNachCheckBoxClick
      end
      object NachschubBereichListBox: TCheckListBox
        Left = 336
        Top = 23
        Width = 267
        Height = 259
        Margins.Left = 8
        Margins.Top = 8
        Margins.Right = 8
        Margins.Bottom = 8
        Anchors = [akLeft, akTop, akRight, akBottom]
        DragMode = dmAutomatic
        ItemHeight = 17
        TabOrder = 5
        OnDragOver = CheckListBoxDragOver
      end
      object TeilNachschubCheckBox: TCheckBox
        Left = 8
        Top = 184
        Width = 258
        Height = 17
        Caption = 'Teilnachsch'#252'be sind zul'#228'ssig'
        TabOrder = 3
      end
      object NotifyCheckBox: TCheckBox
        Left = 8
        Top = 218
        Width = 272
        Height = 17
        Caption = 'Nachschubbegin avisieren'
        TabOrder = 4
      end
    end
    object MFRTabSheet: TTabSheet
      Margins.Left = 8
      Margins.Top = 8
      Margins.Right = 8
      Margins.Bottom = 8
      Caption = 'Materialflu'#223
      ImageIndex = 2
      DesignSize = (
        611
        301)
      object Label5: TLabel
        Left = 8
        Top = 8
        Width = 207
        Height = 13
        Margins.Left = 8
        Margins.Top = 8
        Margins.Right = 8
        Margins.Bottom = 8
        Caption = 'Name des zugeh'#246'rigen Regalbedienger'#228'tes'
      end
      object Label19: TLabel
        Left = 8
        Top = 54
        Width = 117
        Height = 13
        Margins.Left = 8
        Margins.Top = 8
        Margins.Right = 8
        Margins.Bottom = 8
        Caption = 'Zul'#228'ssige Transportarten'
      end
      object RBGEdit: TEdit
        Left = 8
        Top = 23
        Width = 596
        Height = 21
        Margins.Left = 8
        Margins.Top = 8
        Margins.Right = 8
        Margins.Bottom = 8
        Anchors = [akLeft, akTop, akRight]
        TabOrder = 0
        Text = 'RBGEdit'
      end
      object TransportTypeCheckListBox: TCheckListBox
        Left = 8
        Top = 68
        Width = 596
        Height = 226
        Margins.Left = 8
        Margins.Top = 8
        Margins.Right = 8
        Margins.Bottom = 8
        Anchors = [akLeft, akTop, akRight, akBottom]
        ItemHeight = 30
        Style = lbOwnerDrawFixed
        TabOrder = 1
        Visible = False
        OnDrawItem = TransportTypeCheckListBoxDrawItem
      end
    end
    object ZollTabSheet: TTabSheet
      Margins.Left = 8
      Margins.Top = 8
      Margins.Right = 8
      Margins.Bottom = 8
      Caption = 'Zoll'
      ImageIndex = 5
      object ZollBereichCheckBox: TCheckBox
        Left = 8
        Top = 16
        Width = 368
        Height = 17
        Margins.Left = 8
        Margins.Top = 8
        Margins.Right = 8
        Margins.Bottom = 8
        Caption = 'Dieser Lagerbereich ist zollfreie Zone'
        TabOrder = 0
      end
    end
    object HousekeepingTabSheet: TTabSheet
      Margins.Left = 8
      Margins.Top = 8
      Margins.Right = 8
      Margins.Bottom = 8
      Caption = 'Housekeeping'
      ImageIndex = 7
      object HousekeepingPageControl: TPageControl
        Left = 0
        Top = 0
        Width = 611
        Height = 301
        Margins.Left = 8
        Margins.Top = 8
        Margins.Right = 8
        Margins.Bottom = 8
        ActivePage = AutoInventurTabSheet
        Align = alClient
        TabOrder = 0
        object AutoInventurTabSheet: TTabSheet
          Margins.Left = 8
          Margins.Top = 8
          Margins.Right = 8
          Margins.Bottom = 8
          Caption = 'Automatische Inventur'
          object AInvIntervalLabel: TLabel
            Left = 15
            Top = 42
            Width = 140
            Height = 13
            Margins.Left = 8
            Margins.Top = 8
            Margins.Right = 8
            Margins.Bottom = 8
            Caption = 'Interval der Inventurerstellung'
          end
          object AInvIntervalTageLabel: TLabel
            Left = 218
            Top = 62
            Width = 25
            Height = 13
            Margins.Left = 8
            Margins.Top = 8
            Margins.Right = 8
            Margins.Bottom = 8
            Caption = 'Tage'
          end
          object AInvGroupBox: TGroupBox
            Left = 3
            Top = 91
            Width = 584
            Height = 115
            Margins.Left = 8
            Margins.Top = 8
            Margins.Right = 8
            Margins.Bottom = 8
            Caption = 'Lagerplatzauswahl'
            Enabled = False
            TabOrder = 0
            object AInvAlterLabel: TLabel
              Left = 12
              Top = 64
              Width = 145
              Height = 13
              Margins.Left = 8
              Margins.Top = 8
              Margins.Right = 8
              Margins.Bottom = 8
              Caption = 'Letzte Inventur vor mindestens'
            end
            object AInvAlterTageLabel: TLabel
              Left = 216
              Top = 84
              Width = 25
              Height = 13
              Margins.Left = 8
              Margins.Top = 8
              Margins.Right = 8
              Margins.Bottom = 8
              Caption = 'Tage'
            end
            object AInvCountLPLabel: TLabel
              Left = 216
              Top = 38
              Width = 55
              Height = 13
              Margins.Left = 8
              Margins.Top = 8
              Margins.Right = 8
              Margins.Bottom = 8
              Caption = 'Lagerpl'#228'tze'
            end
            object AInvCountLabel: TLabel
              Left = 12
              Top = 20
              Width = 79
              Height = 13
              Margins.Left = 8
              Margins.Top = 8
              Margins.Right = 8
              Margins.Bottom = 8
              Caption = 'Maximale Anzahl'
            end
            object AInvCountEdit: TEdit
              Left = 12
              Top = 34
              Width = 199
              Height = 21
              Margins.Left = 8
              Margins.Top = 8
              Margins.Right = 8
              Margins.Bottom = 8
              Enabled = False
              NumbersOnly = True
              TabOrder = 0
              OnKeyPress = IntegerEditKeyPress
            end
            object AInvAlterEdit: TEdit
              Left = 12
              Top = 80
              Width = 199
              Height = 21
              Margins.Left = 8
              Margins.Top = 8
              Margins.Right = 8
              Margins.Bottom = 8
              Enabled = False
              NumbersOnly = True
              TabOrder = 1
              OnKeyPress = IntegerEditKeyPress
            end
          end
          object AInvCheckBox: TCheckBox
            Left = 15
            Top = 15
            Width = 344
            Height = 16
            Margins.Left = 8
            Margins.Top = 8
            Margins.Right = 8
            Margins.Bottom = 8
            Caption = 'Automatische Erstellung von Inventuren f'#252'r diesen Lagerbereich'
            Color = clBtnFace
            ParentColor = False
            TabOrder = 1
            OnClick = AInvCheckBoxClick
          end
          object AInvIntervalEdit: TEdit
            Left = 15
            Top = 58
            Width = 198
            Height = 21
            Margins.Left = 8
            Margins.Top = 8
            Margins.Right = 8
            Margins.Bottom = 8
            Enabled = False
            NumbersOnly = True
            TabOrder = 2
            OnKeyPress = IntegerEditKeyPress
          end
          object AInvFreigebenCheckbox: TCheckBox
            Left = 15
            Top = 222
            Width = 344
            Height = 16
            Margins.Left = 8
            Margins.Top = 8
            Margins.Right = 8
            Margins.Bottom = 8
            Caption = 'Inventuren direkt freigeben'
            Color = clBtnFace
            ParentColor = False
            TabOrder = 3
          end
        end
        object RuecklagerungTabSheet: TTabSheet
          Margins.Left = 8
          Margins.Top = 8
          Margins.Right = 8
          Margins.Bottom = 8
          Caption = 'R'#252'cklagerung'
          ImageIndex = 1
          object RueckLBLabel: TLabel
            Left = 15
            Top = 42
            Width = 190
            Height = 13
            Margins.Left = 8
            Margins.Top = 8
            Margins.Right = 8
            Margins.Bottom = 8
            Caption = 'Ziellagerbereich bei abgelaufenem MHD'
          end
          object RueckFuellgradProcentLabel: TLabel
            Left = 413
            Top = 125
            Width = 8
            Height = 13
            Margins.Left = 8
            Margins.Top = 8
            Margins.Right = 8
            Margins.Bottom = 8
            Caption = '%'
          end
          object RueckTriggerBedingungRadioGroup: TRadioGroup
            Left = 3
            Top = 91
            Width = 182
            Height = 121
            Caption = 'Bedingung f'#252'r eine R'#252'cklagerung'
            Items.Strings = (
              'Maximaler-F'#252'llgrad in Prozent'
              'Mindestens freie Lagerpl'#228'tze')
            ShowFrame = False
            TabOrder = 4
            OnClick = RueckTriggerBedingungRadioGroupClick
          end
          object RueckLBComboBox: TComboBoxPro
            Left = 15
            Top = 58
            Width = 222
            Height = 22
            Margins.Left = 8
            Margins.Top = 8
            Margins.Right = 8
            Margins.Bottom = 8
            Style = csOwnerDrawFixed
            Enabled = False
            TabOrder = 1
          end
          object RueckCheckBox: TCheckBox
            Left = 15
            Top = 15
            Width = 404
            Height = 16
            Margins.Left = 8
            Margins.Top = 8
            Margins.Right = 8
            Margins.Bottom = 8
            Caption = 
              'Automatische Erstellung von R'#252'cklagerungen f'#252'r diesen Lagerberei' +
              'ch'
            TabOrder = 0
            OnClick = RueckCheckBoxClick
          end
          object RueckFuellgradEdit: TEdit
            Left = 182
            Top = 121
            Width = 222
            Height = 21
            Margins.Left = 8
            Margins.Top = 8
            Margins.Right = 8
            Margins.Bottom = 8
            Enabled = False
            TabOrder = 2
            OnKeyPress = IntegerEditKeyPress
          end
          object RueckFreeLPEdit: TEdit
            Left = 182
            Top = 168
            Width = 222
            Height = 21
            Margins.Left = 8
            Margins.Top = 8
            Margins.Right = 8
            Margins.Bottom = 8
            Enabled = False
            TabOrder = 3
            OnKeyPress = IntegerEditKeyPress
          end
        end
        object ABCKlassifizierungTabSheet: TTabSheet
          Margins.Left = 8
          Margins.Top = 8
          Margins.Right = 8
          Margins.Bottom = 8
          Caption = 'ABC-Klassifizierung'
          ImageIndex = 2
          TabVisible = False
        end
      end
    end
  end
  object ShortNameEdit: TEdit
    Left = 511
    Top = 64
    Width = 114
    Height = 21
    Margins.Left = 8
    Margins.Top = 8
    Margins.Right = 8
    Margins.Bottom = 8
    Anchors = [akTop, akRight]
    MaxLength = 16
    TabOrder = 2
    Text = 'ShortNameEdit'
  end
  object PrjComboBox: TComboBoxPro
    Left = 505
    Top = 115
    Width = 114
    Height = 21
    TabOrder = 4
    Text = 'PrjComboBox'
  end
  object ADOQuery1: TADOQuery
    Connection = LVSDatenModul.MainADOConnection
    Parameters = <>
    Left = 248
    Top = 8
  end
  object ActionList1: TActionList
    Left = 488
    Top = 144
  end
end
