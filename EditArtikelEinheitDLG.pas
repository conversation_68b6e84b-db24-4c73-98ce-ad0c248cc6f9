﻿unit EditArtikelEinheitDLG;

interface

uses
  Windows, Messages, SysUtils, Variants, Classes, Graphics, Controls, Forms,
  Dialogs, DB, ADODB, StdCtrls, ComboBoxPro, ExtCtrls, ComCtrls, VCLUtilitys, BarCodeScanner,
  Grids, StringGridPro, IntegerUpDown, Menus;

type
  TComboArtikelEinheit = class (TComboBoxRef)
    NettoGewicht  : Integer;
    BruttoGewicht : Integer;

    constructor Create (const RecRef, RecNettoGewicht, RecBruttoGewicht : Integer);
  end;

  TEditArtikelEinheitenForm = class(TForm)
    OkButton: TButton;
    AbortButton: TButton;
    ADOQuery1: TADOQuery;
    EinheitPanel: TPanel;
    Label1: TLabel;
    EinheitComboBox: TComboBoxPro;
    VEPButton: TButton;
    Bevel8: TBevel;
    OptionPanel: TPanel;
    MasterCheckBox: TCheckBox;
    StueckCheckBox: TCheckBox;
    GewichtCheckBox: TCheckBox;
    BeschaffungsCheckBox: TCheckBox;
    Bevel6: TBevel;
    BarcodePanel: TPanel;
    Label5: TLabel;
    EANEdit: TEdit;
    Label22: TLabel;
    BarcodeEdit: TEdit;
    Bevel3: TBevel;
    InhaltPanel: TPanel;
    Label2: TLabel;
    InhaltArComboBox: TComboBoxPro;
    Label3: TLabel;
    InhaltAnzEdit: TEdit;
    InhaltAnzUpDown: TIntegerUpDown;
    Bevel4: TBevel;
    GewichtPanel: TPanel;
    Label4: TLabel;
    NettoEdit: TEdit;
    Label13: TLabel;
    Label6: TLabel;
    BruttoEdit: TEdit;
    Label14: TLabel;
    Bevel2: TBevel;
    AbmessungPanel: TPanel;
    Label7: TLabel;
    Label8: TLabel;
    Label9: TLabel;
    Label15: TLabel;
    Bevel10: TBevel;
    Label34: TLabel;
    Label35: TLabel;
    LaengeEdit: TEdit;
    BreiteEdit: TEdit;
    HoeheEdit: TEdit;
    VolumenEdit: TEdit;
    PalettenFaktorPanel: TPanel;
    Label10: TLabel;
    LagenFaktorEdit: TEdit;
    Label11: TLabel;
    PalFaktorEdit: TEdit;
    PageControl1: TPageControl;
    ColliTabSheet: TTabSheet;
    Label24: TLabel;
    Label25: TLabel;
    Label33: TLabel;
    AnzColliEdit: TEdit;
    MuliColliPanel: TPanel;
    Bevel1: TBevel;
    ColliCheckBox: TCheckBox;
    ColliStringGrid: TStringGridPro;
    NameEdit: TEdit;
    SizeTabSheet: TTabSheet;
    Label28: TLabel;
    Label29: TLabel;
    Label30: TLabel;
    Label31: TLabel;
    SizeCodeEdit: TEdit;
    ColorCodeEdit: TEdit;
    SizeTextEdit: TEdit;
    ColorTextEdit: TEdit;
    KommTabSheet: TTabSheet;
    Label32: TLabel;
    KommArtComboBox: TComboBoxPro;
    PfandTabSheet: TTabSheet;
    Label23: TLabel;
    PfandTauschComboBox: TComboBoxPro;
    PfandOptCheckBox: TCheckBox;
    LTTabSheet: TTabSheet;
    Label17: TLabel;
    Label18: TLabel;
    Label21: TLabel;
    Label19: TLabel;
    HKLComboBox: TComboBoxPro;
    LTWEComboBox: TComboBoxPro;
    PalHeightEdit: TEdit;
    WertTabSheet: TTabSheet;
    Label20: TLabel;
    Label16: TLabel;
    Label12: TLabel;
    PreisEdit: TEdit;
    PreisEinheitComboBox: TComboBoxPro;
    OrderTabSheet: TTabSheet;
    Label26: TLabel;
    Label27: TLabel;
    MinArBesEdit: TEdit;
    MinOrderBesEdit: TEdit;
    InventarTabSheet: TTabSheet;
    LifeCheckBox: TCheckBox;
    VersandTabSheet: TTabSheet;
    ReadyShipCheckBox: TCheckBox;
    SperrgutCheckBox: TCheckBox;
    Label36: TLabel;
    UnitsComboBox: TComboBoxPro;
    Bevel5: TBevel;
    UnitPanel: TPanel;
    Label37: TLabel;
    UnitsNettoEdit: TEdit;
    Label38: TLabel;
    Bevel7: TBevel;
    UnitsNormEdit: TEdit;
    Label39: TLabel;
    Label40: TLabel;
    PagePanel: TPanel;
    Label41: TLabel;
    TaraEdit: TEdit;
    Label42: TLabel;
    BigItemCheckBox: TCheckBox;
    InhaltArNrEdit: TEdit;
    EANEditPopupMenu: TPopupMenu;
    CreateEANMenuItem: TMenuItem;
    N1: TMenuItem;
    Einfgen1: TMenuItem;
    Ausschneiden1: TMenuItem;
    Kopieren1: TMenuItem;
    Lschen1: TMenuItem;
    N2: TMenuItem;
    Allesauswhlen1: TMenuItem;
    Rckgngig1: TMenuItem;
    N3: TMenuItem;
    StapelEdit: TEdit;
    Label43: TLabel;
    Label44: TLabel;
    LayerHeightEdit: TEdit;
    Label45: TLabel;
    StapelFaktorEdit: TEdit;
    Label48: TLabel;
    Label49: TLabel;
    MaterialCodeEdit: TEdit;
    MaterialTextEdit: TEdit;
    IDPanel: TPanel;
    Bevel9: TBevel;
    IDEdit: TEdit;
    Label50: TLabel;
    VerpackArtEdit: TEdit;
    Label51: TLabel;
    Label52: TLabel;
    VerpackFormEdit: TEdit;
    ProdIDEdit: TEdit;
    Label54: TLabel;
    Label55: TLabel;
    VersandArtComboBox: TComboBoxPro;
    FillingEdit: TEdit;
    Label46: TLabel;
    Label47: TLabel;
    Label56: TLabel;
    VersandSpedComboBox: TComboBoxPro;
    CuttingCheckBox: TCheckBox;
    CuttingFullCheckBox: TCheckBox;
    FullPalCheckBox: TCheckBox;
    FullPalPickCheckBox: TCheckBox;
    FullPalOverCheckBox: TCheckBox;
    Bevel11: TBevel;
    Bevel12: TBevel;
    Bevel13: TBevel;
    LTWAComboBox: TComboBoxPro;
    Label57: TLabel;
    ContPanel: TPanel;
    Label53: TLabel;
    Label58: TLabel;
    Bevel14: TBevel;
    ContArComboBox: TComboBoxPro;
    ContDenominEdit: TEdit;
    ContDenominUpDown: TIntegerUpDown;
    ContArNrEdit: TEdit;
    Label59: TLabel;
    ContNumerEdit: TEdit;
    ContNumerUpDown: TIntegerUpDown;
    Bevel15: TBevel;
    PersistentCheckBox: TCheckBox;
    procedure IntEditKeyPress(Sender: TObject; var Key: Char);
    procedure FloatEditKeyPress(Sender: TObject; var Key: Char);
    procedure FormCloseQuery(Sender: TObject; var CanClose: Boolean);
    procedure GewichtEditExit(Sender: TObject);
    procedure EANEditExit(Sender: TObject);
    procedure OkButtonClick(Sender: TObject);
    procedure EANEditKeyPress(Sender: TObject; var Key: Char);
    procedure VEPButtonClick(Sender: TObject);
    procedure InhaltArComboBoxChange(Sender: TObject);
    procedure InhaltAnzEditChange(Sender: TObject);
    procedure FormCreate(Sender: TObject);
    procedure PreisEinheitComboBoxChange(Sender: TObject);
    procedure PfandTauschComboBoxChange(Sender: TObject);
    procedure InhaltArComboBoxDropDown(Sender: TObject);
    procedure FormShow(Sender: TObject);
    procedure PurchesEditChange(Sender: TObject);
    procedure SizeColorEditChange(Sender: TObject);
    procedure EANEditChange(Sender: TObject);
    procedure VolumenEditKeyPress(Sender: TObject; var Key: Char);
    procedure AbmessungEditChange(Sender: TObject);
    procedure UnitsNormEditExit(Sender: TObject);
    procedure UnitsComboBoxChange(Sender: TObject);
    procedure InhaltAnzEditExit(Sender: TObject);
    procedure InhaltArNrEditChange(Sender: TObject);
    procedure CreateEANMenuItemClick(Sender: TObject);
    procedure Ausschneiden1Click(Sender: TObject);
    procedure Kopieren1Click(Sender: TObject);
    procedure Einfgen1Click(Sender: TObject);
    procedure Lschen1Click(Sender: TObject);
    procedure EANEditPopupMenuPopup(Sender: TObject);
    procedure Rckgngig1Click(Sender: TObject);
    procedure Allesauswhlen1Click(Sender: TObject);
    procedure FormDestroy(Sender: TObject);
    procedure FillingEditExit(Sender: TObject);
    procedure CuttingCheckBoxClick(Sender: TObject);
    procedure FullPalPickCheckBoxClick(Sender: TObject);
    procedure ContArComboBoxChange(Sender: TObject);
  private
    fRef           : Integer;
    fRefArtikel    : Integer;
    fRefMand       : Integer;
    fRefSubMand    : Integer;

    fEinheitenLoaded : Boolean;

    fSizeColorChanged : Boolean;
    fPurchesChanged : Boolean;

    fNettoChanged  : Boolean;
    fBruttoChanged : Boolean;

    fOldEAN        : String;  //Wird zur Prüfung auf doppelte EANs benutzt

    function SaveArtikelEinheit : Integer;

    procedure ScannerErfassung (var Message: TMessage); message WM_USER + SCANNER_DETECT;
  public
    UpdKey,
    UpdKeyInfo : Integer;

    procedure Prepare (const Ref, RefMand, RefArtikel : Integer); overload;
    procedure Prepare (const Ref, RefMand, RefSubMand, RefArtikel : Integer); overload;
  end;

implementation

{$R *.dfm}

uses
  DatenModul, EAN128Utils, EditVPEDLG, LVSConst, LVSDatenInterface, LVSArtikelInterface,
  FrontendUtils, LVSSecurity, ConfigModul, SprachModul, Clipbrd, ResourceText, LVSGlobalDaten,
  ErrorTracking;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
constructor TComboArtikelEinheit.Create (const RecRef, RecNettoGewicht, RecBruttoGewicht : Integer);
begin
  inherited Create (RecRef);

  NettoGewicht := RecNettoGewicht;
  BruttoGewicht := RecBruttoGewicht;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TEditArtikelEinheitenForm.PfandTauschComboBoxChange(Sender: TObject);
begin
  PfandOptCheckBox.Enabled := (PfandTauschComboBox.ItemIndex > 0);
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TEditArtikelEinheitenForm.PreisEinheitComboBoxChange(Sender: TObject);
begin
  PreisEdit.Enabled := (PreisEinheitComboBox.ItemIndex > 0);
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TEditArtikelEinheitenForm.Prepare (const Ref, RefMand, RefArtikel : Integer);
begin
  Prepare (Ref, RefMand, -1, RefArtikel);
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TEditArtikelEinheitenForm.Prepare (const Ref, RefMand, RefSubMand, RefArtikel : Integer);
var
  eanstr,
  vpestr         : String;
  opt            : Char;
  dispidx        : Integer;
  query          : TADOQuery;
  multicolliflag : Boolean;
begin
  multicolliflag := False;

  fRef        := Ref;
  fRefArtikel := RefArtikel;
  fRefMand    := RefMand;
  fRefSubMand := RefSubMand;

  Screen.Cursor := crSQLWait;

  try
    LoadComboxDBItems (KommArtComboBox, 'LAGER', 'KOMM_ART');

    if (KommArtComboBox.Items.Count = 0) then begin
      KommArtComboBox.Enabled := False;
      KommTabSheet.TabVisible := False;
    end;

    if UnitPanel.Visible and UnitsComboBox.Visible then begin
      LoadComboxDBItems (UnitsComboBox, 'ARTIKEL_EINHEIT', 'UNIT_EINHEIT', False, True);

      UnitsComboBox.Items.Insert (0, GetResourceText (1509));
    end;

    LoadComboxDBItems (HKLComboBox, 'LAGER', 'HOEHEN_KLASSE');
    HKLComboBox.Items.Insert (0, '');

    LoadComboxDBItems (VersandArtComboBox, 'ARTIKEL_EINHEIT', 'VERSAND_ART', False);
    if (VersandArtComboBox.Items.Count = 0) then
      VersandArtComboBox.Enabled := False
    else VersandArtComboBox.Items.Insert (0, '');

    LoadComboxDBItems (VersandSpedComboBox, 'ARTIKEL_EINHEIT', 'VERSAND_SPED', False);
    if (VersandSpedComboBox.Items.Count = 0) then
      VersandSpedComboBox.Enabled := False
    else VersandSpedComboBox.Items.Insert (0, '');

    ADOQuery1.SQL.Clear;

    if (fRefSubMand > 0) then begin
      ADOQuery1.SQL.Add ('select * from V_MANDANT m where m.REF=:ref_sub_mand');
      ADOQuery1.Parameters.ParamByName('ref_sub_mand').Value := fRefSubMand;
    end else begin
      ADOQuery1.SQL.Add ('select * from V_MANDANT m where m.REF=:ref_mand');
      ADOQuery1.Parameters.ParamByName('ref_mand').Value := fRefMand;
    end;

    ADOQuery1.Open;

    if not Assigned (ADOQuery1.FindField ('CURRENCY')) then
    else if ADOQuery1.FieldByName('CURRENCY').IsNull then
      Label20.Caption := 'EUR'
    else
      Label20.Caption := ADOQuery1.FieldByName('CURRENCY').AsString;

    ADOQuery1.Close;

    ClearComboBoxObjects (LTWEComboBox);
    ClearComboBoxObjects (LTWAComboBox);

    ADOQuery1.SQL.Clear;
    ADOQuery1.SQL.Add ('select REF, NAME from V_LT_EINSATZ where EINSATZ=''LE'' and REF_LAGER is null and REF_LOCATION is null and (REF_MAND is null or REF_MAND=:ref_mand)');
    ADOQuery1.Parameters.ParamByName('ref_mand').Value := fRefMand;

    if (LVSConfigModul.RefProject <> -1) then begin
      ADOQuery1.SQL.Add ('and REF_PROJECT=:ref_prj');
      ADOQuery1.Parameters.ParamByName('ref_prj').Value := LVSConfigModul.RefProject;
    end;

    ADOQuery1.SQL.Add ('order by DEFAULT_LT desc, REIHENFOLGE asc');
    try
      ADOQuery1.Open;

      while not (ADOQuery1.EOF) do begin
        LTWEComboBox.Items.AddObject (ADOQuery1.Fields [1].AsString, TComboBoxRef.Create (ADOQuery1.Fields [0].AsInteger));

        ADOQuery1.Next;
      end;

      ADOQuery1.Close;
    except
    end;

    LTWEComboBox.Items.Insert (0, '');


    if not UseDefLTWEWA then
      LTWAComboBox.Visible := false
    else begin
      ADOQuery1.SQL.Clear;
      ADOQuery1.SQL.Add ('select REF, NAME from V_LT_EINSATZ where EINSATZ=''WA'' and REF_LAGER is null and REF_LOCATION is null and (REF_MAND is null or REF_MAND=:ref_mand)');
      ADOQuery1.Parameters.ParamByName('ref_mand').Value := fRefMand;

      if (LVSConfigModul.RefProject <> -1) then begin
        ADOQuery1.SQL.Add ('and REF_PROJECT=:ref_prj');
        ADOQuery1.Parameters.ParamByName('ref_prj').Value := LVSConfigModul.RefProject;
      end;

      ADOQuery1.SQL.Add ('order by DEFAULT_LT desc, REIHENFOLGE asc');
      try
        ADOQuery1.Open;

        while not (ADOQuery1.EOF) do begin
          LTWAComboBox.Items.AddObject (ADOQuery1.Fields [1].AsString, TComboBoxRef.Create (ADOQuery1.Fields [0].AsInteger));

          ADOQuery1.Next;
        end;

        ADOQuery1.Close;
      except
      end;

      LTWAComboBox.Items.Insert (0, '');
    end;


    if (LVSConfigModul.UsePfandgut) then begin
      ClearComboBoxObjects (PfandTauschComboBox);

      ADOQuery1.SQL.Clear;
      ADOQuery1.SQL.Add ('select REF,NAME,ARTIKEL_NR from V_LT_TYPEN where STATUS=''AKT'' and nvl (PFAND_LT, ''0'') = ''1'' and REF_LAGER is null and REF_LOCATION is null and REF_MAND=(select REF_MAND from V_ARTIKEL where REF=:ref_ar) order by REIHENFOLGE asc');
      ADOQuery1.Parameters [0].Value := fRefArtikel;

      try
        ADOQuery1.Open;

        while not (ADOQuery1.EOF) do begin
          PfandTauschComboBox.Items.AddObject (ADOQuery1.Fields [2].AsString+'|'+ADOQuery1.Fields [1].AsString, TComboBoxRef.Create (ADOQuery1.Fields [0].AsInteger));

          ADOQuery1.Next;
        end;

        ADOQuery1.Close;
      except
      end;
      PfandTauschComboBox.Items.Insert (0, '');
    end;

    ClearComboBoxObjects (EinheitComboBox);

    ADOQuery1.SQL.Clear;
    ADOQuery1.SQL.Add ('select vpe.REF, vpe.KURZ_BEZEICHNUNG, vpe.BEZEICHNUNG from V_ARTIKEL_VPE vpe, V_ARTIKEL a where vpe.REF_MAND=a.REF_MAND and a.REF=:ref_ar order by vpe.KURZ_BEZEICHNUNG');
    ADOQuery1.Parameters [0].Value := fRefArtikel;

    Screen.Cursor := crSQLWait;

    try
      ADOQuery1.Open;

      while not (ADOQuery1.Eof) do begin
        EinheitComboBox.Items.AddObject(ADOQuery1.Fields [1].AsString+'|'+ADOQuery1.Fields [2].AsString, TComboBoxRef.Create(ADOQuery1.Fields [0].AsInteger));

        ADOQuery1.Next;
      end;
      ADOQuery1.Close;
    finally
      Screen.Cursor := crDefault;
    end;

    ClearComboBoxObjects (InhaltArComboBox);
    InhaltArComboBox.Items.Add ('');

    ClearComboBoxObjects (ContArComboBox);
    ContArComboBox.Items.Add ('');

    ColliCheckBox.Checked := False;
    ClearGridObjects (ColliStringGrid);

    if (Ref = -1) then begin
      ADOQuery1.SQL.Clear;
      ADOQuery1.SQL.Add ('select'
                        +' ae.*, aei.*, ar.REF_MAND, ar.REF_SUB_MAND, ar.REF_ARTIKEL_SET, m.CONFIG_OPT as MAND_CONFIG_OPT'
                        +' from'
                        +' v_ARTIKEL ar'
                        +' inner join V_MANDANT m on (m.REF=nvl (ar.REF_SUB_MAND, ar.REF_MAND))'
                        +' left outer join V_ARTIKEL_EINHEIT ae on (ae.REF_AR=ar.REF)'
                        +' left outer join V_ARTIKEL_EINHEIT_INFO aei on (aei.REF_AR_EINHEIT=ae.REF)'
                        +' where'
                        +' ar.REF=:ref');
      ADOQuery1.Parameters [0].Value := fRefArtikel;

      ADOQuery1.Open;

      fRefMand    := ADOQuery1.FieldByName ('REF_MAND').AsInteger;
      fRefSubMand := DBGetReferenz (ADOQuery1.FieldByName ('REF_SUB_MAND'));

      //Setartikel können keine Collis haben und die Anzahl-Packstücke wird automatisch bestimmt
      if not (ADOQuery1.FieldByName('REF_ARTIKEL_SET').IsNull) then begin
        AnzColliEdit.Enabled := False;

        MuliColliPanel.Visible := False;
      end;

      StapelEdit.Visible       := Assigned (ADOQuery1.FindField ('STAPEL_REIHENFOLGE'));
      StapelFaktorEdit.Visible := Assigned (ADOQuery1.FindField ('STAPEL_FAKTOR'));
      FillingEdit.Visible      := Assigned (ADOQuery1.FindField ('FILLING_QUANTITY'));
      PalHeightEdit.Visible    := Assigned (ADOQuery1.FindField ('PAL_HEIGHT'));
      LayerHeightEdit.Visible  := Assigned (ADOQuery1.FindField ('LAYER_HEIGHT'));
      IDPanel.Visible          := Assigned (ADOQuery1.FindField ('VARIANT_ID'));
      MaterialCodeEdit.Visible := Assigned (ADOQuery1.FindField ('MATERIAL_CODE'));
      MaterialTextEdit.Visible := Assigned (ADOQuery1.FindField ('MATERIAL_TEXT'));
      VerpackArtEdit.Visible   := Assigned (ADOQuery1.FindField ('VERPACKUNG_ART'));
      VerpackFormEdit.Visible  := Assigned (ADOQuery1.FindField ('VERPACKUNG_FORM'));
      ProdIDEdit.Visible       := Assigned (ADOQuery1.FindField ('PRODUKT_ID'));
      CuttingCheckBox.Visible  := Assigned (ADOQuery1.FindField ('OPT_CUTTING'));
      FullPalCheckBox.Visible  := Assigned (ADOQuery1.FindField ('OPT_FULL_PAL_EINHEIT'));
      FullPalPickCheckBox.Visible := Assigned (ADOQuery1.FindField ('OPT_FULL_PAL_PICK'));
      PersistentCheckBox.Visible  := Assigned (ADOQuery1.FindField ('OPT_PERSISTENT'));

      VersandArtComboBox.Visible  := Assigned (ADOQuery1.FindField ('VERSAND_ART'));
      VersandSpedComboBox.Visible := Assigned (ADOQuery1.FindField ('VERSAND_SPED'));

      opt := GetOpt (ADOQuery1.FieldByName ('MAND_CONFIG_OPT').AsString, cMandArContFactor);

      if (not Assigned (ADOQuery1.FindField ('CONTEND_NUMER')) or ((opt = ' ') or (opt = '0'))) then begin
        InhaltPanel.Visible := True;
        ContPanel.Visible := false;
      end else begin
        InhaltPanel.Visible := false;
        ContPanel.Visible := True;
      end;

      ADOQuery1.Close;

      EinheitComboBox.ItemIndex := -1;
      EinheitComboBox.Text := '';

      IDEdit.Text := '';
      EANEdit.Text := '';
      InhaltArNrEdit.Text := '';
      BarcodeEdit.Text := '';
      NettoEdit.Text := '';
      BruttoEdit.Text := '';
      TaraEdit.Text := '';
      AnzColliEdit.Text := '';
      LaengeEdit.Text := '';
      BreiteEdit.Text := '';
      HoeheEdit.Text := '';
      LagenFaktorEdit.Text := '';
      PalFaktorEdit.Text := '';
      VolumenEdit.Text := '';
      StapelEdit.Text := '';
      StapelFaktorEdit.Text := '';
      FillingEdit.Text := '';
      ProdIDEdit.Text := '';

      PreisEdit.Enabled := False;
      PreisEdit.Text := '';
      PreisEinheitComboBox.ItemIndex := 0;

      InhaltArComboBox.ItemIndex := 0;
      ContArComboBox.ItemIndex := 0;

      InhaltAnzUpDown.Position := 0;
      InhaltAnzUpDown.Enabled := False;
      InhaltAnzEdit.Enabled := False;
      InhaltAnzEdit.Text    := '';

      fNettoChanged  := False;
      fBruttoChanged := False;

      LTWEComboBox.ItemIndex  := -1;
      LTWAComboBox.ItemIndex  := -1;
      HKLComboBox.ItemIndex := -1;
      PalHeightEdit.Text := '';
      LayerHeightEdit.Text := '';

      NameEdit.Text := '';
      AnzColliEdit.Text := '';

      VerpackArtEdit.Text  := '';
      VerpackFormEdit.Text := '';

      PfandTauschComboBox.ItemIndex := 0;
      PfandOptCheckBox.Checked := False;

      SizeCodeEdit.Text := '';
      SizeTextEdit.Text := '';
      ColorCodeEdit.Text := '';
      ColorTextEdit.Text := '';
      MaterialCodeEdit.Text := '';
      MaterialTextEdit.Text := '';

      MinArBesEdit.Text := '';
      MinOrderBesEdit.Text := '';

      UnitsComboBox.ItemIndex := 0;
      UnitsNormEdit.Text := '';
      UnitsNettoEdit.Text := '';

      UnitsComboBoxChange (nil);
    end else begin
      VerpackArtEdit.Text  := '';
      VerpackFormEdit.Text := '';

      ADOQuery1.SQL.Clear;
      ADOQuery1.SQL.Add ('select ae.*, aei.*, ar.REF_MAND, ar.REF_SUB_MAND, m.CONFIG_OPT as MAND_CONFIG_OPT'
                        +',aein.REF_AE_EINHEIT as INHALT_REF_AE_EINHEIT, aein.ARTIKEL_NR as INHALT_ARTIKEL_NR, aein.EINHEIT as INHALT_EINHEIT, aein.ARTIKEL_TEXT as INHALT_ARTIKEL_TEXT, aein.NETTO_GEWICHT as INHALT_NETTO_GEWICHT'
                        +',aein.BRUTTO_GEWICHT as INHALT_BRUTTO_GEWICHT,aein.INHALT_ANZAHL as INHALT_INHALT_ANZAHL,ar.REF_ARTIKEL_SET'
                        +' from V_ARTIKEL_EINHEIT ae'
                        +'   inner join V_ARTIKEL ar on (ar.REF=ae.REF_AR)'
                        +'   inner join V_MANDANT m on (m.REF=nvl (ar.REF_SUB_MAND, ar.REF_MAND))'
                        +'   inner join V_ARTIKEL_EINHEIT_INFO aei on (aei.REF_AR_EINHEIT=ae.REF)'
                        +'   left outer join V_ARTIKEL_LISTE aein on (aein.REF_AE_EINHEIT=ae.REF_INHALT)'
                        +' where ae.REF=:ref'
                        );
      ADOQuery1.Parameters [0].Value := Ref;

      try
        ADOQuery1.Open;

        opt := GetOpt (ADOQuery1.FieldByName ('MAND_CONFIG_OPT').AsString, cMandArContFactor);

        if (not Assigned (ADOQuery1.FindField ('CONTEND_NUMER')) or ((opt = ' ') or (opt = '0'))) then begin
          InhaltPanel.Visible := True;
          ContPanel.Visible := false;
        end else begin
          InhaltPanel.Visible := false;
          ContPanel.Visible := True;
        end;

        fRefMand    := ADOQuery1.FieldByName ('REF_MAND').AsInteger;
        fRefSubMand := DBGetReferenz (ADOQuery1.FieldByName ('REF_SUB_MAND'));

        AnzColliEdit.Enabled := ADOQuery1.FieldByName('REF_ARTIKEL_SET').IsNull;

        if (LVSConfigModul.PrjArtikelCollis) then begin
          if ADOQuery1.FieldByName('REF_ARTIKEL_SET').IsNull then
            multicolliflag := (ADOQuery1.FieldByName ('OPT_MULTI_COLLI').AsString = '1')
          else begin
            multicolliflag := False;
            MuliColliPanel.Visible := False;
          end;
        end;

        if (ADOQuery1.FieldByName ('UPDATE_COUNT').IsNull) then
          UpdKey := -1
        else UpdKey := ADOQuery1.FieldByName ('UPDATE_COUNT').AsInteger;

        if (ADOQuery1.FieldByName ('INFO_UPDATE_COUNT').IsNull) then
          UpdKeyInfo := -1
        else UpdKeyInfo := ADOQuery1.FieldByName ('INFO_UPDATE_COUNT').AsInteger;

        if not (Assigned (ADOQuery1.FindField ('VARIANT_ID'))) then
          IDPanel.Visible := False
        else
          IDEdit.Text := ADOQuery1.FieldByName ('VARIANT_ID').AsString;

        if not (Assigned (ADOQuery1.FindField ('PRODUKT_ID'))) then
          ProdIDEdit.Visible := False
        else
          ProdIDEdit.Text := ADOQuery1.FieldByName ('PRODUKT_ID').AsString;

        if InhaltPanel.Visible then begin
          if InhaltArComboBox.Enabled then begin
            if (ADOQuery1.FieldByName ('REF_INHALT').IsNull) then begin
              InhaltArNrEdit.Text := '';
              InhaltArComboBox.ItemIndex := 0;

              InhaltAnzUpDown.Position := 0;
              InhaltAnzUpDown.Enabled := False;
              InhaltAnzEdit.Enabled := False;
              InhaltAnzEdit.Text    := '';
            end else begin
              vpestr := ADOQuery1.FieldByName ('INHALT_EINHEIT').AsString;
              if (ADOQuery1.FieldByName ('INHALT_INHALT_ANZAHL').IsNull) or (ADOQuery1.FieldByName ('INHALT_INHALT_ANZAHL').AsInteger = 0) then
                vpestr := vpestr + ' ('+IntToStr (ADOQuery1.FieldByName ('INHALT_INHALT_ANZAHL').AsInteger)+')';

              InhaltArNrEdit.Text := ADOQuery1.FieldByName ('INHALT_ARTIKEL_NR').AsString;

              with ADOQuery1 do
                InhaltArComboBox.ItemIndex := InhaltArComboBox.Items.AddObject(FieldByName ('INHALT_ARTIKEL_NR').AsString+'|'+vpestr+'|'+FieldByName ('INHALT_ARTIKEL_TEXT').AsString, TComboArtikelEinheit.Create(FieldByName ('INHALT_REF_AE_EINHEIT').AsInteger, FieldByName ('INHALT_NETTO_GEWICHT').AsInteger, FieldByName ('INHALT_BRUTTO_GEWICHT').AsInteger));

              InhaltAnzUpDown.Enabled := True;
              InhaltAnzEdit.Enabled := True;

              InhaltAnzUpDown.Position := ADOQuery1.FieldByName ('INHALT_ANZAHL').AsInteger;
            end;
          end;
        end else if ContPanel.Visible then begin
          if ContArComboBox.Enabled then begin
            if (ADOQuery1.FieldByName ('REF_INHALT').IsNull) then begin
              ContArNrEdit.Text := '';
              ContArComboBox.ItemIndex := 0;

              ContNumerUpDown.Position := 0;
              ContNumerUpDown.Enabled := False;
              ContDenominUpDown.Position := 0;
              ContDenominUpDown.Enabled := False;
              ContNumerEdit.Enabled := False;
              ContNumerEdit.Text    := '';
              ContDenominEdit.Enabled := False;
              ContDenominEdit.Text    := '';
            end else begin
              vpestr := ADOQuery1.FieldByName ('INHALT_EINHEIT').AsString;
              if (ADOQuery1.FieldByName ('INHALT_INHALT_ANZAHL').IsNull) or (ADOQuery1.FieldByName ('INHALT_INHALT_ANZAHL').AsInteger = 0) then
                vpestr := vpestr + ' ('+IntToStr (ADOQuery1.FieldByName ('INHALT_INHALT_ANZAHL').AsInteger)+')';

              ContArNrEdit.Text := ADOQuery1.FieldByName ('INHALT_ARTIKEL_NR').AsString;

              with ADOQuery1 do
                ContArComboBox.ItemIndex := ContArComboBox.Items.AddObject(FieldByName ('INHALT_ARTIKEL_NR').AsString+'|'+vpestr+'|'+FieldByName ('INHALT_ARTIKEL_TEXT').AsString, TComboArtikelEinheit.Create(FieldByName ('INHALT_REF_AE_EINHEIT').AsInteger, FieldByName ('INHALT_NETTO_GEWICHT').AsInteger, FieldByName ('INHALT_BRUTTO_GEWICHT').AsInteger));

              ContNumerUpDown.Enabled := True;
              ContNumerEdit.Enabled := True;
              ContNumerUpDown.Position := ADOQuery1.FieldByName ('CONTEND_NUMER').AsInteger;

              ContDenominUpDown.Enabled := True;
              ContDenominEdit.Enabled := True;
              ContDenominUpDown.Position := ADOQuery1.FieldByName ('CONTEND_DENOMIN').AsInteger;
            end;
          end;
        end;

        EinheitComboBox.ItemIndex := FindComboboxRef (EinheitComboBox, ADOQuery1.FieldByName ('REF_EINHEIT').AsInteger);

        fOldEAN := ADOQuery1.FieldByName ('EAN').AsString;

        EANEdit.Text     := ADOQuery1.FieldByName ('EAN').AsString;
        BarcodeEdit.Text := ADOQuery1.FieldByName ('BARCODE').AsString;

        if UnitPanel.Visible then begin
          if Assigned (ADOQuery1.FindField ('UNIT_EINHEIT')) then begin
            if (ADOQuery1.FindField ('UNIT_EINHEIT').IsNull) then
              UnitsComboBox.ItemIndex := 0
            else begin
              UnitsComboBox.ItemIndex := FindComboboxDBItem (UnitsComboBox, ADOQuery1.FindField ('UNIT_EINHEIT').AsString, -1);
              if (UnitsComboBox.ItemIndex = -1) then UnitsComboBox.ItemIndex := 0;
            end;

            if (ADOQuery1.FindField ('UNIT_EINHEIT_NORM').IsNull) then
              UnitsNormEdit.Text := ''
            else UnitsNormEdit.Text := Format ('%0.3f', [ADOQuery1.FieldByName ('UNIT_EINHEIT_NORM').AsInteger / 1000]);

            if (ADOQuery1.FindField ('UNIT_EINHEIT_NETTO').IsNull) then
              UnitsNettoEdit.Text := ''
            else UnitsNettoEdit.Text := Format ('%0.3f', [ADOQuery1.FieldByName ('UNIT_EINHEIT_NETTO').AsInteger / 1000]);
          end else begin
            UnitsComboBox.ItemIndex := 0;
            UnitsNormEdit.Text := '';
            UnitsNettoEdit.Text := '';
          end;
        end;

        UnitsComboBoxChange (nil);

        if (ADOQuery1.FieldByName ('NETTO_GEWICHT').IsNull) then begin
          fNettoChanged  := False;
          NettoEdit.Text := '';
        end else begin
          fNettoChanged  := True;
          NettoEdit.Text := Format ('%0.3f', [ADOQuery1.FieldByName ('NETTO_GEWICHT').AsInteger / 1000]);
        end;

        if (ADOQuery1.FieldByName ('BRUTTO_GEWICHT').IsNull) then begin
          fBruttoChanged := False;
          BruttoEdit.Text := ''
        end else begin
          fBruttoChanged := True;
          BruttoEdit.Text := Format ('%0.3f', [ADOQuery1.FieldByName ('BRUTTO_GEWICHT').AsInteger / 1000]);
        end;

        if (ADOQuery1.FieldByName ('TARA_GEWICHT').IsNull or (ADOQuery1.FieldByName ('TARA_GEWICHT').AsInteger <= 0)) then begin
          TaraEdit.Text := ''
        end else begin
          TaraEdit.Text := Format ('%0.3f', [ADOQuery1.FieldByName ('TARA_GEWICHT').AsInteger / 1000]);
        end;

        if not Assigned (ADOQuery1.FindField ('COLLI_NAME')) then
          NameEdit.Visible := False
        else NameEdit.Text := ADOQuery1.FieldByName ('COLLI_NAME').AsString;

        if (ADOQuery1.FieldByName ('ANZAHL_PACKEINHEIT').IsNull) then begin
          AnzColliEdit.Text := ''
        end else begin
          AnzColliEdit.Text := IntToStr (ADOQuery1.FieldByName ('ANZAHL_PACKEINHEIT').AsInteger);
        end;

        LaengeEdit.Text := ADOQuery1.FieldByName ('L').AsString;
        BreiteEdit.Text := ADOQuery1.FieldByName ('B').AsString;
        HoeheEdit.Text  := ADOQuery1.FieldByName ('H').AsString;

        if (ADOQuery1.FieldByName ('VOLUMEN').IsNull) then
          VolumenEdit.Text := ''
        else VolumenEdit.Text := Format ('%.4f', [ADOQuery1.FieldByName ('VOLUMEN').AsFloat / 1000000000]);

        LagenFaktorEdit.Text := ADOQuery1.FieldByName ('LAGEN_FAKTOR').AsString;
        PalFaktorEdit.Text   := ADOQuery1.FieldByName ('PAL_FAKTOR').AsString;

        if (ADOQuery1.FieldByName ('PREIS_EINHEIT').IsNull) or (ADOQuery1.FieldByName ('PREIS_EINHEIT').AsInteger > (PreisEinheitComboBox.Items.Count - 1)) or (ADOQuery1.FieldByName ('PREIS_EINHEIT').AsInteger < 0) then begin
          PreisEdit.Enabled := False;
          PreisEdit.Text := '';

          PreisEinheitComboBox.ItemIndex := 0;
        end else begin
          PreisEinheitComboBox.ItemIndex := ADOQuery1.FieldByName ('PREIS_EINHEIT').AsInteger;

          PreisEdit.Enabled := True;

          if (ADOQuery1.FieldByName ('PREIS').IsNull) then
            PreisEdit.Text := ''
          else
            PreisEdit.Text := Format ('%0.3f', [ADOQuery1.FieldByName ('PREIS').AsInteger / 1000]);
        end;

        if (ADOQuery1.FieldByName ('OPT_GEWICHT').AsString = '1') then
          GewichtCheckBox.Checked := True
        else GewichtCheckBox.Checked := False;

        if (ADOQuery1.FieldByName ('OPT_MASTER').AsString = '1') then
          MasterCheckBox.Checked := True
        else MasterCheckBox.Checked := False;

        if (ADOQuery1.FieldByName ('STUECK_KENNZEICHEN').AsString = '1') then
          StueckCheckBox.Checked := True
        else StueckCheckBox.Checked := False;

        if (ADOQuery1.FieldByName ('OPT_BESCHAFFUNG').AsString = '1') then
          BeschaffungsCheckBox.Checked := True
        else BeschaffungsCheckBox.Checked := False;

        if not (Assigned (ADOQuery1.FindField ('OPT_LIFE_CYCLE'))) then begin
          LifeCheckBox.Enabled := False;
          InventarTabSheet.TabVisible := False;
        end else if (ADOQuery1.FieldByName ('OPT_LIFE_CYCLE').AsString = '1') then
          LifeCheckBox.Checked := True
        else LifeCheckBox.Checked := False;

        if not (Assigned (ADOQuery1.FindField ('OPT_READY_FOR_SHIP')) and Assigned (ADOQuery1.FindField ('OPT_SPERRGUT'))) then begin
          ReadyShipCheckBox.Enabled := False;
          VersandTabSheet.TabVisible := False;
        end else begin
          if not (Assigned (ADOQuery1.FindField ('OPT_READY_FOR_SHIP'))) then
            ReadyShipCheckBox.Visible := False
          else if (ADOQuery1.FieldByName ('OPT_READY_FOR_SHIP').AsString = '1') then
            ReadyShipCheckBox.Checked := True
          else ReadyShipCheckBox.Checked := False;

          if not (Assigned (ADOQuery1.FindField ('OPT_SPERRGUT'))) then
            SperrgutCheckBox.Visible := False
          else if (ADOQuery1.FieldByName ('OPT_SPERRGUT').AsString = '1') then
            SperrgutCheckBox.Checked := True
          else SperrgutCheckBox.Checked := False;
        end;

        if not (Assigned (ADOQuery1.FindField ('OPT_BIG_ITEM'))) then
          BigItemCheckBox.Visible := False
        else if (ADOQuery1.FieldByName ('OPT_BIG_ITEM').AsString = '1') then
          BigItemCheckBox.Checked := True
        else BigItemCheckBox.Checked := False;

        if not (Assigned (ADOQuery1.FindField ('OPT_CUTTING'))) then
          CuttingCheckBox.Visible := False
        else if (ADOQuery1.FieldByName ('OPT_CUTTING').AsString > '0') then begin
          CuttingCheckBox.Checked := True;
          CuttingFullCheckBox.Checked := (ADOQuery1.FieldByName ('OPT_CUTTING').AsString = '2');
        end else begin
          CuttingCheckBox.Checked := False;
          CuttingFullCheckBox.Checked := False;
        end;

        if not (Assigned (ADOQuery1.FindField ('OPT_FULL_PAL_EINHEIT'))) then
          FullPalCheckBox.Visible := False
        else if (ADOQuery1.FieldByName ('OPT_FULL_PAL_EINHEIT').AsString > '0') then begin
          FullPalCheckBox.Checked := True;
        end;

        if not (Assigned (ADOQuery1.FindField ('OPT_FULL_PAL_PICK'))) then
          FullPalPickCheckBox.Visible := False
        else if (ADOQuery1.FieldByName ('OPT_FULL_PAL_PICK').AsString > '0') then begin
          FullPalPickCheckBox.Checked := True;
          FullPalOverCheckBox.Checked := (ADOQuery1.FieldByName ('OPT_FULL_PAL_PICK').AsString = '2');
        end else begin
          FullPalPickCheckBox.Checked := False;
          FullPalOverCheckBox.Checked := False;
        end;

        if not (Assigned (ADOQuery1.FindField ('OPT_PERSISTENT'))) then
          PersistentCheckBox.Visible := False
        else if (ADOQuery1.FieldByName ('OPT_PERSISTENT').AsString > '0') then begin
          PersistentCheckBox.Checked := True;
        end else begin
          PersistentCheckBox.Checked := False;
        end;

        if not (Assigned (ADOQuery1.FindField ('VERPACKUNG_ART'))) then
          VerpackArtEdit.Visible := false
        else VerpackArtEdit.Text := ADOQuery1.FieldByName ('VERPACKUNG_ART').AsString;

        if not (Assigned (ADOQuery1.FindField ('VERPACKUNG_FORM'))) then
          VerpackFormEdit.Visible := false
        else VerpackFormEdit.Text := ADOQuery1.FieldByName ('VERPACKUNG_FORM').AsString;

        if not (Assigned (ADOQuery1.FindField ('VERSAND_ART'))) then
          VersandArtComboBox.Visible := false
        else begin
          VersandArtComboBox.ItemIndex := FindComboboxDBItem (VersandArtComboBox, ADOQuery1.FieldByName ('VERSAND_ART').AsString);
          if (VersandArtComboBox.ItemIndex = -1) then VersandArtComboBox.ItemIndex := 0;
        end;

        if VersandSpedComboBox.Visible then begin
          if not (Assigned (ADOQuery1.FindField ('VERSAND_SPED'))) then
            VersandSpedComboBox.Visible := false
          else if ADOQuery1.FindField ('VERSAND_SPED').IsNull then
            VersandSpedComboBox.ItemIndex := 0
          else begin
            VersandSpedComboBox.ItemIndex := FindComboboxDBItem (VersandSpedComboBox, ADOQuery1.FieldByName ('VERSAND_SPED').AsString);

            if (VersandSpedComboBox.ItemIndex = -1) then begin
              VersandSpedComboBox.Enabled := True;

              VersandSpedComboBox.ItemIndex := VersandSpedComboBox.Items.AddObject (ADOQuery1.FieldByName('VERSAND_SPED').AsString, TDBItemsDaten.Create ('VERSAND_SPED', ADOQuery1.FieldByName('VERSAND_SPED').AsString, ADOQuery1.FieldByName('VERSAND_SPED').AsString, '', ''));
            end;
          end;
        end;

        PfandTauschComboBox.ItemIndex := FindComboboxRef (PfandTauschComboBox, ADOQuery1.FieldByName ('REF_LT_TYPE').AsInteger);
        if (PfandTauschComboBox.ItemIndex = -1) then PfandTauschComboBox.ItemIndex := 0;

        PfandOptCheckBox.Enabled := (PfandTauschComboBox.ItemIndex > 0);

        if (ADOQuery1.FieldByName ('PFAND_LT_AUTOMATE').AsString = '1') then
          PfandOptCheckBox.Checked := True
        else PfandOptCheckBox.Checked := False;

        if (SizeTabSheet.TabVisible) then begin
          SizeCodeEdit.Text  := ADOQuery1.FieldByName ('SIZE_CODE').AsString;
          SizeTextEdit.Text  := ADOQuery1.FieldByName ('SIZE_TEXT').AsString;
          ColorCodeEdit.Text := ADOQuery1.FieldByName ('COLOR_CODE').AsString;
          ColorTextEdit.Text := ADOQuery1.FieldByName ('COLOR_TEXT').AsString;

          if not (Assigned (ADOQuery1.FindField ('MATERIAL_CODE'))) then
            MaterialCodeEdit.Visible := False
          else
            MaterialCodeEdit.Text := ADOQuery1.FieldByName ('MATERIAL_CODE').AsString;

          if not (Assigned (ADOQuery1.FindField ('MATERIAL_TEXT'))) then
            MaterialTextEdit.Visible := False
          else
            MaterialTextEdit.Text := ADOQuery1.FieldByName ('MATERIAL_TEXT').AsString;
        end;

        if (KommArtComboBox.Enabled) then begin
          KommArtComboBox.ItemIndex := KommArtComboBox.IndexOf (ADOQuery1.FieldByName ('KOMM_ART').AsString);
          if (KommArtComboBox.ItemIndex = -1) then KommArtComboBox.ItemIndex := 0;
        end;

        if not Assigned (ADOQuery1.FindField ('STAPEL_FAKTOR')) then
          StapelFaktorEdit.Visible := False
        else if ADOQuery1.FieldByName ('STAPEL_FAKTOR').IsNull then
          StapelFaktorEdit.Text := ''
        else
          StapelFaktorEdit.Text := ADOQuery1.FieldByName ('STAPEL_FAKTOR').AsString;

        if not Assigned (ADOQuery1.FindField ('STAPEL_REIHENFOLGE')) then
          StapelEdit.Visible := False
        else if ADOQuery1.FieldByName ('STAPEL_REIHENFOLGE').IsNull then
          StapelEdit.Text := ''
        else
          StapelEdit.Text := ADOQuery1.FieldByName ('STAPEL_REIHENFOLGE').AsString;

        if Assigned (ADOQuery1.FindField ('REF_DEFAULT_LT_WE')) then begin
          LTWEComboBox.ItemIndex  := FindComboboxRef (LTWEComboBox, ADOQuery1.FieldByName ('REF_DEFAULT_LT_WE').AsInteger);
          if (LTWEComboBox.ItemIndex = -1) then LTWEComboBox.ItemIndex := 0;
        end else begin
          LTWEComboBox.ItemIndex  := FindComboboxRef (LTWEComboBox, ADOQuery1.FieldByName ('REF_DEFAULT_LT').AsInteger);
          if (LTWEComboBox.ItemIndex = -1) then LTWEComboBox.ItemIndex := 0;
        end;

        if not Assigned (ADOQuery1.FindField ('REF_DEFAULT_LT_WA')) then
          LTWAComboBox.Visible := false
        else begin
          LTWAComboBox.ItemIndex  := FindComboboxRef (LTWAComboBox, ADOQuery1.FieldByName ('REF_DEFAULT_LT_WA').AsInteger);
          if (LTWAComboBox.ItemIndex = -1) then LTWAComboBox.ItemIndex := 0;
        end;

        HKLComboBox.ItemIndex := HKLComboBox.IndexOf (ADOQuery1.FieldByName ('DEFAULT_HKL').AsString);
        if (HKLComboBox.ItemIndex = -1) then HKLComboBox.ItemIndex := 0;

        if not Assigned (ADOQuery1.FindField ('PAL_HEIGHT')) then
          PalHeightEdit.Visible := False
        else if (ADOQuery1.FieldByName ('PAL_HEIGHT').IsNull) then
          PalHeightEdit.Text := ''
        else
          PalHeightEdit.Text := ADOQuery1.FieldByName ('PAL_HEIGHT').AsString;

        if not Assigned (ADOQuery1.FindField ('LAYER_HEIGHT')) then
          LayerHeightEdit.Visible := False
        else if (ADOQuery1.FieldByName ('LAYER_HEIGHT').IsNull) then
          LayerHeightEdit.Text := ''
        else
          LayerHeightEdit.Text := ADOQuery1.FieldByName ('LAYER_HEIGHT').AsString;

        if not Assigned (ADOQuery1.FindField ('FILLING_QUANTITY')) then
          FillingEdit.Visible := False
        else if (ADOQuery1.FieldByName ('FILLING_QUANTITY').IsNull) then
          FillingEdit.Text := ''
        else
          FillingEdit.Text := Format ('%0.3f', [ADOQuery1.FieldByName ('FILLING_QUANTITY').AsInteger / 1000]);

        if ADOQuery1.FieldByName ('MIN_BESTAND').IsNull then
          MinArBesEdit.Text := ''
        else
          MinArBesEdit.Text := ADOQuery1.FieldByName ('MIN_BESTAND').AsString;

        if ADOQuery1.FieldByName ('MIN_ORDER_MENGE').IsNull then
          MinOrderBesEdit.Text := ''
        else
          MinOrderBesEdit.Text := ADOQuery1.FieldByName ('MIN_ORDER_MENGE').AsString;


        if multicolliflag then begin
          ColliCheckBox.Checked := True;

          query := TADOQuery.Create (Self);

          try
            query.LockType := ltReadOnly;
            query.Connection := LVSDatenModul.MainADOConnection;

            query.SQL.Add ('select aec.REF, aec.REF_SET_AR_EINHEIT, ae.COLLI_NR, ae.COLLI_NAME, aec.MENGE, ae.EINHEIT, ae.EAN from V_ARTIKEL_EINHEIT_COLLI aec, V_ARTIKEL_EINHEIT ae where ae.REF=aec.REF_SET_AR_EINHEIT and aec.REF_MASTER_AR_EINHEIT='+IntToStr (Ref)+' order by ae.COLLI_NR');

            dispidx := ColliStringGrid.FixedCols;

            query.Open;

            while not (query.Eof) do begin
              ColliStringGrid.Objects [0,dispidx] := TGridRef.Create (query.Fields[0].AsInteger);
              ColliStringGrid.Objects [1,dispidx] := TGridRef.Create (query.Fields[1].AsInteger);

              ColliStringGrid.Cells [1,dispidx] := query.Fields[2].AsString;
              ColliStringGrid.Cells [2,dispidx] := query.Fields[3].AsString;
              ColliStringGrid.Cells [3,dispidx] := query.Fields[4].AsString;
              ColliStringGrid.Cells [4,dispidx] := query.Fields[5].AsString;
              ColliStringGrid.Cells [5,dispidx] := query.Fields[6].AsString;

              Inc (dispidx);

              query.Next;
            end;

            query.Close;

            if (dispidx > ColliStringGrid.FixedCols) then
              ColliStringGrid.RowCount := dispidx
            else begin
              ColliStringGrid.RowCount := ColliStringGrid.FixedCols;
              ColliStringGrid.Rows [ColliStringGrid.RowCount - 1].Clear;
            end;
          finally
            query.Free;
          end;
        end;

        ADOQuery1.Close;
      except
      end;
    end;

    CuttingFullCheckBox.Visible  := CuttingCheckBox.Visible;
    FullPalOverCheckBox.Visible  := FullPalPickCheckBox.Visible;

    if (fRefSubMand > 0) then
      CheckConfigParameter (fRefSubMand, -1, -1, 'EAN_NUMMER', eanstr)
    else
      CheckConfigParameter (fRefMand, -1, -1, 'EAN_NUMMER', eanstr);

    CreateEANMenuItem.Enabled := (Length (eanstr) > 0);
  finally
    Screen.Cursor := crDefault;
  end;

  fPurchesChanged   := False;
  fSizeColorChanged := False;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TEditArtikelEinheitenForm.InhaltAnzEditChange(Sender: TObject);
begin
  if (InhaltArComboBox.ItemIndex > 0) then begin
    if Assigned (InhaltArComboBox.Items.Objects [InhaltArComboBox.ItemIndex]) then begin
      if not (fNettoChanged) and (TComboArtikelEinheit (InhaltArComboBox.Items.Objects [InhaltArComboBox.ItemIndex]).NettoGewicht <> 0) then
        NettoEdit.Text := Format ('%0.3f', [TComboArtikelEinheit (InhaltArComboBox.Items.Objects [InhaltArComboBox.ItemIndex]).NettoGewicht * InhaltAnzUpDown.Position / 1000]);

      if not (fBruttoChanged) and (TComboArtikelEinheit (InhaltArComboBox.Items.Objects [InhaltArComboBox.ItemIndex]).BruttoGewicht <> 0) then
        BruttoEdit.Text := Format ('%0.3f', [TComboArtikelEinheit (InhaltArComboBox.Items.Objects [InhaltArComboBox.ItemIndex]).BruttoGewicht * InhaltAnzUpDown.Position / 1000]);
    end;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TEditArtikelEinheitenForm.InhaltAnzEditExit(Sender: TObject);
begin
  if (Sender = InhaltArComboBox) then
    InhaltArComboBox.Enabled := (InhaltAnzUpDown.Position > 0) or (Length (InhaltArNrEdit.Text) > 0)
  else
    ContArComboBox.Enabled := (ContNumerUpDown.Position > 0) or (ContDenominUpDown.Position > 0) or (Length (ContArNrEdit.Text) > 0);
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TEditArtikelEinheitenForm.InhaltArComboBoxChange(Sender: TObject);
begin
  if (InhaltArComboBox.ItemIndex > 0) then begin
    InhaltAnzUpDown.Enabled := True;
    InhaltAnzEdit.Enabled := True;
  end else begin
    InhaltAnzUpDown.Enabled := False;
    InhaltAnzEdit.Enabled := False;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TEditArtikelEinheitenForm.InhaltArComboBoxDropDown(Sender: TObject);
var
  idx,
  ref,
  dlgres,
  selidx : Integer;
  vpestr : String;
  combo  : TComboBoxPro;
begin
  if not (fEinheitenLoaded) then begin
    combo := (Sender as TComboBoxPro);

    ref := GetComboBoxRef (combo);

    selidx := -1;

    ClearComboBoxObjects (combo);
    combo.Items.Add ('');

    //Die möglichen Inhaltsartikel laden
    ADOQuery1.SQL.Clear;
    ADOQuery1.SQL.Add ('select iae.REF_AE_EINHEIT, iae.ARTIKEL_NR, iae.EINHEIT, iae.ARTIKEL_TEXT, iae.NETTO_GEWICHT, iae.BRUTTO_GEWICHT, iae.INHALT_ANZAHL from V_ARTIKEL_LISTE iae, V_ARTIKEL a, V_ARTIKEL ia');
    ADOQuery1.SQL.Add ('where ia.REF=iae.REF and ia.REF_MAND=a.REF_MAND and ((a.REF_SUB_MAND is null and ia.REF_SUB_MAND is null) or a.REF_SUB_MAND=ia.REF_SUB_MAND) and a.REF=:ref_ar');
    ADOQuery1.Parameters.ParamByName('ref_ar').Value := fRefArtikel;

    if (fRef <> -1) then begin
      ADOQuery1.SQL.Add ('and REF_AE_EINHEIT<>:ref_ae');
      ADOQuery1.Parameters.ParamByName('ref_ae').Value := fRef;
    end;

    if (Sender = InhaltArComboBox) then begin
      if (Length (InhaltArNrEdit.Text) > 0) then begin
        ADOQuery1.SQL.Add ('and iae.ARTIKEL_NR like :ar_nr');
        ADOQuery1.Parameters.ParamByName('ar_nr').Value := InhaltArNrEdit.Text+'%';
      end;
    end else begin
      if (Length (ContArNrEdit.Text) > 0) then begin
        ADOQuery1.SQL.Add ('and iae.ARTIKEL_NR like :ar_nr');
        ADOQuery1.Parameters.ParamByName('ar_nr').Value := ContArNrEdit.Text+'%';
      end;
    end;

    ADOQuery1.SQL.Add ('order by LPAD (ARTIKEL_NR, 32, ''0'')');

    Screen.Cursor := crSQLWait;

    try
      ADOQuery1.Open;

      if (ADOQuery1.RecordCount < 10000) then
        dlgres := mrYes
      else
        dlgres := MessageDLG (FormatMessageText (1364, [IntToStr (ADOQuery1.RecordCount)]), mtConfirmation, [mbYes, mbNo, mbCancel], 0);

      if (dlgres = mrYes) then begin
        combo.Items.BeginUpdate;

        try
          while not (ADOQuery1.Eof) do begin
            if (ADOQuery1.Fields [6].IsNull) or (ADOQuery1.Fields [6].AsInteger = 0) then
              vpestr := ADOQuery1.Fields [2].AsString
            else
              vpestr := ADOQuery1.Fields [2].AsString + ' ('+IntToStr (ADOQuery1.Fields [6].AsInteger)+')';

            with ADOQuery1 do
              idx := combo.Items.AddObject(Fields [1].AsString+'|'+vpestr+'|'+Fields [3].AsString, TComboArtikelEinheit.Create(Fields [0].AsInteger, Fields [4].AsInteger, Fields [5].AsInteger));

              if (ref <> -1) and (ref = ADOQuery1.Fields [0].AsInteger) then
                selidx := idx;

            ADOQuery1.Next;
          end;
        finally
          combo.Items.EndUpdate;
        end;
      end;

      ADOQuery1.Close;
    finally
      Screen.Cursor := crDefault;
    end;

    if (selidx = -1) then
      combo.ItemIndex := 0
    else
      combo.ItemIndex := selidx;

    fEinheitenLoaded := True;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TEditArtikelEinheitenForm.InhaltArNrEditChange(Sender: TObject);
begin
  fEinheitenLoaded := False;

  if (Sender = InhaltArNrEdit) then
    InhaltArComboBox.Enabled := (Length (InhaltArNrEdit.Text) > 0)
  else
    ContArComboBox.Enabled := (Length (ContArNrEdit.Text) > 0);
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TEditArtikelEinheitenForm.IntEditKeyPress(Sender: TObject; var Key: Char);
var
  wert : Integer;
begin
  if not (Key in ['0'..'9', #8,^C,^V]) then
    Key := #0
  else if (Key in ['0'..'9']) then begin
    if not (TryStrToInt((Sender as TEDit).Text + key, wert)) then
      Key := #0
    else if (wert > 99999) then Key := #0
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TEditArtikelEinheitenForm.Kopieren1Click(Sender: TObject);
begin
  EANEdit.CopyToClipboard
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TEditArtikelEinheitenForm.Lschen1Click(Sender: TObject);
begin
  EANEdit.ClearSelection;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TEditArtikelEinheitenForm.ContArComboBoxChange(Sender: TObject);
begin
  if (ContArComboBox.ItemIndex > 0) then begin
    ContNumerUpDown.Enabled := True;
    ContNumerEdit.Enabled := True;
    ContDenominUpDown.Enabled := True;
    ContDenominEdit.Enabled := True;
  end else begin
    ContNumerUpDown.Enabled := False;
    ContNumerEdit.Enabled := False;
    ContDenominUpDown.Enabled := False;
    ContDenominEdit.Enabled := False;
  end;
end;

procedure TEditArtikelEinheitenForm.CreateEANMenuItemClick(Sender: TObject);
var
  res,
  eannr  : Integer;
  numstr : String;
  eanstr : String;
begin
  if (fRefSubMand <= 0) then
    res := GetConfigSequenzNummer (fRefMand, -1, -1, 'EAN_NUMMER', eanstr, eannr)
  else begin
    res := GetConfigSequenzNummer (fRefSubMand, -1, -1, 'EAN_NUMMER', eanstr, eannr);

    if (Length (eanstr) = 0) then
      res := GetConfigSequenzNummer (fRefMand, -1, -1, 'EAN_NUMMER', eanstr, eannr);
  end;

  if (res = 0) and (Length (eanstr) > 0) then begin
    if (Length (eanstr) < 12) then begin
      numstr := IntToStr (eannr);

      if (Length (eanstr + numstr) > 7) then begin
        while (Length (eanstr + numstr) < 12) do eanstr := eanstr + '0';
      end else begin
        while (Length (eanstr + numstr) < 7) do eanstr := eanstr + '0';
      end;

      eanstr := eanstr + numstr
    end;

    eanstr := eanstr + GetEANCheckDigit (eanstr);

    EANEdit.Text := eanstr;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TEditArtikelEinheitenForm.CuttingCheckBoxClick(Sender: TObject);
begin
  CuttingFullCheckBox.Enabled := CuttingCheckBox.Checked;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TEditArtikelEinheitenForm.AbmessungEditChange(Sender: TObject);
begin
  VolumenEdit.Enabled := (Length (LaengeEdit.Text) = 0) and (Length (BreiteEdit.Text) = 0) and (Length (HoeheEdit.Text) = 0);
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TEditArtikelEinheitenForm.PurchesEditChange(Sender: TObject);
begin
  fPurchesChanged := True;
end;

procedure TEditArtikelEinheitenForm.Rckgngig1Click(Sender: TObject);
begin
  EANEdit.Undo;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TEditArtikelEinheitenForm.FillingEditExit(Sender: TObject);
var
  wert : Double;
begin
  if not (AbortButton.Focused) then begin
    if (Length ((Sender as TEdit).Text) > 0) Then begin
      if not (TryStrToFloat((Sender as TEdit).Text, wert)) then
        (Sender as TEdit).SetFocus
      else begin
        (Sender as TEdit).Text := Format ('%0.3f', [wert])
      end;
    end;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TEditArtikelEinheitenForm.FloatEditKeyPress(Sender: TObject; var Key: Char);
begin
  if not (Key in ['0'..'9', '.', ',', #8,^C,^V]) then
    Key := #0;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TEditArtikelEinheitenForm.FormCloseQuery(Sender: TObject; var CanClose: Boolean);
var
  preis,
  vol,
  nettowert,
  bruttowert : Double;
  res,
  palf,
  lagef      : Integer;
  query      : TADOQuery;
begin
  if (ModalResult <> mrOk) Then
    CanClose := True
  else begin
    CanClose := True;

    nettowert := 0;
    bruttowert := 0;
    palf := 0;
    lagef := 0;

    //Prüfen, ob die EAN bereits benutzt wird
    if (EANEdit.Enabled and (Length (EANEdit.Text) > 0) and (EANEdit.Text <> fOldEAN)) then begin
      query := TADOQuery.Create (Self);

      try
        query.LockType := ltReadOnly;
        query.Connection := LVSDatenModul.MainADOConnection;

        if (fRefSubMand > 0) then begin
          query.SQL.Add ('select * from V_ARTIKEL_LISTE where STATUS=''AKT'' and REF_SUB_MAND=:ref_sub_mand and EAN=:ean');
          query.Parameters.ParamByName ('ref_sub_mand').Value := fRefSubMand;
          query.Parameters.ParamByName ('ean').Value := EANEdit.Text;
        end else begin
          query.SQL.Add ('select * from V_ARTIKEL_LISTE where STATUS=''AKT'' and REF_SUB_MAND is null and REF_MAND=:ref_mand and EAN=:ean');
          query.Parameters.ParamByName ('ref_mand').Value := fRefMand;
          query.Parameters.ParamByName ('ean').Value := EANEdit.Text;
        end;

        query.Open;

        if (query.RecordCount > 0) then begin
          if (MessageDLG (FormatMessageText (1369, [EANEdit.Text, query.FieldByName('ARTIKEL_NR').AsString]), mtConfirmation, [mbYes, mbNo, mbCancel], 0) <> mrYes) then begin
            CanClose := False;

            EANEdit.SetFocus;
          end;
        end;

        query.Close;
      finally
        query.Free;
      end;
    end;

    if (CanClose) then begin
      CanClose := False;

      if (EinheitComboBox.ItemIndex = -1) then
        EinheitComboBox.SetFocus
      else if (Length (NettoEdit.Text) > 0) and not (TryStrToFloat(NettoEdit.Text, nettowert)) then
        NettoEdit.SetFocus
      else if (Length (BruttoEdit.Text) > 0) and not (TryStrToFloat(BruttoEdit.Text, bruttowert)) then
        BruttoEdit.SetFocus
      else if (nettowert > 0) and (bruttowert > 0) and (nettowert > bruttowert) then begin
        MessageDlg(FormatMessageText (1367, []), mtError, [mbOK], 0);
        NettoEdit.SetFocus;
      end else if (Length (PalFaktorEdit.Text) > 0) and not (TryStrToInt (PalFaktorEdit.Text, palf)) then
        PalFaktorEdit.SetFocus
      else if (Length (LagenFaktorEdit.Text) > 0) and not (TryStrToInt (LagenFaktorEdit.Text, lagef)) then
        LagenFaktorEdit.SetFocus
      else if (palf > 0) and (lagef > 0) and (lagef > palf) then begin
        MessageDlg(FormatMessageText (1368, []), mtError, [mbOK], 0);
        LagenFaktorEdit.SetFocus;
      end else if (WertTabSheet.TabVisible and PreisEdit.Enabled and (Length (PreisEdit.Text) > 0) and not (TryStrToFloat (PreisEdit.Text, preis))) then begin
        //Der Reiter muss auch sichtbar sein
        PageControl1.ActivePage := WertTabSheet;

        if PreisEdit.CanFocus then
          PreisEdit.SetFocus
      end else if VolumenEdit.Enabled and (Length (VolumenEdit.Text) > 0) and not (TryStrToFloat (VolumenEdit.Text, vol)) then begin
        VolumenEdit.SetFocus;
      end else begin
        res := SaveArtikelEinheit;

        if (res = 0) then
          CanClose := True
        else begin
          if (fRef = -1) then
            MessageDLG (FormatMessageText (1365, [LVSDatenModul.LastLVSErrorText]), mtError, [mbOK], 0)
          else
            MessageDLG (FormatMessageText (1366, [LVSDatenModul.LastLVSErrorText]), mtError, [mbOK], 0);

          CanClose := False;
        end;
      end;
    end;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
function TEditArtikelEinheitenForm.SaveArtikelEinheit : Integer;
var
  res,
  dbres,
  preis,
  anzvpe,
  hkl,
  h,
  palh,
  lagh,
  units,
  preisidx,
  stapel,
  unitsnorm,
  stapelfaktor,
  filling    : Integer;
  minbes,
  minorder   : Integer;
  refin,
  mengein,
  numerin,
  denomin    : Integer;
  m,
  gw,
  vol,
  tara,
  netto,
  brutto,
  unitsnetto : Double;
  opt        : String;
  dbok       : Boolean;
  dbitems    : TDBItemsDaten;
begin
  opt := '';

  if GewichtCheckBox.Visible and (GewichtCheckBox.Checked) then
    opt := opt + 'GW;';

  if (MasterCheckBox.Checked) then
    opt := opt + 'MA;';

  if (StueckCheckBox.Checked) then
    opt := opt + 'ST;';

  if (BeschaffungsCheckBox.Checked) then
    opt := opt + 'BES;';

  if not FullPalCheckBox.Visible then
    opt := opt + '~FULLPAL;'
  else if (FullPalCheckBox.Checked) then
    opt := opt + 'FULLPAL;';

  if (GetComboBoxRef (PfandTauschComboBox) <> -1) and (PfandOptCheckBox.Checked) then
    opt := opt + 'PFAND_OPT;';

  if LifeCheckBox.Enabled and (LifeCheckBox.Checked) then
    opt := opt + 'LIFE;';

  if (MuliColliPanel.Visible) then begin
    if ColliCheckBox.Checked then
      opt := opt + 'MULTI_COLLI;';
  end;

  if ReadyShipCheckBox.Checked then
    opt := opt + 'READY_SHIP;';

  if SperrgutCheckBox.Checked then
    opt := opt + 'BULKY;';

  if BigItemCheckBox.Checked then
    opt := opt + 'BIG;';

  if not PersistentCheckBox.Visible then
    opt := opt + '~PERSISTENT;'
  else if PersistentCheckBox.Checked then
    opt := opt + 'PERSISTENT;';

  if not CuttingCheckBox.Visible then
    opt := opt + '~CUTTING;'
  else begin
    if CuttingCheckBox.Checked  and CuttingFullCheckBox.Checked then
      opt := opt + 'CUTTING_FULL;'
    else if CuttingCheckBox.Checked then
      opt := opt + 'CUTTING;';
  end;

  if not FullPalPickCheckBox.Visible then
    opt := opt + '~PALPICK;'
  else begin
    if FullPalPickCheckBox.Checked  and FullPalOverCheckBox.Checked then
      opt := opt + 'PALPICK_OVER;'
    else if FullPalPickCheckBox.Checked then
      opt := opt + 'PALPICK;';
  end;

  if (Length (NettoEdit.Text) > 0) then
    netto := StrToFloat (NettoEdit.Text) * 1000
  else netto := -1;

  if (Length (BruttoEdit.Text) > 0) then
    brutto := StrToFloat (BruttoEdit.Text) * 1000
  else brutto := -1;

  if not FillingEdit.Visible then
    filling := -1
  else if (Length (FillingEdit.Text) > 0) then
    filling := Round (StrToFloat (FillingEdit.Text) * 1000)
  else filling := -1;

  if (Length (AnzColliEdit.Text) > 0) then
    anzvpe := StrToInt (AnzColliEdit.Text)
  else anzvpe := -1;

  if not StapelEdit.Visible then
    stapel := -1
  else if (Length (StapelEdit.Text) > 0) then
    stapel := StrToInt (StapelEdit.Text)
  else stapel := -1;

  if not StapelFaktorEdit.Visible then
    stapelfaktor := -1
  else if (Length (StapelFaktorEdit.Text) > 0) then
    stapelfaktor := StrToInt (StapelFaktorEdit.Text)
  else stapelfaktor := -1;

  if (PreisEinheitComboBox.ItemIndex <= 0) then begin
    preisidx := -1;
    preis := -1;
  end else begin
    preisidx := PreisEinheitComboBox.ItemIndex;

    if (Length (PreisEdit.Text) > 0) then
      preis := Round (StrToFloat (PreisEdit.Text) * 1000)
    else preis := -1;
  end;

  refin   := -1;
  mengein := -1;
  numerin := -1;
  denomin := -1;

  if InhaltPanel.Visible then begin
    if (GetComboBoxRef (InhaltArComboBox) > 0) and (InhaltAnzUpDown.Position > 0) then begin
      refin   := GetComboBoxRef (InhaltArComboBox);
      mengein := InhaltAnzUpDown.Position;
    end;
  end else if ContPanel.Visible then begin
    if (GetComboBoxRef (ContArComboBox) > 0) and (ContNumerUpDown.Position > 0)  and (ContDenominUpDown.Position > 0) then begin
      refin   := GetComboBoxRef (ContArComboBox);
      numerin := ContNumerUpDown.Position;
      denomin := ContDenominUpDown.Position;
    end;
  end;

  dbres := 0;
  dbok := False;

  //Wenn ja alles in einer ganzen Transaktion verpacken
  while (dbres = 0) and not (dbok) do begin
    LVSDatenModul.BeginTransaction (LVSDatenModul.MainADOConnection);

    try
      if (fRef = -1) then begin
        dbres := InsertArtikelEinheit (fRefArtikel,
                                       GetComboBoxRef (EinheitComboBox),
                                       refin, mengein,
                                       NameEdit.Text, EANEdit.Text, BarcodeEdit.Text,
                                       DBStrToInt (LaengeEdit.Text),
                                       DBStrToInt (BreiteEdit.Text),
                                       DBStrToInt (HoeheEdit.Text),
                                       netto, brutto, anzvpe,
                                       DBStrToInt (PalFaktorEdit.Text),
                                       DBStrToInt (LagenFaktorEdit.Text),
                                       preisidx,
                                       preis,
                                       opt,
                                       GetComboBoxRef (PfandTauschComboBox),
                                       fRef);
      end else begin
        dbres := ChangeArtikelEinheit (fRef,
                                       UpdKey,
                                       GetComboBoxRef (EinheitComboBox),
                                       refin, mengein,
                                       NameEdit.Text, EANEdit.Text, BarcodeEdit.Text,
                                       DBStrToInt (LaengeEdit.Text),
                                       DBStrToInt (BreiteEdit.Text),
                                       DBStrToInt (HoeheEdit.Text),
                                       netto, brutto, anzvpe,
                                       DBStrToInt (PalFaktorEdit.Text),
                                       DBStrToInt (LagenFaktorEdit.Text),
                                       preisidx,
                                       preis,
                                       opt,
                                       GetComboBoxRef (PfandTauschComboBox));
      end;

      if (dbres = 0) and ContPanel.Visible then
        dbres := SetInhaltArtikel (fRef, refin, numerin, denomin);

      if (dbres = 0) then begin
        if (HKLComboBox.ItemIndex = 0) then
          hkl := -1
        else if not (TryStrToInt (HKLComboBox.GetItemText, hkl)) then
          hkl := -1;

        if (Length (PalHeightEdit.Text) = 0) then
          palh := -1
        else if not (TryStrToInt (PalHeightEdit.Text, palh)) then
          palh := -1;

        if (Length (LayerHeightEdit.Text) = 0) then
          lagh := -1
        else if not (TryStrToInt (LayerHeightEdit.Text, lagh)) then
          lagh := -1;

        if LTWAComboBox.Visible then
          dbres := ChangeArtikelEinheitInfo (fRef, UpdKeyInfo, GetComboBoxRef (LTWEComboBox), GetComboBoxRef (LTWAComboBox), hkl, palh, lagh, -1)
        else
          dbres := ChangeArtikelEinheitInfo (fRef, UpdKeyInfo, GetComboBoxRef (LTWEComboBox), hkl, palh);
      end;

      if (dbres = 0) and (KommTabSheet.TabVisible) and (KommArtComboBox.Enabled) then begin
        dbres := ChangeArtikelEinheitKommArt (fRef, UpdKeyInfo, GetComboBoxDBItemWert (KommArtComboBox));
      end;

      if (dbres = 0) and (SizeTabSheet.TabVisible) and fSizeColorChanged then begin
        if (MaterialCodeEdit.Visible) then
          dbres := ChangeArtikelEigenschaften (fRef, -1, ColorCodeEdit.Text, ColorTextEdit.Text, SizeCodeEdit.Text, SizeTextEdit.Text, MaterialCodeEdit.Text, MaterialTextEdit.Text)
        else dbres := ChangeArtikelEigenschaften (fRef, -1, ColorCodeEdit.Text, ColorTextEdit.Text, SizeCodeEdit.Text, SizeTextEdit.Text);
      end;

      if (dbres = 0) and (FillingEdit.Visible) then begin
        dbres := ChangeArtikelEinheitFilling (fRef, filling);
      end;

      if (dbres = 0) and (IDPanel.Visible) then
        dbres := ChangeArtikelEinheitID (fRef, IDEdit.Text);

      if (dbres = 0) and (ProdIDEdit.Visible) then
        dbres := ChangeArtikelEinheitProduktID (fRef, ProdIDEdit.Text);

      if (dbres = 0) and (StapelEdit.Visible) then
        dbres := ChangeArtikelEinheitStapelFolge (fRef, stapel);

      if (dbres = 0) and (VersandArtComboBox.Visible) then
        dbres := ChangeArtikelEinheitVersandArt (fRef, GetComboBoxDBItemWert (VersandArtComboBox));

      if (dbres = 0) and (VersandSpedComboBox.Visible) then
        dbres := ChangeArtikelEinheitVersandSped (fRef, GetComboBoxDBItemWert (VersandSpedComboBox));

      if (dbres = 0) and (StapelFaktorEdit.Visible) then
        dbres := ChangeArtikelEinheitStapelFaktor (fRef, stapelfaktor);

      if (dbres = 0) and (UnitPanel.Visible) then begin
        if (UnitsComboBox.ItemIndex = -1) then
          units := -1
        else begin
          dbitems := GetComboBoxDBItem (UnitsComboBox);

          if not Assigned (dbitems) or not (TryStrToInt (dbitems.ItemsWert, units)) then
            units := -1
        end;

        if (units = -1) then
          unitsnetto := -1
        else if (Length (UnitsNettoEdit.Text) = 0) then
          unitsnetto := -1
        else if not TryStrToFloat (UnitsNettoEdit.Text, gw) then
          unitsnetto := -1
        else
          unitsnetto := Round (gw * 1000);

        if (units = -1) then
          unitsnorm := -1
        else if (Length (UnitsNormEdit.Text) = 0) then
          unitsnorm := -1
        else if not TryStrToFloat (UnitsNormEdit.Text, m) then
          unitsnorm := -1
        else
          unitsnorm := Round (m * 1000);

        if (Length (TaraEdit.Text) > 0) then
          tara := Round (StrToFloat (TaraEdit.Text) * 1000)
        else tara := -1;

        dbres := ChangeArtikelEinheitUnits (fRef, UpdKeyInfo, netto, tara, unitsnetto, units, unitsnorm);
      end;

      if (res = 0) and (OrderTabSheet.TabVisible) and fPurchesChanged then begin
        if (Length (MinArBesEdit.Text) = 0) then
          minbes := -1
        else if not TryStrToInt (MinArBesEdit.Text, minbes) then
          minbes := 0;

        if (Length (MinOrderBesEdit.Text) = 0) then
          minorder := -1
        else if not TryStrToInt (MinOrderBesEdit.Text, minorder) then
          minorder := 0;

        dbres := ChangeArtikelBeschaffung (fRef, -1, minbes, minorder);
      end;

      if (res = 0) and VolumenEdit.Enabled and (Length (VolumenEdit.Text) > 0) then begin
        if TryStrToFloat (VolumenEdit.Text, vol) then begin
          vol := vol * 1000 * 1000 * 1000;

          dbres := ChangeArtikelEinheitVolumen (fRef, Trunc (vol));
        end;
      end;

      if (res = 0) and (VersandTabSheet.Visible and (VerpackArtEdit.Visible or VerpackFormEdit.Visible)) then
      begin
        //Nur aufrufen, wenn die beiden Felder auch sichtbar sind
        dbres := ChangeArtikelEinheitVerpackung(fRef, VerpackArtEdit.Text, VerpackFormEdit.Text, GetComboBoxDBItemWert (VersandArtComboBox), -1);
      end;

      dbok := True;

      if (dbres <> 0) then begin
        res := dbres;

        LVSDatenModul.EndTransaction (LVSDatenModul.MainADOConnection, trRollback);
      end else begin
        LVSDatenModul.EndTransaction (LVSDatenModul.MainADOConnection, trCommit);
      end;
    except
      on E: EOracleRetryException do begin
        dbres := 0;

        ErrorTrackingModule.WriteErrorLog ('EOracleRetryException TEditArtikelEinheitenForm.SaveArtikelEinheit', e.ClassName + ' : ' + e.Message);
      end;

      on  E: Exception do begin
        res := -9;

        LVSDatenModul.EndTransaction (LVSDatenModul.MainADOConnection, trRollback);

        ErrorTrackingModule.WriteErrorLog ('Exception TEditArtikelEinheitenForm.SaveArtikelEinheit', e.ClassName + ' : ' + e.Message);
      end;
    end;
  end;

  Result := res;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TEditArtikelEinheitenForm.FormCreate(Sender: TObject);
begin
  fOldEAN := '';

  InhaltArComboBox.ColWidths [0] := 80;
  InhaltArComboBox.ColWidths [1] := 140;

  ContArComboBox.ColWidths [0] := 80;
  ContArComboBox.ColWidths [1] := 140;

  fPurchesChanged   := False;
  fSizeColorChanged := False;

  LVSSprachModul.InitForm (Self);

  {$ifdef TranslateHelper}
    LVSSprachModul.SetNoTranslate (Self, EinheitComboBox);
    LVSSprachModul.SetNoTranslate (Self, InhaltArComboBox);
    LVSSprachModul.SetNoTranslate (Self, ContArComboBox);
    LVSSprachModul.SetNoTranslate (Self, EANEdit);
    LVSSprachModul.SetNoTranslate (Self, BarcodeEdit);
    LVSSprachModul.SetNoTranslate (Self, InhaltAnzEdit);
    LVSSprachModul.SetNoTranslate (Self, KommArtComboBox);
    LVSSprachModul.SetNoTranslate (Self, PfandTauschComboBox);
    LVSSprachModul.SetNoTranslate (Self, LTWEComboBox);
    LVSSprachModul.SetNoTranslate (Self, LTWAComboBox);
    LVSSprachModul.SetNoTranslate (Self, HKLComboBox);
  {$endif}
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TEditArtikelEinheitenForm.FormDestroy(Sender: TObject);
begin
  ClearComboBoxObjects (LTWEComboBox);
  ClearComboBoxObjects (LTWAComboBox);
  ClearComboBoxObjects (HKLComboBox);
  ClearComboBoxObjects (InhaltArComboBox);
  ClearComboBoxObjects (ContArComboBox);
  ClearComboBoxObjects (KommArtComboBox);
  ClearComboBoxObjects (PfandTauschComboBox);
  ClearComboBoxObjects (EinheitComboBox);
  ClearComboBoxObjects (VersandArtComboBox);
  ClearComboBoxObjects (VersandSpedComboBox);
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TEditArtikelEinheitenForm.FormShow(Sender: TObject);
begin
  if not (UnitPanel.Visible) then
    Height := Height - UnitPanel.Height;

  if not (ContPanel.Visible) then
    Height := Height - ContPanel.Height
  else if not (InhaltPanel.Visible) then
    Height := Height - InhaltPanel.Height;

  if not (GewichtPanel.Visible) then
    Height := Height - GewichtPanel.Height;

  if not (IDPanel.Visible) then
    Height := Height - IDPanel.Height
  else
    IDEdit.MaxLength := ArtikelNrSize;


  if not (LVSConfigModul.UseColorSize) then
    SizeTabSheet.TabVisible := False;

  if not (LVSConfigModul.UsePfandgut) then
    PfandTabSheet.TabVisible := False;

  if not (LVSConfigModul.PrjArtikelCollis) then
    MuliColliPanel.Visible := False;

  if not (Assigned (PageControl1.ActivePage)) or not (PageControl1.ActivePage.TabVisible) then
    PageControl1.ActivePage  := PageControl1.FindNextPage (Nil, True, True);

  InventarTabSheet.TabVisible := LVSConfigModul.UseLifeCycle;
  OrderTabSheet.TabVisible    := LVSSecurityModule.CheckReadRecht   ('', '', '', 'Waren-Beschaffung');

  MinArBesEdit.Enabled     := LVSSecurityModule.CheckChangeRecht ('', '', '', 'Waren-Beschaffung');
  MinOrderBesEdit.Enabled  := LVSSecurityModule.CheckChangeRecht ('', '', '', 'Waren-Beschaffung');

  Label33.Visible := NameEdit.Visible;
  Label54.Visible := ProdIDEdit.Visible;
  Label48.Visible := StapelFaktorEdit.Visible;
  Label43.Visible := StapelEdit.Visible;
  Label44.Visible := LayerHeightEdit.Visible;
  Label45.Visible := LayerHeightEdit.Visible;
  Label46.Visible := FillingEdit.Visible;
  Label47.Visible := FillingEdit.Visible;
  Label21.Visible := PalHeightEdit.Visible;
  Label19.Visible := PalHeightEdit.Visible;
  Label51.Visible := VerpackArtEdit.Visible;
  Label52.Visible := VerpackFormEdit.Visible;
  Label55.Visible := VersandArtComboBox.Visible;
  Label56.Visible := VersandSpedComboBox.Visible;

  if not LTWAComboBox.Visible then
    Label57.Visible := false
  else begin
    if not (VerpackFormEdit.Visible) then begin
      if not (VerpackArtEdit.Visible) then begin
        LTWAComboBox.Left := VerpackArtEdit.Left;
        LTWAComboBox.Width := VersandTabSheet.ClientWidth - (LTWAComboBox.Left * 2);
      end else begin
        LTWAComboBox.Left := VerpackFormEdit.Left;
        LTWAComboBox.Width := VersandTabSheet.ClientWidth - LTWAComboBox.Left - 8;
      end;
    end;

    Label57.Left := LTWAComboBox.Left;
  end;

  InhaltAnzEditExit (Sender);
  AbmessungEditChange (Sender);
  CuttingCheckBoxClick (Sender);

  if (IDPanel.Visible and (Length (IDEdit.Text) = 0) and IDEdit.CanFocus) then
    IDEdit.SetFocus
  else if (Length (EANEdit.Text) = 0) and EANEdit.CanFocus then
    EANEdit.SetFocus
  else if (Length (BarcodeEdit.Text) = 0) and BarcodeEdit.CanFocus then
    BarcodeEdit.SetFocus
  else
    OkButton.SetFocus;
end;

procedure TEditArtikelEinheitenForm.FullPalPickCheckBoxClick(Sender: TObject);
begin
  FullPalOverCheckBox.Enabled := FullPalPickCheckBox.Checked;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TEditArtikelEinheitenForm.GewichtEditExit(Sender: TObject);
var
  wert : Double;
begin
  if not (AbortButton.Focused) then begin
    if (Length ((Sender as TEdit).Text) > 0) Then begin
      if not (TryStrToFloat((Sender as TEdit).Text, wert)) then
        (Sender as TEdit).SetFocus
      else if ArtikelGewichtIsFloat then
        (Sender as TEdit).Text := Format ('%0.5f', [wert])
      else
        (Sender as TEdit).Text := Format ('%0.3f', [wert])
    end;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TEditArtikelEinheitenForm.Allesauswhlen1Click(Sender: TObject);
begin
  EANEdit.SelectAll;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TEditArtikelEinheitenForm.Ausschneiden1Click(Sender: TObject);
begin
  EANEdit.CutToClipboard;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TEditArtikelEinheitenForm.EANEditChange(Sender: TObject);
var
  idx : Integer;
  editstr : String;
begin
  editstr := EANEdit.Text;

  idx := 1;

  while (idx <= Length (editstr)) do begin
    if (editstr [idx] = ' ') then
      Delete (editstr, idx, 1)
    else
      Inc (idx);
  end;

  EANEdit.Text := editstr;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TEditArtikelEinheitenForm.EANEditExit(Sender: TObject);
var
  ch     : Char;
  len,
  dlgres : Integer;
begin
  if not (AbortButton.Focused) then begin
    len := Length (EANEdit.Text);

    if (len > 0) then begin
      if (len <> 8) and (len <> 12) and (len <> 13) and (len <> 14) then begin
        if (MessageDlg(FormatMessageText (1104, []), mtError, [mbNo, mbYes], 0) = mrNo) then
          EANEdit.SetFocus;
      end else begin
        ch := GetEANCheckDigit (copy (EANEdit.Text, 1, len - 1));

        if (ch <> EANEdit.Text [len]) then begin
          if (len = 12) then
            dlgres := MessageDlg (FormatMessageText (1103, []), mtError, [mbNo, mbYes], 0)
          else dlgres := MessageDlg (FormatMessageText (1104, []), mtError, [mbNo, mbYes], 0);

          if (dlgres = mrNo) then
            EANEdit.SetFocus;
        end;
      end;
    end;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TEditArtikelEinheitenForm.OkButtonClick(Sender: TObject);
begin
  ModalResult := mrOk;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TEditArtikelEinheitenForm.EANEditKeyPress(Sender: TObject; var Key: Char);
begin
  if not (Key in ['0'..'9', #8, ^C,^V]) then
    Key := #0;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TEditArtikelEinheitenForm.EANEditPopupMenuPopup(Sender: TObject);
begin
  Rckgngig1.Enabled := EANEdit.CanUndo;
  Einfgen1.Enabled := (Length (Clipboard.AsText) > 0);
  Kopieren1.Enabled := (EANEdit.SelLength > 0);
  Ausschneiden1.Enabled := Kopieren1.Enabled;
  Lschen1.Enabled := Kopieren1.Enabled;
  Allesauswhlen1.Enabled := (EANEdit.SelLength > 0) and (EANEdit.SelLength < Length(EANEdit.Text));
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TEditArtikelEinheitenForm.Einfgen1Click(Sender: TObject);
begin
  EANEdit.PasteFromClipboard;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TEditArtikelEinheitenForm.VEPButtonClick(Sender: TObject);
var
  idx      : Integer;
  editform : TEditVPEForm;
begin
  editform := TEditVPEForm.Create (Self);

  try
    editform.Caption := GetResourceText (1392);

    editform.Referenz   := -1;
    editform.RefMand    := fRefMand;
    editform.RefSubMand := fRefSubMand;

    if (editform.ShowModal = mrOk) Then begin
      idx := EinheitComboBox.Items.AddObject(editform.KurzEdit.Text+'|'+editform.LangEdit.Text, TComboBoxRef.Create (editform.Referenz));

      EinheitComboBox.ItemIndex := idx;
    end;
  finally
    editform.Release;
  end;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TEditArtikelEinheitenForm.VolumenEditKeyPress(Sender: TObject; var Key: Char);
begin
  if not (Key in [',','.','0'..'9', #8,^C,^V]) then
    Key := #0
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TEditArtikelEinheitenForm.ScannerErfassung (var Message: TMessage);
var
  ean_nr,
  errmsg     : String;
  ean28flag,
  ean128flag : Boolean;
  barcode    : TEANBarcode;
begin
  FillChar (barcode, sizeof (TEANBarcode), 0);

  barcode.NVE := '';

  ean_nr := '';
  errmsg := '';
  ean28flag := False;
  ean128flag := False;

  if (Length (ScanCode) = 14) and (ScanCode [1] = EAN13ID) then begin
    //Bei 28er und 29er EANs wird der variable Teil auf 00000 gesetzt
    if ((Copy (ScanCode, 2, 2) = '28') or (Copy (ScanCode, 2, 2) = '29')) then begin
      ean_nr := Copy (ScanCode, 2, 7) + '00000';
      EANEdit.Text := ean_nr + GetEANCheckDigit (ean_nr);
    end else begin
      EANEdit.Text := Copy (ScanCode, 2, 13);
    end;
  end else if (ScannerTyp in [0,2,4]) and (Length (ScanCode) = 9) and (ScanCode [1] = EAN8ID) then begin
    EANEdit.Text := Copy (ScanCode, 2, 8);
  end else if (ScannerTyp in [1,3]) and (Length (ScanCode) = 10) and (ScanCode [1] = EAN8ID) and (ScanCode [2] = 'F') then begin
    EANEdit.Text := Copy (ScanCode, 3, 8);
  end else if (Length (ScanCode) > 3) and (ScanCode [1] = EAN128ID) then begin
    if (DecodeEANBarcode (Copy (ScanCode, 2, Length (ScanCode) - 1), barcode, errmsg) <> 0) then begin
      if (Length (errmsg) = 0) then
        errmsg := FormatMessageText (1037, [])
    end else begin
      ean128flag := True;

      //Im EAN138
      if (Length (barcode.EAN) > 0) then
        ean_nr := barcode.EAN
      else if (Length (barcode.InhaltEAN) > 0) then
        ean_nr := barcode.InhaltEAN;

      if (Length (ean_nr) > 0) then begin
        if (ean_nr [1] = '0') then
          //Nur die führende 0 abschneiden
          Delete (ean_nr, 1, 1)
        else if (ean_nr [1] = '9') then begin
          //Die 9 für variabel abschneiden und Prüfziffer neu rechnen
          ean_nr := copy (ean_nr, 2, 12);

          ean_nr :=  ean_nr + GetEANCheckDigit (ean_nr);
        end;

        EANEdit.Text := ean_nr;
      end;
    end;
  end else begin
    if ((Length (ScanCode) - 1) > MaxBarcodeSize) then
      errmsg := FormatMessageText  (1039, [])
    else begin
      BarcodeEdit.Text := Copy (ScanCode, 2, Length (ScanCode) - 1);
    end;
  end;

  if (Length (errmsg) <> 0) then
    MessageDLG (errmsg, mtInformation	, [mbOk], 0);
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TEditArtikelEinheitenForm.SizeColorEditChange(Sender: TObject);
begin
  fSizeColorChanged := True;
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TEditArtikelEinheitenForm.UnitsComboBoxChange(Sender: TObject);
begin
  BruttoEdit.Enabled := not (UnitsComboBox.ItemIndex > 0);
  TaraEdit.Enabled   := not (BruttoEdit.Enabled);
end;

//******************************************************************************
//* Function Name:
//* Author       : Stefan Graf
//******************************************************************************
//* Description
//******************************************************************************
//* Return Value :
//******************************************************************************
procedure TEditArtikelEinheitenForm.UnitsNormEditExit(Sender: TObject);
var
  wert : Double;
begin
  if not (AbortButton.Focused) then begin
    if (Length ((Sender as TEdit).Text) > 0) Then begin
      if not (TryStrToFloat((Sender as TEdit).Text, wert)) then
        (Sender as TEdit).SetFocus
      else begin
        (Sender as TEdit).Text := Format ('%0.3f', [wert])
      end;
    end;
  end;
end;

end.
