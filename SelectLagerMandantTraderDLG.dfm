object SelectLagerMandantTraderForm: TSelectLagerMandantTraderForm
  Left = 0
  Top = 0
  BorderStyle = bsDialog
  Caption = 'SelectLagerMandantTraderForm'
  ClientHeight = 163
  ClientWidth = 395
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -12
  Font.Name = 'Segoe UI'
  Font.Style = []
  Position = poOwnerFormCenter
  OnCreate = FormCreate
  TextHeight = 15
  object Label72: TLabel
    Left = 7
    Top = 8
    Width = 29
    Height = 15
    Caption = 'Lager'
  end
  object Label65: TLabel
    Left = 7
    Top = 36
    Width = 48
    Height = 15
    Caption = 'Mandant'
  end
  object Label66: TLabel
    Left = 7
    Top = 64
    Width = 77
    Height = 15
    Caption = 'Untermandant'
  end
  object Label67: TLabel
    Left = 7
    Top = 92
    Width = 49
    Height = 15
    Caption = 'Verk'#228'ufer'
  end
  object Bevel1: TBevel
    Left = 8
    Top = 120
    Width = 379
    Height = 8
    Shape = bsTopLine
  end
  object LagerComboBox: TComboBoxPro
    Left = 110
    Top = 5
    Width = 273
    Height = 22
    Style = csOwnerDrawFixed
    TabOrder = 0
  end
  object MandantComboBox: TComboBoxPro
    Left = 110
    Top = 33
    Width = 273
    Height = 22
    Style = csOwnerDrawFixed
    TabOrder = 1
    OnChange = MandantComboBoxChange
  end
  object SubMandComboBox: TComboBoxPro
    Left = 110
    Top = 61
    Width = 273
    Height = 22
    Style = csOwnerDrawFixed
    TabOrder = 2
    OnChange = SubMandComboBoxChange
  end
  object TraderComboBox: TComboBoxPro
    Left = 111
    Top = 89
    Width = 273
    Height = 22
    Style = csOwnerDrawFixed
    TabOrder = 3
  end
  object OkButton: TButton
    Left = 216
    Top = 130
    Width = 91
    Height = 25
    Caption = #220'bernehmen'
    ModalResult = 1
    TabOrder = 4
  end
  object AbortButton: TButton
    Left = 312
    Top = 130
    Width = 75
    Height = 25
    Caption = 'Abbrechen'
    ModalResult = 3
    TabOrder = 5
  end
end
