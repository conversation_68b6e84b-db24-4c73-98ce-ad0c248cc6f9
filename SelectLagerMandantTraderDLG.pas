﻿unit SelectLagerMandantTraderDLG;

interface

uses
  Winapi.Windows, Winapi.Messages, System.SysUtils, System.Variants, System.Classes, Vcl.Graphics,
  Vcl.Controls, Vcl.Forms, Vcl.Dialogs, Vcl.StdCtrls, Vcl.ExtCtrls, ComboBoxPro;

type
  TSelectLagerMandantTraderForm = class(TForm)
    Label72: TLabel;
    LagerComboBox: TComboBoxPro;
    Label65: TLabel;
    MandantComboBox: TComboBoxPro;
    Label66: TLabel;
    SubMandComboBox: TComboBoxPro;
    Label67: TLabel;
    TraderComboBox: TComboBoxPro;
    Bevel1: TBevel;
    OkButton: TButton;
    AbortButton: TButton;
    procedure FormCreate(Sender: TObject);
    procedure MandantComboBoxChange(Sender: TObject);
    procedure SubMandComboBoxChange(Sender: TObject);
  private
    { Private-Deklarationen }
  public
    { Public-Deklarationen }
  end;

implementation

{$R *.dfm}

uses
  VCLUtilitys, FrontendUtils, DatenModul;

procedure TSelectLagerMandantTraderForm.FormCreate(Sender: TObject);
begin
  LoadLagerCombobox (LagerComboBox, LVSDatenModul.AktLocationRef);
  LoadMandantCombobox (MandantComboBox, LVSDatenModul.AktLocationRef);
end;

procedure TSelectLagerMandantTraderForm.MandantComboBoxChange(Sender: TObject);
begin
  if (SubMandComboBox.Visible) then begin
    ClearComboBoxObjects (SubMandComboBox);

    LoadSubMandantCombobox (SubMandComboBox, LVSDatenModul.AktLocationRef, GetComboBoxRef(MandantComboBox));

    if (SubMandComboBox.Items.Count = 0) then
      SubMandComboBox.Enabled := False
    else begin
      SubMandComboBox.Enabled := True;

      SubMandComboBox.Items.Insert (0, '');
      SubMandComboBox.ItemIndex := -1;
    end;
  end;

  if (TraderComboBox.Visible) then begin
    ClearComboBoxObjects (TraderComboBox);

    LoadTraderComboBox (TraderComboBox, LVSDatenModul.AktLocationRef, GetComboBoxRef(MandantComboBox), -1);

    if (TraderComboBox.Items.Count = 0) then
      TraderComboBox.Enabled := False
    else begin
      TraderComboBox.Enabled := True;

      TraderComboBox.Items.Insert (0, '');
      TraderComboBox.ItemIndex := -1;
    end;
  end;
end;

procedure TSelectLagerMandantTraderForm.SubMandComboBoxChange(Sender: TObject);
begin
  if (TraderComboBox.Visible) then begin
    ClearComboBoxObjects (TraderComboBox);

    LoadTraderComboBox (TraderComboBox, LVSDatenModul.AktLocationRef, GetComboBoxRef(MandantComboBox), GetComboBoxRef(SubMandComboBox));

    if (TraderComboBox.Items.Count = 0) then
      TraderComboBox.Enabled := False
    else begin
      TraderComboBox.Enabled := True;

      TraderComboBox.Items.Insert (0, '');
      TraderComboBox.ItemIndex := -1;
    end;
  end;
end;

end.
