object Form1: TForm1
  Left = 0
  Top = 0
  Caption = 'Form1'
  ClientHeight = 307
  ClientWidth = 398
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -12
  Font.Name = 'Segoe UI'
  Font.Style = []
  OnCloseQuery = FormCloseQuery
  OnShow = FormShow
  DesignSize = (
    398
    307)
  TextHeight = 15
  object Panel1: TPanel
    Left = 0
    Top = 0
    Width = 398
    Height = 41
    Align = alTop
    BevelOuter = bvNone
    Caption = 'Panel1'
    TabOrder = 0
    ExplicitLeft = 208
    ExplicitTop = 296
    ExplicitWidth = 185
  end
  object TourDBGrid: TDBGridPro
    Left = 0
    Top = 41
    Width = 398
    Height = 224
    Hint = 'Sortieren mit linker Maustaste, Suchen mit rechter Maustaste|'
    Align = alTop
    Constraints.MinHeight = 100
    DataSource = TourDataSource
    Options = [dgTitles, dgIndicator, dgColumnResize, dgColLines, dgRowLines, dgTabs, dgRowSelect, dgAlwaysShowSelection, dgConfirmDelete, dgCancelOnExit]
    ReadOnly = True
    TabOrder = 1
    TitleFont.Charset = DEFAULT_CHARSET
    TitleFont.Color = clWindowText
    TitleFont.Height = -12
    TitleFont.Name = 'Segoe UI'
    TitleFont.Style = []
    Flat = False
    BandsFont.Charset = DEFAULT_CHARSET
    BandsFont.Color = clWindowText
    BandsFont.Height = -11
    BandsFont.Name = 'MS Sans Serif'
    BandsFont.Style = []
    Groupings = <>
    GridStyle.Style = gsCustom
    GridStyle.OddColor = clWindow
    GridStyle.EvenColor = clWindow
    TitleHeight.PixelCount = 24
    FooterColor = clBtnFace
    ExOptions = [eoENTERlikeTAB, eoKeepSelection, eoTitleWordWrap]
    RegistryKey = 'Software\Scalabium'
    RegistrySection = 'SMDBGrid'
    WidthOfIndicator = 11
    DefaultRowHeight = 19
    ScrollBars = ssHorizontal
    ColCount = 2
    RowCount = 2
  end
  object NewButton: TButton
    Left = 8
    Top = 271
    Width = 75
    Height = 25
    Caption = 'Neue Tour'
    TabOrder = 2
    OnClick = NewButtonClick
  end
  object OkButton: TButton
    Left = 233
    Top = 272
    Width = 75
    Height = 25
    Anchors = [akRight, akBottom]
    Caption = #220'bernehmen'
    ModalResult = 1
    TabOrder = 3
    ExplicitLeft = 280
    ExplicitTop = 440
  end
  object Button3: TButton
    Left = 314
    Top = 272
    Width = 75
    Height = 25
    Anchors = [akRight, akBottom]
    Caption = 'Abbrechen'
    ModalResult = 3
    TabOrder = 4
    ExplicitLeft = 361
    ExplicitTop = 440
  end
  object TourDataSource: TOraDataSource
    DataSet = TourQuery
    Left = 208
    Top = 144
  end
  object TourQuery: TSmartQuery
    Left = 344
    Top = 112
  end
end
