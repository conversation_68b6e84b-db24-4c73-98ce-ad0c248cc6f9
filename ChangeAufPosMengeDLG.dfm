object ChangeAufPosMengeForm: TChangeAufPosMengeForm
  Left = 0
  Top = 0
  BorderStyle = bsDialog
  Caption = 'Sollmenge '#228'ndern'
  ClientHeight = 266
  ClientWidth = 318
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'Tahoma'
  Font.Style = []
  Position = poOwnerFormCenter
  OnCloseQuery = FormCloseQuery
  OnCreate = FormCreate
  OnShow = FormShow
  DesignSize = (
    318
    266)
  TextHeight = 13
  object Label2: TLabel
    Left = 8
    Top = 18
    Width = 51
    Height = 13
    Caption = 'Auftragnr.'
  end
  object Label3: TLabel
    Left = 8
    Top = 37
    Width = 30
    Height = 13
    Caption = 'Kunde'
  end
  object Label4: TLabel
    Left = 8
    Top = 66
    Width = 30
    Height = 13
    Caption = 'Artikel'
  end
  object Bevel1: TBevel
    Left = 8
    Top = 223
    Width = 305
    Height = 9
    Anchors = [akLeft, akRight, akBottom]
    Shape = bsTopLine
    ExplicitTop = 184
    ExplicitWidth = 303
  end
  object Bevel2: TBevel
    Left = 8
    Top = 104
    Width = 305
    Height = 9
    Anchors = [akLeft, akTop, akRight]
    Shape = bsTopLine
    ExplicitWidth = 303
  end
  object Bevel3: TBevel
    Left = 8
    Top = 56
    Width = 305
    Height = 9
    Anchors = [akLeft, akTop, akRight]
    Shape = bsTopLine
    ExplicitWidth = 303
  end
  object AufNrLabel: TLabel
    Left = 96
    Top = 18
    Width = 53
    Height = 13
    Caption = 'AufNrLabel'
  end
  object KundeLabel: TLabel
    Left = 96
    Top = 37
    Width = 55
    Height = 13
    Caption = 'KundeLabel'
  end
  object ArtikelLabel: TLabel
    Left = 96
    Top = 66
    Width = 55
    Height = 13
    Caption = 'ArtikelLabel'
  end
  object Label7: TLabel
    Left = 8
    Top = 174
    Width = 98
    Height = 13
    Caption = 'Grund der '#196'nderung'
  end
  object MengeLabel: TLabel
    Left = 96
    Top = 85
    Width = 57
    Height = 13
    Caption = 'MengeLabel'
  end
  object Label1: TLabel
    Left = 8
    Top = 85
    Width = 76
    Height = 13
    Caption = 'Bestellte Menge'
  end
  object Label5: TLabel
    Left = 8
    Top = 117
    Width = 76
    Height = 13
    Caption = 'Neue Sollmenge'
  end
  object OkButton: TButton
    Left = 154
    Top = 233
    Width = 75
    Height = 25
    Anchors = [akRight, akBottom]
    Caption = 'Ok'
    Default = True
    ModalResult = 1
    TabOrder = 3
  end
  object AbortButton: TButton
    Left = 235
    Top = 233
    Width = 75
    Height = 25
    Anchors = [akRight, akBottom]
    Cancel = True
    Caption = 'Abbrechen'
    ModalResult = 3
    TabOrder = 4
  end
  object MengeEdit: TEdit
    Left = 8
    Top = 136
    Width = 76
    Height = 21
    TabOrder = 0
    Text = '0'
    OnKeyPress = MengeEditKeyPress
  end
  object MengeUpDown: TIntegerUpDown
    Left = 84
    Top = 136
    Width = 16
    Height = 21
    Associate = MengeEdit
    TabOrder = 1
  end
  object GrundComboBox: TComboBox
    Left = 8
    Top = 190
    Width = 302
    Height = 21
    Style = csDropDownList
    Anchors = [akLeft, akTop, akRight]
    MaxLength = 64
    TabOrder = 2
    Items.Strings = (
      'Ware mangelhaft')
  end
end
