﻿unit LVSConst;

interface

const
  //Zeichen-Position innerhalb von MANDAN_CONFIG.CONFIG_OPT und LOCATION_CONFIG.CONFIG_OPT
  cMandLocalArtikelnr       = 5;
  cMandLiefRetBes           = 6;
  cMandLiefRetWE            = 7;
  cMandUpdIFCArLagerDaten   = 8;           //Die Schnittstelle kann die Lagerstammdaten der Artikeleinheiten L,B,H, Netto, Bru<PERSON> und Big, Sperr und ShipReady nicht überschreiben
  cMandArPictureWE          = 10;
  cMandWECheckLastMHD       = 11;
  cMandWECheckQSGrund       = 12;
  cMandWEChangeAREinheitOpt = 13;
  cMandWEBedarfOpt          = 14;
  cMandWEKommBestandOpt     = 15;
  cMandInvEinzelScan        = 16;
  cMandInvOldMHDBesAbgleich = 17;
  cMandInvAssigneCountPos   = 18;
  cMandInvANGBesAbgleich    = 19;
  cMandBesCreateGrund       = 20;
  cMandBesChangeGrund       = 21;
  cMandBesDeleteGrund       = 22;
  cMandBesLockGrund         = 23;
  cMandBesUnLockGrund       = 24;
  cMandBesQSGrund           = 25;
  cMandVorResGrund          = 26;
  cMandWEBeschaffEinheit    = 27;
  cMandBesINVGrund          = 28;
  cMandDelOrderGrund        = 29;
  cMandStornoOrderGrund     = 30;
  cMandWEBBDFromPast        = 31;
  cMandArPictureVerteil     = 32;
  cMandArPictureVerpack     = 33;
  cMandWECheckLSNo          = 34;
  cMandWECheckKFZNo         = 35;
  cMandLHMErfassen          = 36;
  cMandLHMAutoErfassen      = 37;
  cMandVerpackungErfassen   = 38;
  cMandWELeerProPosOpt      = 39;
  cMandRetSelectQuality     = 40;
  cMandRetSelectCategory    = 41;
  cMandRetSelectGrund       = 42;
  cMandRetSelectZustand     = 43;
  cMandRetParallelWE        = 44;
  cMandRetParallelAnnahme   = 45;
  cMandRetProcessPreGrading = 46;
  cMandWENachsBestandOpt    = 47;
  cMandRetAutoPrint         = 48;
  cMandWEFillARData         = 49;
  cMandBestNrUnique         = 50;
  cMandBestEnabled          = 51;
  cMandBesLockID            = 52;
  cMandBestAutoAddPos       = 53;
  cMandRetZustandComment    = 54;
  cMandRetGrundPflicht      = 55;
  cMandRetAvisAutoBuch      = 56;
  cMandRetourenLabel        = 57;
  cMandRetourenSchein       = 58;
  cMandLieferSchein         = 59;
  cMandUniqueChargen        = 60;
  cMandBestandBeleg         = 61;
  cMandWECheckSupplier      = 62;
  cMandWECategory           = 63;
  cMandWECheckPackType      = 64;
  cMandRetPrintLabel        = 65;
  cMandRetSerial            = 66;
  cMandRetSerialAll         = 67;
  cMandWESerial             = 68;
  cMandWESerialAll          = 69;
  cMandAbgleichAufEmpf      = 70;
  cMandAufEnabled           = 71;
  cMandAufSelEmpf           = 72;
  cMandAufAutoAddPos        = 73;
  cMandAufVorRes            = 74;
  cMandWEFillARMesData      = 75;
  cMandWEFillARPalData      = 76;
  cMandWEFillARAttData      = 77;
  cMandWEFillARBarData      = 78;
  cMandWEFillARShipData     = 79;
  cMandSelKommUser          = 80;
  cMandSelKommUserReset     = 81;
  cMandSelPacker            = 82;
  cMandSelPackerReset       = 83;
  cMandRetVAS               = 84;
  cMandAdrCheck             = 85;
  cMandArContFactor         = 87;    //Die Inhaltsmengen bei Verpackungen wird mit Zähler / Nennen angegeben
  cMandAufLiefAdr           = 88;    //Die Lieferadresse wird aus den Schnittstellen immer übernommen, auch wenn sie gleich der Kunden-Adresse ist
  cMandWEFillARGefahr       = 91;
  cMandWEFillARKlasse       = 92;
  cMandWEMehrMenge          = 93;
  cMandIFCAufPrio           = 94;   //Die Prio wird aus der Schnittstelle mit übernommen
  cMandWEAnzPackSt          = 95;
  cMandUseDefaultLT         = 96;
  cMandVerpackVorLTAccept   = 97;

implementation

end.
