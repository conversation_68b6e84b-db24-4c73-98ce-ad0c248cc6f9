object BatchdialogForm: TBatchdialogForm
  Left = 0
  Top = 0
  Caption = 'Konfiguration der B'#228'tchl'#228'ufe'
  ClientHeight = 837
  ClientWidth = 735
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'Tahoma'
  Font.Style = []
  Position = poOwnerFormCenter
  OnActivate = FormActivate
  OnClose = FormClose
  OnCloseQuery = FormCloseQuery
  OnCreate = FormCreate
  OnDestroy = FormDestroy
  OnShow = FormShow
  DesignSize = (
    735
    837)
  TextHeight = 13
  object Label25: TLabel
    Left = 280
    Top = 24
    Width = 80
    Height = 13
    Caption = 'Zul'#228'ssige L'#228'nder'
  end
  object Label26: TLabel
    Left = 280
    Top = 72
    Width = 118
    Height = 13
    Caption = 'Ausgeschlossene L'#228'nder'
  end
  object CloseButton: TButton
    Left = 650
    Top = 804
    Width = 75
    Height = 25
    Anchors = [akRight, akBottom]
    BiDiMode = bdLeftToRight
    Caption = 'Schlie'#223'en'
    ParentBiDiMode = False
    TabOrder = 0
    OnClick = CloseButtonClick
  end
  object ParamGroupBox: TGroupBox
    AlignWithMargins = True
    Left = 5
    Top = 256
    Width = 725
    Height = 541
    Margins.Left = 5
    Margins.Top = 0
    Margins.Right = 5
    Margins.Bottom = 40
    Align = alBottom
    Caption = 'Parameter'
    TabOrder = 1
    DesignSize = (
      725
      541)
    object Label2: TLabel
      Left = 16
      Top = 17
      Width = 27
      Height = 13
      Caption = 'Name'
    end
    object LabelSubMand: TLabel
      Left = 16
      Top = 77
      Width = 60
      Height = 13
      Caption = 'Submandant'
    end
    object Label3: TLabel
      Left = 16
      Top = 52
      Width = 42
      Height = 13
      Caption = 'Mandant'
    end
    object Label9: TLabel
      Left = 17
      Top = 107
      Width = 27
      Height = 13
      Caption = 'Lager'
    end
    object EditConfigName: TEdit
      Left = 97
      Top = 14
      Width = 243
      Height = 21
      MaxLength = 32
      TabOrder = 0
    end
    object ComboSubMandant: TComboBoxPro
      Left = 97
      Top = 77
      Width = 617
      Height = 22
      Style = csOwnerDrawFixed
      Anchors = [akLeft, akTop, akRight]
      TabOrder = 2
    end
    object ComboMandant: TComboBoxPro
      Left = 97
      Top = 49
      Width = 617
      Height = 22
      Style = csOwnerDrawFixed
      Anchors = [akLeft, akTop, akRight]
      Enabled = False
      TabOrder = 1
      OnChange = ComboMandantChange
    end
    object ComboLager: TComboBoxPro
      Left = 97
      Top = 104
      Width = 617
      Height = 22
      Style = csOwnerDrawFixed
      Anchors = [akLeft, akTop, akRight]
      TabOrder = 3
      OnChange = ComboLagerChange
    end
    object ApplyButton: TButton
      Left = 550
      Top = 502
      Width = 75
      Height = 25
      Anchors = [akRight, akBottom]
      Caption = #220'bernehmen'
      TabOrder = 5
      OnClick = ApplyButtonClick
    end
    object DiscardButton: TButton
      Left = 639
      Top = 501
      Width = 75
      Height = 25
      Anchors = [akRight, akBottom]
      Caption = 'Verwerfen'
      TabOrder = 6
      OnClick = DiscardButtonClick
    end
    object PageControl1: TPageControl
      Left = 3
      Top = 144
      Width = 706
      Height = 352
      ActivePage = RuleTabSheet
      Anchors = [akLeft, akRight, akBottom]
      TabOrder = 4
      object PlanTabSheet: TTabSheet
        Caption = 'Planung'
        ImageIndex = 1
        DesignSize = (
          698
          324)
        object Label4: TLabel
          Left = 12
          Top = 52
          Width = 45
          Height = 13
          Caption = 'Ablaufart'
          WordWrap = True
        end
        object Label5: TLabel
          Left = 12
          Top = 201
          Width = 68
          Height = 13
          Caption = 'Anz. Auftr'#228'ge'
          WordWrap = True
        end
        object Label13: TLabel
          Left = 74
          Top = 220
          Width = 16
          Height = 13
          Caption = 'Min'
        end
        object Label1: TLabel
          Left = 324
          Top = 96
          Width = 84
          Height = 13
          Caption = 'Kommissionierprio'
        end
        object Label7: TLabel
          Left = 125
          Top = 220
          Width = 20
          Height = 13
          Caption = 'Max'
        end
        object Label6: TLabel
          Left = 12
          Top = 8
          Width = 82
          Height = 13
          Caption = 'Batchauftragsart'
          WordWrap = True
        end
        object Label8: TLabel
          Left = 12
          Top = 96
          Width = 69
          Height = 13
          Caption = 'Ablaufplanung'
          WordWrap = True
        end
        object Label14: TLabel
          Left = 12
          Top = 245
          Width = 70
          Height = 13
          Caption = 'Max. Volumen '
        end
        object Label15: TLabel
          Left = 151
          Top = 245
          Width = 65
          Height = 13
          Caption = 'Max. Gewicht'
        end
        object Label16: TLabel
          Left = 74
          Top = 264
          Width = 14
          Height = 13
          Caption = 'm3'
        end
        object Label17: TLabel
          Left = 216
          Top = 264
          Width = 11
          Height = 13
          Caption = 'kg'
        end
        object Label18: TLabel
          Left = 446
          Top = 96
          Width = 85
          Height = 13
          Caption = 'Komm. Art Option'
        end
        object Label20: TLabel
          Left = 324
          Top = 8
          Width = 86
          Height = 13
          Caption = 'Transport-Gruppe'
          WordWrap = True
        end
        object Bevel2: TBevel
          Left = 12
          Top = 187
          Width = 678
          Height = 8
          Anchors = [akLeft, akTop, akRight]
          Shape = bsTopLine
        end
        object ComboAblauf: TComboBoxPro
          Left = 12
          Top = 68
          Width = 243
          Height = 22
          Style = csOwnerDrawFixed
          TabOrder = 0
        end
        object EditAnzahlMin: TEdit
          Left = 12
          Top = 217
          Width = 56
          Height = 21
          TabOrder = 6
        end
        object EditPrio: TEdit
          Left = 324
          Top = 112
          Width = 56
          Height = 21
          TabOrder = 4
        end
        object EditAnzahlMax: TEdit
          Left = 151
          Top = 217
          Width = 56
          Height = 21
          TabOrder = 7
        end
        object ComboAufBatch: TComboBoxPro
          Left = 12
          Top = 24
          Width = 243
          Height = 22
          Style = csOwnerDrawFixed
          TabOrder = 1
        end
        object ComboPlanart: TComboBoxPro
          Left = 12
          Top = 112
          Width = 243
          Height = 22
          Style = csOwnerDrawFixed
          TabOrder = 2
        end
        object MaxVolEdit: TEdit
          Left = 12
          Top = 261
          Width = 56
          Height = 21
          TabOrder = 8
        end
        object MaxWeigthEdit: TEdit
          Left = 151
          Top = 261
          Width = 56
          Height = 21
          TabOrder = 9
        end
        object KommArtOptEdit: TEdit
          Left = 446
          Top = 112
          Width = 121
          Height = 21
          MaxLength = 16
          TabOrder = 5
          Text = 'KommArtOptEdit'
        end
        object TAGroupComboBox: TComboBoxPro
          Left = 324
          Top = 24
          Width = 243
          Height = 22
          Style = csOwnerDrawFixed
          TabOrder = 3
        end
      end
      object WATabSheet: TTabSheet
        Caption = 'Abschluss'
        object Label11: TLabel
          Left = 19
          Top = 24
          Width = 45
          Height = 13
          Caption = 'Packplatz'
        end
        object Label12: TLabel
          Left = 20
          Top = 172
          Width = 44
          Height = 13
          Caption = 'WA-Platz'
        end
        object Label10: TLabel
          Left = 19
          Top = 128
          Width = 83
          Height = 13
          Caption = 'WA-Lagerbereich'
        end
        object ComboPackplatz: TComboBoxPro
          Left = 19
          Top = 40
          Width = 243
          Height = 22
          Style = csOwnerDrawFixed
          TabOrder = 0
        end
        object ComboLagerplatz: TComboBoxPro
          Left = 19
          Top = 188
          Width = 243
          Height = 22
          Style = csOwnerDrawFixed
          TabOrder = 2
        end
        object ComboBereich: TComboBoxPro
          Left = 19
          Top = 144
          Width = 243
          Height = 22
          Style = csOwnerDrawFixed
          TabOrder = 3
          OnChange = ComboBereichChange
        end
        object AutoSelPackplatzCheckBox: TCheckBox
          Left = 19
          Top = 88
          Width = 406
          Height = 17
          Caption = 'Automatisch einen freien Packplatz w'#228'hlen'
          TabOrder = 1
        end
      end
      object ConfigTabSheet: TTabSheet
        Caption = 'Konfiguration'
        ImageIndex = 2
        DesignSize = (
          698
          324)
        object Label19: TLabel
          Left = 14
          Top = 16
          Width = 43
          Height = 13
          Caption = 'Komm-LT'
        end
        object Check_OPT_BIG_ITEM: TCheckBox
          Left = 14
          Top = 83
          Width = 156
          Height = 17
          Caption = 'Big Items zul'#228'ssig'
          TabOrder = 1
        end
        object Check_OPT_SPERRGUT: TCheckBox
          Left = 208
          Top = 83
          Width = 156
          Height = 17
          Caption = 'Sperrgut zulassen'
          TabOrder = 2
        end
        object Check_OPT_AUTO_KOMM_PLAN: TCheckBox
          Left = 411
          Top = 83
          Width = 156
          Height = 17
          Caption = 'Komm. Automatisch planen'
          TabOrder = 3
        end
        object Check_OPT_BIG_ITEM_ONLY: TCheckBox
          Left = 14
          Top = 115
          Width = 156
          Height = 17
          Caption = 'Nur Big Items zul'#228'ssig'
          TabOrder = 4
        end
        object Check_OPT_SPERRGUT_ONLY: TCheckBox
          Left = 208
          Top = 115
          Width = 156
          Height = 17
          Caption = 'Nur Sperrgut zulassen'
          TabOrder = 5
        end
        object Check_OPT_KOMM_POS_VERDICHTEN: TCheckBox
          Left = 411
          Top = 115
          Width = 156
          Height = 17
          Caption = 'Komm. Positionen verdichten'
          TabOrder = 6
        end
        object RadioGroupEinpoester: TRadioGroup
          Left = 14
          Top = 153
          Width = 674
          Height = 44
          Anchors = [akLeft, akTop, akRight]
          Caption = 'Zul'#228'ssige Auftr'#228'ge'
          Columns = 3
          ItemIndex = 0
          Items.Strings = (
            'Alle Auftr'#228'ge '#252'bernehmen'
            'nur Einposten-Auftr'#228'ge '#252'bernehmen'
            'keine Einposten-Auftr'#228'ge '#252'bernehmen')
          TabOrder = 7
        end
        object RadioGroupKommPlanen: TRadioGroup
          Left = 14
          Top = 217
          Width = 674
          Height = 44
          Anchors = [akLeft, akTop, akRight]
          Caption = 'Art der Kommissionierplanung'
          Columns = 5
          ItemIndex = 0
          Items.Strings = (
            'Ohne Aufteilung'
            'Pro LB-Gruppe'
            'Pro LB'
            'Pro LB Zone'
            'Pro LB und Zone')
          TabOrder = 8
        end
        object KommLTComboBox: TComboBoxPro
          Left = 14
          Top = 32
          Width = 299
          Height = 22
          Style = csOwnerDrawFixed
          TabOrder = 0
        end
      end
      object PackTabSheet: TTabSheet
        Caption = 'Verteilen/Verpacken'
        ImageIndex = 3
        object Check_OPT_KOMM_COMPLETED: TCheckBox
          Left = 14
          Top = 49
          Width = 470
          Height = 17
          Caption = 'Nur vollst'#228'ndige Batchl'#228'ufe verteilen bzw. verpacken'
          TabOrder = 0
        end
      end
      object RuleTabSheet: TTabSheet
        Caption = 'Regeln'
        ImageIndex = 4
        DesignSize = (
          698
          324)
        object Label21: TLabel
          Left = 336
          Top = 16
          Width = 80
          Height = 13
          Caption = 'Zul'#228'ssige L'#228'nder'
        end
        object Label23: TLabel
          Left = 336
          Top = 92
          Width = 115
          Height = 13
          Caption = 'Zul'#228'ssige Auftragsarten'
        end
        object Label27: TLabel
          Left = 336
          Top = 244
          Width = 148
          Height = 13
          Caption = 'Zul'#228'ssige Auftragskennzeichen'
        end
        object Label29: TLabel
          Left = 8
          Top = 16
          Width = 96
          Height = 13
          Caption = 'Zul'#228'ssige Versender'
        end
        object Label30: TLabel
          Left = 175
          Top = 16
          Width = 128
          Height = 13
          Caption = 'Ausgeschlossen Versender'
        end
        object Label22: TLabel
          Left = 336
          Top = 168
          Width = 112
          Height = 13
          Caption = 'Zul'#228'ssige Versandarten'
        end
        object IncSpedCheckListBox: TCheckListBox
          Left = 5
          Top = 32
          Width = 148
          Height = 273
          ItemHeight = 13
          Style = lbOwnerDrawFixed
          TabOrder = 0
          OnDrawItem = SpedCheckListBoxDrawItem
        end
        object IncCountryEdit: TEdit
          Left = 344
          Top = 32
          Width = 340
          Height = 21
          Hint = 'Zul'#228'ssige L'#228'nder'
          Anchors = [akLeft, akTop, akRight]
          MaxLength = 64
          TabOrder = 1
          Text = 'IncCountryEdit'
        end
        object ExcCountryEdit: TEdit
          Left = 344
          Top = 58
          Width = 340
          Height = 21
          Hint = 'Ausgeschlossene L'#228'nder'
          Anchors = [akLeft, akTop, akRight]
          MaxLength = 64
          TabOrder = 2
          Text = 'ExcCountryEdit'
        end
        object IncAufArtEdit: TEdit
          Left = 344
          Top = 108
          Width = 340
          Height = 21
          Anchors = [akLeft, akTop, akRight]
          MaxLength = 64
          TabOrder = 3
          Text = 'IncAufArtEdit'
        end
        object ExcAufArtEdit: TEdit
          Left = 344
          Top = 134
          Width = 340
          Height = 21
          Anchors = [akLeft, akTop, akRight]
          MaxLength = 64
          TabOrder = 4
          Text = 'ExcAufArtEdit'
        end
        object IncIndicatorEdit: TEdit
          Left = 344
          Top = 260
          Width = 340
          Height = 21
          Anchors = [akLeft, akTop, akRight]
          MaxLength = 64
          TabOrder = 5
          Text = 'IncIndicatorEdit'
        end
        object ExcIndicatorEdit: TEdit
          Left = 344
          Top = 286
          Width = 340
          Height = 21
          Anchors = [akLeft, akTop, akRight]
          MaxLength = 64
          TabOrder = 6
          Text = 'ExcIndicatorEdit'
        end
        object ExcSpedCheckListBox: TCheckListBox
          Left = 173
          Top = 32
          Width = 148
          Height = 273
          ItemHeight = 13
          Style = lbOwnerDrawFixed
          TabOrder = 7
          OnDrawItem = SpedCheckListBoxDrawItem
        end
        object IncVersEdit: TEdit
          Left = 344
          Top = 184
          Width = 340
          Height = 21
          Anchors = [akLeft, akTop, akRight]
          MaxLength = 64
          TabOrder = 8
          Text = 'IncVersEdit'
        end
        object ExcVersEdit: TEdit
          Left = 344
          Top = 210
          Width = 340
          Height = 21
          Anchors = [akLeft, akTop, akRight]
          MaxLength = 64
          TabOrder = 9
          Text = 'ExcVersEdit'
        end
      end
    end
  end
  object BatchGrid: TDBGridPro
    Left = 8
    Top = 8
    Width = 719
    Height = 233
    Anchors = [akLeft, akTop, akRight, akBottom]
    DataSource = OraDataSource1
    Options = [dgTitles, dgIndicator, dgColumnResize, dgColLines, dgRowLines, dgTabs, dgRowSelect]
    PopupMenu = BatchGridPopupMenu
    ReadOnly = True
    TabOrder = 2
    TitleFont.Charset = DEFAULT_CHARSET
    TitleFont.Color = clWindowText
    TitleFont.Height = -11
    TitleFont.Name = 'Tahoma'
    TitleFont.Style = []
    Flat = False
    BandsFont.Charset = DEFAULT_CHARSET
    BandsFont.Color = clWindowText
    BandsFont.Height = -11
    BandsFont.Name = 'Tahoma'
    BandsFont.Style = []
    Groupings = <>
    GridStyle.Style = gsNormal
    GridStyle.OddColor = clWindow
    GridStyle.EvenColor = clWindow
    TitleHeight.PixelCount = 24
    FooterColor = clBtnFace
    ExOptions = [eoENTERlikeTAB, eoKeepSelection, eoBLOBEditor, eoTitleWordWrap, eoFilterAutoApply]
    RegistryKey = 'Software\Scalabium'
    RegistrySection = 'SMDBGrid'
    WidthOfIndicator = 11
    DefaultRowHeight = 17
    ScrollBars = ssHorizontal
    ColCount = 2
    RowCount = 2
  end
  object OraQuery1: TOraQuery
    Session = LVSDatenModul.OraMainSession
    SQL.Strings = (
      'select * '
      'from v_pcd_batchlauf_config')
    ReadOnly = True
    AutoCommit = False
    Options.DynamicReadThreshold = 0
    Left = 72
    Top = 80
  end
  object OraDataSource1: TOraDataSource
    DataSet = OraQuery1
    OnDataChange = OraDataSource1DataChange
    Left = 104
    Top = 80
  end
  object BatchGridPopupMenu: TPopupMenu
    Left = 288
    Top = 96
    object Neu1: TMenuItem
      Caption = 'Neu'
      OnClick = Neu1Click
    end
    object BatchCopyMenuItem: TMenuItem
      Caption = 'Kopieren'
      OnClick = BatchCopyMenuItemClick
    end
    object N1: TMenuItem
      Caption = '-'
    end
    object aktivieren1: TMenuItem
      Caption = 'Aktivieren'
      OnClick = aktivieren1Click
    end
    object deaktivieren1: TMenuItem
      Caption = 'Deaktivieren'
      OnClick = deaktivieren1Click
    end
    object N2: TMenuItem
      Caption = '-'
    end
    object Lschen1: TMenuItem
      Caption = 'L'#246'schen'
      OnClick = Lschen1Click
    end
    object N3: TMenuItem
      Caption = '-'
    end
  end
end
