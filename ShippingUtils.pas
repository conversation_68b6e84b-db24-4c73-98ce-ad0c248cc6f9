﻿unit ShippingUtils;

interface

type
  TShippingInfos = record
    ReturnForm    : String;
    DeliveryForm  : String;
    InvoiceForm   : String;
    ProformaForm  : String;
    CMRForm       : String;
    CN23Form      : String;
    SCCContForm   : String;

    Incoterm      : String;
    VAT           : String;
    EORI          : String;
    CustomNr      : String;

    OptAutoPrintDelivery : Boolean;

    OptPrintDelivery  : Char;
    OptPrintInvoice   : Char;
    OptNVEContent     : Char;
    OptPrintProforma  : Char;
    OptPrintCMR       : Char;
    OptPrintCN23      : Char;
    OptPrintZoll      : Char;
  end;

  procedure InitOrderShippingInfos;

  function GetOrderShippingInfos (const RefAuf : Integer; var ShippingInfo : TShippingInfos) : Integer;

implementation

uses
  Ora, OraSmart, DatenModul;

var
  fUseFirst         : boolean = true;
  fUseMandEORI      : boolean;
  fUseAufConfigCMR  : boolean;
  fUseAufNVEInhalt  : boolean;

procedure InitOrderShippingInfos;
begin
  fUseFirst         := true;
  fUseMandEORI      := false;
  fUseAufConfigCMR  := false;
  fUseAufNVEInhalt  := false;
end;

//******************************************************************************
//* Function Name: GetOrderShippingInfos
//* Author       : Stefan Graf
//* Datum        : 10.08.2025
//******************************************************************************
//* Description  :
//******************************************************************************
//* Return Value :
//******************************************************************************
function GetOrderShippingInfos (const RefAuf : Integer; var ShippingInfo : TShippingInfos) : Integer;
var
  aufcfgsql : String;
  query     : TSmartQuery;
begin
  ShippingInfo.ReturnForm   := 'RETOURENSCHEIN';
  ShippingInfo.DeliveryForm := 'LIEFERSCHEIN';
  ShippingInfo.CMRForm      := 'CMR';
  ShippingInfo.InvoiceForm  := 'RECHNUNG';
  ShippingInfo.ProformaForm := 'PROFORMA_RECHNUNG';
  ShippingInfo.SCCContForm  := 'NVE-INHALT';
  ShippingInfo.CN23Form     := 'ZOLL_CN23';

  ShippingInfo.VAT      := '';
  ShippingInfo.EORI     := '';
  ShippingInfo.CustomNr := '';
  ShippingInfo.Incoterm := '';

  ShippingInfo.OptAutoPrintDelivery := false;

  ShippingInfo.OptNVEContent     := #0;
  ShippingInfo.OptPrintZoll      := #0;
  ShippingInfo.OptPrintDelivery  := #0;
  ShippingInfo.OptPrintInvoice   := #0;
  ShippingInfo.OptPrintProforma  := #0;
  ShippingInfo.OptPrintCMR       := #0;
  ShippingInfo.OptPrintCN23      := #0;

  query := LVSDatenModul.CreateSmartQuery (Nil, 'GetOrderShippingInfos');

  try
    if fUseFirst then begin
      query.SQL.Clear;
      query.SQL.Add ('select * from V_MANDANT where ROWNUM=0');

      query.Open;

      fUseMandEORI := Assigned (query.FindField ('EORI_NUMMER'));

      query.Close;

      query.SQL.Clear;
      query.SQL.Add ('select * from V_AUFTRAG_ART_CONFIG where ROWNUM=0');

      query.Open;

      fUseAufConfigCMR := Assigned (query.FindField ('OPT_PRINT_CMR'));

      query.Close;

      query.SQL.Clear;
      query.SQL.Add ('select * from V_AUFTRAG_ART_PLANUNG where ROWNUM=0');

      query.Open;

      fUseAufNVEInhalt := Assigned (query.FindField ('OPT_PRINT_NVE_INHALT'));

      query.Close;

      fUseFirst := false;
    end;

    aufcfgsql := ',aufcfg.OPT_PRINT_ZOLL_DOC as CFG_OPT_PRINT_ZOLL_DOC, aufcfg.OPT_PROFORMA as CFG_OPT_PROFORMA ,aufcfg.OPT_PRINT_INVOICE as CFG_OPT_PRINT_INVOICE';

    if fUseAufConfigCMR then
      aufcfgsql := aufcfgsql + ',aufcfg.OPT_PRINT_CMR as CFG_OPT_PRINT_CMR'
    else
      aufcfgsql := aufcfgsql + ',null as CFG_OPT_PRINT_CMR';

    if fUseAufNVEInhalt then
      aufcfgsql := aufcfgsql + ',verplan.OPT_PRINT_NVE_INHALT as PLAN_OPT_PRINT_NVE_INHALT'
    else
      aufcfgsql := aufcfgsql + ',null as PLAN_OPT_PRINT_NVE_INHALT';

    if fUseMandEORI then
      aufcfgsql := aufcfgsql + ',m.EORI_NUMMER as MAND_EORI_NUMMER'
    else
      aufcfgsql := aufcfgsql + ',null as MAND_EORI_NUMMER';

    query.SQL.Clear;

    query.SQL.Add ('select'
                  +' vercfg.*'
                  +' ,m.VAT_ID as MAND_VAT_ID'
                  +' ,verplan.OPT_AUTO_PRINT_LS, verplan.OPT_PRINT_LS as PLAN_OPT_PRINT_LS'
                  +' ,ad.INCOTERMS_CODE as AUF_INCOTERM'
                  +' ,av.OPT_PRINT_LS as AUF_OPT_PRINT_LS'
                  +  aufcfgsql
                  +' from'
                  +'   V_AUFTRAG_LS auf'
                  +'   inner join V_AUFTRAG_VERSAND av on (av.REF_AUF_KOPF=auf.REF)'
                  +'   inner join V_AUFTRAG_ADD_INFOS ad on (ad.REF_AUF_KOPF=auf.REF)'
                  +'   inner join V_MANDANT m on ((auf.REF_SUB_MAND is not null and m.REF=auf.REF_SUB_MAND) or (auf.REF_SUB_MAND is null and m.REF=auf.REF_MAND))'
                  +'   left outer join V_AUFTRAG_ART_CONFIG aufcfg on (aufcfg.REF=auf.REF_ART_CONFIG)'
                  +'   left outer join V_AUFTRAG_ART_PLANUNG verplan on (verplan.REF=auf.REF_ART_PLANUNG)'
                  +'   left outer join V_AUFTRAG_ART_VERSAND_CONFIG vercfg on (vercfg.REF=auf.REF_AUF_VERS_CFG)'
                  +'  where auf.REF=:ref_auf');
    query.Params.ParamByName('ref_auf').Value := RefAuf;

    try
      query.Open;

      if Assigned (query.FindField ('RETOUREN_FORMULAR')) and not query.FieldByName ('RETOUREN_FORMULAR').IsNull then
        ShippingInfo.ReturnForm := query.FieldByName ('RETOUREN_FORMULAR').AsString;

      if Assigned (query.FindField ('LIEFERSCHEIN_FORMULAR')) and not query.FieldByName ('LIEFERSCHEIN_FORMULAR').IsNull then
        ShippingInfo.DeliveryForm := query.FieldByName ('LIEFERSCHEIN_FORMULAR').AsString;

      if Assigned (query.FindField ('CMR_FORMULAR')) and not query.FieldByName ('CMR_FORMULAR').IsNull then
        ShippingInfo.CMRForm := query.FieldByName ('CMR_FORMULAR').AsString;

      if Assigned (query.FindField ('CN23_FORMULAR')) and not query.FieldByName ('CN23_FORMULAR').IsNull then
        ShippingInfo.CN23Form := query.FieldByName ('CN23_FORMULAR').AsString;

      if Assigned (query.FindField ('PROFORMA_FORMULAR')) and not query.FieldByName ('PROFORMA_FORMULAR').IsNull then
        ShippingInfo.ProformaForm := query.FieldByName ('PROFORMA_FORMULAR').AsString;

      if Assigned (query.FindField ('INVOICE_FORMULAR')) and not query.FieldByName ('INVOICE_FORMULAR').IsNull then
        ShippingInfo.InvoiceForm := query.FieldByName ('INVOICE_FORMULAR').AsString;

      if Assigned (query.FindField ('OPT_PRINT_LS')) and not query.FieldByName ('OPT_PRINT_LS').IsNull then begin
        ShippingInfo.OptPrintDelivery := query.FieldByName('OPT_PRINT_LS').AsString[1];
        ShippingInfo.OptAutoPrintDelivery := (ShippingInfo.OptPrintDelivery > '0');
      end else if not query.FieldByName ('AUF_OPT_PRINT_LS').IsNull then begin
        ShippingInfo.OptPrintDelivery := query.FieldByName('AUF_OPT_PRINT_LS').AsString[1];
        ShippingInfo.OptAutoPrintDelivery := (ShippingInfo.OptPrintDelivery > '0');
      end else if not query.FieldByName ('PLAN_OPT_PRINT_LS').IsNull then begin
        ShippingInfo.OptPrintDelivery := query.FieldByName('PLAN_OPT_PRINT_LS').AsString[1];
        ShippingInfo.OptAutoPrintDelivery := (query.FieldByName('OPT_AUTO_PRINT_LS').AsString > '0');
      end;

      if Assigned (query.FindField ('INCOTERM')) and not query.FieldByName ('INCOTERM').IsNull then
        ShippingInfo.Incoterm := query.FieldByName('INCOTERM').AsString
      else
        ShippingInfo.Incoterm := query.FieldByName('AUF_INCOTERM').AsString;

      if Assigned (query.FindField ('FISKAL_ADDRESS')) and not query.FieldByName ('FISKAL_ADDRESS').IsNull then begin
        if Assigned (query.FindField ('FISKAL_VAT_ID')) and not query.FieldByName ('FISKAL_VAT_ID').IsNull then
          ShippingInfo.VAT := query.FieldByName('FISKAL_VAT_ID').AsString
        else if Assigned (query.FindField ('VAT_ID')) and not query.FieldByName ('VAT_ID').IsNull then
          ShippingInfo.VAT := query.FieldByName('VAT_ID').AsString
        else
          ShippingInfo.VAT := query.FieldByName('MAND_VAT_ID').AsString;

        if Assigned (query.FindField ('FISKAL_EORI_NUMMER')) and not query.FieldByName ('FISKAL_EORI_NUMMER').IsNull then
          ShippingInfo.VAT := query.FieldByName('FISKAL_EORI_NUMMER').AsString
        else if Assigned (query.FindField ('EORI_NUMMER')) and not query.FieldByName ('EORI_NUMMER').IsNull then
          ShippingInfo.VAT := query.FieldByName('EORI_NUMMER').AsString
        else
          ShippingInfo.VAT := query.FieldByName('MAND_EORI_NUMMER').AsString;

        if Assigned (query.FindField ('FISKAL_CUSTOM_NO')) and not query.FieldByName ('FISKAL_CUSTOM_NO').IsNull then
          ShippingInfo.CustomNr := query.FieldByName('FISKAL_CUSTOM_NO').AsString
        else if Assigned (query.FindField ('CUSTOM_NO')) and not query.FieldByName ('CUSTOM_NO').IsNull then
          ShippingInfo.CustomNr := query.FieldByName('CUSTOM_NO').AsString
      end else begin
        if Assigned (query.FindField ('VAT_ID')) and not query.FieldByName ('VAT_ID').IsNull then
          ShippingInfo.VAT := query.FieldByName('VAT_ID').AsString
        else
          ShippingInfo.VAT := query.FieldByName('MAND_VAT_ID').AsString;

        if Assigned (query.FindField ('EORI_NUMMER')) and not query.FieldByName ('EORI_NUMMER').IsNull then
          ShippingInfo.VAT := query.FieldByName('EORI_NUMMER').AsString
        else
          ShippingInfo.VAT := query.FieldByName('MAND_EORI_NUMMER').AsString;

        if Assigned (query.FindField ('CUSTOM_NO')) and not query.FieldByName ('CUSTOM_NO').IsNull then
          ShippingInfo.CustomNr := query.FieldByName('CUSTOM_NO').AsString
      end;

      if Assigned (query.FindField ('OPT_PRINT_INVOICE')) and not query.FieldByName ('OPT_PRINT_INVOICE').IsNull then
        ShippingInfo.OptPrintInvoice := query.FieldByName('OPT_PRINT_INVOICE').AsString[1]
      else if not query.FieldByName ('CFG_OPT_PRINT_INVOICE').IsNull then
        ShippingInfo.OptPrintInvoice := query.FieldByName('CFG_OPT_PRINT_INVOICE').AsString[1];

      if Assigned (query.FindField ('FISKAL_ADDRESS')) and not query.FieldByName ('FISKAL_ADDRESS').IsNull then
        ShippingInfo.OptPrintZoll := '1'
      else if Assigned (query.FindField ('CFG_OPT_PRINT_ZOLL_DOC')) and not query.FieldByName ('CFG_OPT_PRINT_ZOLL_DOC').IsNull then
        ShippingInfo.OptPrintZoll := query.FieldByName('CFG_OPT_PRINT_ZOLL_DOC').AsString[1];

      if Assigned (query.FindField ('OPT_PROFORMA')) and not query.FieldByName ('OPT_PROFORMA').IsNull then
        ShippingInfo.OptPrintProforma := query.FieldByName('OPT_PROFORMA').AsString[1]
      else if Assigned (query.FindField ('CFG_OPT_PROFORMA')) and not query.FieldByName ('CFG_OPT_PROFORMA').IsNull then
        ShippingInfo.OptPrintProforma := query.FieldByName('CFG_OPT_PROFORMA').AsString[1];

      if Assigned (query.FindField ('OPT_PRINT_CMR')) and not query.FieldByName ('OPT_PRINT_CMR').IsNull then
        ShippingInfo.OptPrintCMR := query.FieldByName('OPT_PRINT_CMR').AsString[1]
      else if Assigned (query.FindField ('CFG_OPT_PRINT_CMR')) and not query.FieldByName ('CFG_OPT_PRINT_CMR').IsNull then
        ShippingInfo.OptPrintCMR := query.FieldByName('CFG_OPT_PRINT_CMR').AsString[1];

      if Assigned (query.FindField ('OPT_CN23')) and not query.FieldByName ('OPT_CN23').IsNull then
        ShippingInfo.OptPrintCN23 := query.FieldByName('OPT_CN23').AsString[1];

      if Assigned (query.FindField ('OPT_PRINT_NVE_INHALT')) and not query.FieldByName ('OPT_PRINT_NVE_INHALT').IsNull then
        ShippingInfo.OptNVEContent := query.FieldByName('OPT_PRINT_NVE_INHALT').AsString[1]
      else if Assigned (query.FindField ('PLAN_OPT_PRINT_NVE_INHALT')) and not query.FieldByName ('PLAN_OPT_PRINT_NVE_INHALT').IsNull then
        ShippingInfo.OptNVEContent := query.FieldByName('PLAN_OPT_PRINT_NVE_INHALT').AsString[1];

      query.Close;
    except
    end;
  finally
    query.Free;
  end;

  Result := 0;
end;

end.
