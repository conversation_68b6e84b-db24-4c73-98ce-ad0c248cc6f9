﻿//*****************************************************************************
//*  Program System    : storelogix-Leistand
//*  Module Name       : SLManager
//*  Author            : <PERSON> / Common Solutions
//*  Date of creation  : 20.07.2004
//*****************************************************************************
// $Archive: /Zimbo/LVS/FrontEnd/SLManager.dpr $
// $Revision: 143 $
// $Modtime: 23.09.09 22:59 $
// $Author: Stefan.graf $
//*****************************************************************************
//*  Description       : Hauptprogramm
//*****************************************************************************
//{$SetPEOptFlags $8000}
program SLManager;

{$R 'storelogix.res' 'storelogix.rc'}

uses
  madLinkDisAsm,
  madExcept,
  madListModules,
  SysUtils,
  Trace,
  Windows,
  Forms,
  Graphics,
  StoreLogixSplashScreen,
  BarCodeScanner,
  Vcl.Themes,
  Vcl.Styles,
  SprachDLG in 'SprachDLG.pas' {ChangeSpracheForm},
  LVSFrontEndMain in 'LVSFrontEndMain.pas' {LVSForm},
  DatenModul in 'DatenModul.pas' {LVSDatenModul: TDataModule},
  BestellVereinnahmung in 'BestellVereinnahmung.pas' {BestellVereinnahmungForm},
  ArtikelDLG in 'ArtikelDLG.pas' {ArtikelForm},
  SprachModul in 'SprachModul.pas' {LVSSprachModul: TDataModule},
  SetupDLG in 'SetupDLG.pas' {SetupForm},
  ConfigModul in 'ConfigModul.pas' {LVSConfigModul: TDataModule},
  StoreLogixAboutUnit in 'StoreLogixAboutUnit.pas' {AboutForm},
  LVSSecurity in 'LVSSecurity.pas' {LVSSecurityModule: TDataModule},
  PrintModul in 'PrintModul.pas' {PrintModule: TDataModule},
  MessageModul in 'MessageModul.pas' {MessageModule: TDataModule},
  MessageExDLG in 'MessageExDLG.pas' {MessageForm},
  ErrorTracking in 'ErrorTracking.pas' {ErrorTrackingModule: TDataModule},
  DBGridUtilModule in 'DBGridUtilModule.pas' {DBGridUtils: TDataModule},
  DrawLPDLG in 'DrawLPDLG.pas' {DrawLPForm},
  FrontendACOModul in 'FrontendACOModul.pas' {FrontendACOModule: TDataModule},
  ACOModul in 'ACOModul.pas' {ACOModule: TDataModule},
  FrontendDatasets in 'FrontendDatasets.pas' {FrontendDataModule: TDataModule},
  KommAbschlussDLG in 'KommAbschlussDLG.pas' {KommAbschlussForm},
  KommGwAbschlussDLG in 'KommGwAbschlussDLG.pas' {KommGwAbschlussForm},
  InvPosRes in 'InvPosRes.pas' {InvPosResForm},
  SetRecordFilter in 'SetRecordFilter.pas' {RecordFilterForm},
  FrontendImageModule in 'FrontendImageModule.pas' {ImageModule: TDataModule},
  PDFPreviewDLG in 'PDFPreviewDLG.pas',
  KeyboardUtils in 'Library\KeyboardUtils.pas',
  TourInfoFrame in 'TourInfoFrame.pas' {TourInfo: TFrame},
  ACLTemplate in 'ACLTemplate.pas' {ACLFrame: TFrame},
  FrontendODACDatasets in 'FrontendODACDatasets.pas' {FrontendODACDataModule: TDataModule},
  MandAdrFRM in 'MandAdrFRM.pas' {MandAdrFrame: TFrame},
  AuftragAdrFRM in 'AuftragAdrFRM.pas' {AuftragAdrFrame: TFrame},
  ShippingUtils in 'ShippingUtils.pas';

{$R *.res}
{$R 'aVersionInfo.res'}

var
  splash  : TSplashForm;

  {$ifdef TRACE}
    trcname : String;
  {$endif}
begin
//  MemChk;

  Application.Initialize;
  SetWindowLong (Application.Handle, GWL_EXSTYLE, GetWindowLong(Application.Handle, GWL_EXSTYLE) or WS_EX_TOOLWINDOW);

  Application.MainFormOnTaskbar := True;
  Application.Title := LoadStr (100);

  splash := TSplashForm.Create (Nil);
  try

    splash.Show;
    splash.Update;

    {$ifdef Trace} TraceString ('LVSConfigModul'); {$endif}
    Application.CreateForm(TLVSConfigModul, LVSConfigModul);
  {$ifdef Trace} TraceString ('LVSSprachModul'); {$endif}
    Application.CreateForm(TLVSSprachModul, LVSSprachModul);
    {$ifdef Trace} TraceString ('LVSForm'); {$endif}
    Application.CreateForm(TLVSForm, LVSForm);
    {$ifdef Trace} TraceString ('TPDFPreviewForm'); {$endif}
    Application.CreateForm(TPDFPreviewForm, PDFPreviewForm);
    {$ifdef Trace} TraceString ('ImageModule'); {$endif}
    Application.CreateForm(TImageModule, ImageModule);
    {$ifdef Trace} TraceString ('RecordFilterForm'); {$endif}
    Application.CreateForm(TRecordFilterForm, RecordFilterForm);
    {$ifdef Trace} TraceString ('InvPosResForm'); {$endif}
    Application.CreateForm(TInvPosResForm, InvPosResForm);

    {$ifdef Numpad}
      {$ifdef Trace} TraceString ('NumericKeypad'); {$endif}
      Application.CreateForm(TNumericKeypad, NumericKeypad);
    {$endif}

    {$ifdef Trace} TraceString ('DBGridUtils'); {$endif}
    Application.CreateForm(TDBGridUtils, DBGridUtils);
    {$ifdef Trace} TraceString ('KommAbschlussForm'); {$endif}
    Application.CreateForm(TKommAbschlussForm, KommAbschlussForm);
    {$ifdef Trace} TraceString ('KommGwAbschlussForm'); {$endif}
    Application.CreateForm(TKommGewichtAbschlussForm, KommGwAbschlussForm);
    {$ifdef Trace} TraceString ('ErrorTrackingModule'); {$endif}
    Application.CreateForm(TErrorTrackingModule, ErrorTrackingModule);
    {$ifdef Trace} TraceString ('LVSDatenModul'); {$endif}
    Application.CreateForm(TLVSDatenModul, LVSDatenModul);
    {$ifdef Trace} TraceString ('FrontendDataModule'); {$endif}
    Application.CreateForm(TFrontendDataModule, FrontendDataModule);
    {$ifdef Trace} TraceString ('FrontendODACDataModule'); {$endif}
    Application.CreateForm(TFrontendODACDataModule, FrontendODACDataModule);
    {$ifdef Trace} TraceString ('BestellVereinnahmungForm'); {$endif}
    Application.CreateForm(TBestellVereinnahmungForm, BestellVereinnahmungForm);
    {$ifdef Trace} TraceString ('ArtikelForm'); {$endif}
    Application.CreateForm(TArtikelForm, ArtikelForm);
    {$ifdef Trace} TraceString ('SetupForm'); {$endif}
    Application.CreateForm(TSetupForm, SetupForm);
    {$ifdef Trace} TraceString ('LVSSecurityModule'); {$endif}
    Application.CreateForm(TLVSSecurityModule, LVSSecurityModule);
    {$ifdef Trace} TraceString ('PrintModule'); {$endif}
    Application.CreateForm(TPrintModule, PrintModule);
    {$ifdef Trace} TraceString ('MessageModule'); {$endif}
    Application.CreateForm(TMessageModule, MessageModule);
    {$ifdef Trace} TraceString ('MessageForm'); {$endif}
    Application.CreateForm(TMessageForm, MessageForm);
    {$ifdef Trace} TraceString ('FrontendACOModule'); {$endif}
    Application.CreateForm(TFrontendACOModule, FrontendACOModule);
  finally
    splash.Free;
  end;

  //Hier wird das projektspezifische ACOModul veröffentlicht
  LVSSecurityModule.ACOModul := FrontendACOModule;

  DBGridUtils.CompTranslate := LVSSprachModul.LVSCompTranslate;

  LVSForm.Caption := 'storelogix Manager';
  LVSForm.Init;

  LVSForm.Show;

  if (LVSForm.OpenDatabase (Nil) = -1) then
    Application.Terminate;

  Application.ProcessMessages;

  Application.Run;
end.
